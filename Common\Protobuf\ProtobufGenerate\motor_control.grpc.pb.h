// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: motor_control.proto
#ifndef GRPC_motor_5fcontrol_2eproto__INCLUDED
#define GRPC_motor_5fcontrol_2eproto__INCLUDED

#include "motor_control.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace motor_control {

//  控床服务
class BedMasterAppService final {
 public:
  static constexpr char const* service_full_name() {
    return "motor_control.BedMasterAppService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    //  开始移床
    virtual ::grpc::Status StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::motor_control::StartMoveStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>> AsyncStartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>>(AsyncStartMoveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>> PrepareAsyncStartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>>(PrepareAsyncStartMoveRaw(context, request, cq));
    }
    //  停止移床
    virtual ::grpc::Status StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> AsyncStopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(AsyncStopMoveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> PrepareAsyncStopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(PrepareAsyncStopMoveRaw(context, request, cq));
    }
    //  获取PostId
    virtual ::grpc::Status GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::motor_control::GetPostIdStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>> AsyncGetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>>(AsyncGetPostIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>> PrepareAsyncGetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>>(PrepareAsyncGetPostIdRaw(context, request, cq));
    }
    //  获取动物床控制权
    virtual ::grpc::Status GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> AsyncGainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(AsyncGainControlRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> PrepareAsyncGainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(PrepareAsyncGainControlRaw(context, request, cq));
    }
    //  释放动物床控制权
    virtual ::grpc::Status ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> AsyncReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(AsyncReleaseControlRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> PrepareAsyncReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(PrepareAsyncReleaseControlRaw(context, request, cq));
    }
    //  心跳检测
    virtual ::grpc::Status HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> AsyncHeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(AsyncHeartBeatCheckRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>> PrepareAsyncHeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>>(PrepareAsyncHeartBeatCheckRaw(context, request, cq));
    }
    //  获取动物床状态
    virtual ::grpc::Status GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::SystemStatusInfoStatus* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>> AsyncGetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>>(AsyncGetSystemStatusInfoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>> PrepareAsyncGetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>>(PrepareAsyncGetSystemStatusInfoRaw(context, request, cq));
    }
    // 获取trigger信息
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> GetTriggerInfo(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(GetTriggerInfoRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> AsyncGetTriggerInfo(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(AsyncGetTriggerInfoRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> PrepareAsyncGetTriggerInfo(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(PrepareAsyncGetTriggerInfoRaw(context, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      //  开始移床
      virtual void StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  停止移床
      virtual void StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  获取PostId
      virtual void GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  获取动物床控制权
      virtual void GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  释放动物床控制权
      virtual void ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  心跳检测
      virtual void HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      //  获取动物床状态
      virtual void GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      // 获取trigger信息
      virtual void GetTriggerInfo(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::motor_control::GetTriggerInfoDescription,::motor_control::TriggerInfoStatus>* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>* AsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::StartMoveStatus>* PrepareAsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* AsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* PrepareAsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>* AsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::GetPostIdStatus>* PrepareAsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* AsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* PrepareAsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* AsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* PrepareAsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* AsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::CommonStatus>* PrepareAsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>* AsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::motor_control::SystemStatusInfoStatus>* PrepareAsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* GetTriggerInfoRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* AsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* PrepareAsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::motor_control::StartMoveStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>> AsyncStartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>>(AsyncStartMoveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>> PrepareAsyncStartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>>(PrepareAsyncStartMoveRaw(context, request, cq));
    }
    ::grpc::Status StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> AsyncStopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(AsyncStopMoveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> PrepareAsyncStopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(PrepareAsyncStopMoveRaw(context, request, cq));
    }
    ::grpc::Status GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::motor_control::GetPostIdStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>> AsyncGetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>>(AsyncGetPostIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>> PrepareAsyncGetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>>(PrepareAsyncGetPostIdRaw(context, request, cq));
    }
    ::grpc::Status GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> AsyncGainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(AsyncGainControlRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> PrepareAsyncGainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(PrepareAsyncGainControlRaw(context, request, cq));
    }
    ::grpc::Status ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> AsyncReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(AsyncReleaseControlRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> PrepareAsyncReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(PrepareAsyncReleaseControlRaw(context, request, cq));
    }
    ::grpc::Status HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::CommonStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> AsyncHeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(AsyncHeartBeatCheckRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>> PrepareAsyncHeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>>(PrepareAsyncHeartBeatCheckRaw(context, request, cq));
    }
    ::grpc::Status GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::motor_control::SystemStatusInfoStatus* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>> AsyncGetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>>(AsyncGetSystemStatusInfoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>> PrepareAsyncGetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>>(PrepareAsyncGetSystemStatusInfoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> GetTriggerInfo(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(GetTriggerInfoRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> AsyncGetTriggerInfo(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(AsyncGetTriggerInfoRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>> PrepareAsyncGetTriggerInfo(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>>(PrepareAsyncGetTriggerInfoRaw(context, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, std::function<void(::grpc::Status)>) override;
      void StartMove(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) override;
      void StopMove(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, std::function<void(::grpc::Status)>) override;
      void GetPostId(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) override;
      void GainControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) override;
      void ReleaseControl(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, std::function<void(::grpc::Status)>) override;
      void HeartBeatCheck(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, std::function<void(::grpc::Status)>) override;
      void GetSystemStatusInfo(::grpc::ClientContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTriggerInfo(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::motor_control::GetTriggerInfoDescription,::motor_control::TriggerInfoStatus>* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>* AsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::StartMoveStatus>* PrepareAsyncStartMoveRaw(::grpc::ClientContext* context, const ::motor_control::StartMoveDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* AsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* PrepareAsyncStopMoveRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>* AsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::GetPostIdStatus>* PrepareAsyncGetPostIdRaw(::grpc::ClientContext* context, const ::motor_control::GetPostIdDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* AsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* PrepareAsyncGainControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* AsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* PrepareAsyncReleaseControlRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* AsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::CommonStatus>* PrepareAsyncHeartBeatCheckRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>* AsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::motor_control::SystemStatusInfoStatus>* PrepareAsyncGetSystemStatusInfoRaw(::grpc::ClientContext* context, const ::motor_control::CommonDescription& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* GetTriggerInfoRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* AsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* PrepareAsyncGetTriggerInfoRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_StartMove_;
    const ::grpc::internal::RpcMethod rpcmethod_StopMove_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPostId_;
    const ::grpc::internal::RpcMethod rpcmethod_GainControl_;
    const ::grpc::internal::RpcMethod rpcmethod_ReleaseControl_;
    const ::grpc::internal::RpcMethod rpcmethod_HeartBeatCheck_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSystemStatusInfo_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTriggerInfo_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    //  开始移床
    virtual ::grpc::Status StartMove(::grpc::ServerContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response);
    //  停止移床
    virtual ::grpc::Status StopMove(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response);
    //  获取PostId
    virtual ::grpc::Status GetPostId(::grpc::ServerContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response);
    //  获取动物床控制权
    virtual ::grpc::Status GainControl(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response);
    //  释放动物床控制权
    virtual ::grpc::Status ReleaseControl(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response);
    //  心跳检测
    virtual ::grpc::Status HeartBeatCheck(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response);
    //  获取动物床状态
    virtual ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response);
    // 获取trigger信息
    virtual ::grpc::Status GetTriggerInfo(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* stream);
  };
  template <class BaseClass>
  class WithAsyncMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartMove() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartMove(::grpc::ServerContext* context, ::motor_control::StartMoveDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::StartMoveStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StopMove() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopMove(::grpc::ServerContext* context, ::motor_control::CommonDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::CommonStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPostId() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPostId(::grpc::ServerContext* context, ::motor_control::GetPostIdDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::GetPostIdStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GainControl() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGainControl(::grpc::ServerContext* context, ::motor_control::CommonDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::CommonStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReleaseControl(::grpc::ServerContext* context, ::motor_control::CommonDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::CommonStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHeartBeatCheck(::grpc::ServerContext* context, ::motor_control::CommonDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::CommonStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSystemStatusInfo(::grpc::ServerContext* context, ::motor_control::CommonDescription* request, ::grpc::ServerAsyncResponseWriter< ::motor_control::SystemStatusInfoStatus>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTriggerInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTriggerInfo() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetTriggerInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTriggerInfo(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTriggerInfo(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(7, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_StartMove<WithAsyncMethod_StopMove<WithAsyncMethod_GetPostId<WithAsyncMethod_GainControl<WithAsyncMethod_ReleaseControl<WithAsyncMethod_HeartBeatCheck<WithAsyncMethod_GetSystemStatusInfo<WithAsyncMethod_GetTriggerInfo<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartMove() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::StartMoveDescription* request, ::motor_control::StartMoveStatus* response) { return this->StartMove(context, request, response); }));}
    void SetMessageAllocatorFor_StartMove(
        ::grpc::MessageAllocator< ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartMove(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StopMove() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) { return this->StopMove(context, request, response); }));}
    void SetMessageAllocatorFor_StopMove(
        ::grpc::MessageAllocator< ::motor_control::CommonDescription, ::motor_control::CommonStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopMove(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPostId() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::GetPostIdDescription* request, ::motor_control::GetPostIdStatus* response) { return this->GetPostId(context, request, response); }));}
    void SetMessageAllocatorFor_GetPostId(
        ::grpc::MessageAllocator< ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPostId(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GainControl() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) { return this->GainControl(context, request, response); }));}
    void SetMessageAllocatorFor_GainControl(
        ::grpc::MessageAllocator< ::motor_control::CommonDescription, ::motor_control::CommonStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GainControl(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) { return this->ReleaseControl(context, request, response); }));}
    void SetMessageAllocatorFor_ReleaseControl(
        ::grpc::MessageAllocator< ::motor_control::CommonDescription, ::motor_control::CommonStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReleaseControl(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::CommonStatus* response) { return this->HeartBeatCheck(context, request, response); }));}
    void SetMessageAllocatorFor_HeartBeatCheck(
        ::grpc::MessageAllocator< ::motor_control::CommonDescription, ::motor_control::CommonStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::CommonStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* HeartBeatCheck(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::motor_control::CommonDescription* request, ::motor_control::SystemStatusInfoStatus* response) { return this->GetSystemStatusInfo(context, request, response); }));}
    void SetMessageAllocatorFor_GetSystemStatusInfo(
        ::grpc::MessageAllocator< ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSystemStatusInfo(
      ::grpc::CallbackServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTriggerInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTriggerInfo() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackBidiHandler< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->GetTriggerInfo(context); }));
    }
    ~WithCallbackMethod_GetTriggerInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTriggerInfo(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::motor_control::GetTriggerInfoDescription, ::motor_control::TriggerInfoStatus>* GetTriggerInfo(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  typedef WithCallbackMethod_StartMove<WithCallbackMethod_StopMove<WithCallbackMethod_GetPostId<WithCallbackMethod_GainControl<WithCallbackMethod_ReleaseControl<WithCallbackMethod_HeartBeatCheck<WithCallbackMethod_GetSystemStatusInfo<WithCallbackMethod_GetTriggerInfo<Service > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartMove() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StopMove() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPostId() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GainControl() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTriggerInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTriggerInfo() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetTriggerInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTriggerInfo(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartMove() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartMove(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StopMove() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopMove(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPostId() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPostId(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GainControl() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGainControl(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReleaseControl(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHeartBeatCheck(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSystemStatusInfo(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTriggerInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTriggerInfo() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetTriggerInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTriggerInfo(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTriggerInfo(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(7, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartMove() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartMove(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartMove(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StopMove() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StopMove(context, request, response); }));
    }
    ~WithRawCallbackMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopMove(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPostId() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPostId(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPostId(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GainControl() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GainControl(context, request, response); }));
    }
    ~WithRawCallbackMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GainControl(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReleaseControl(context, request, response); }));
    }
    ~WithRawCallbackMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReleaseControl(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->HeartBeatCheck(context, request, response); }));
    }
    ~WithRawCallbackMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* HeartBeatCheck(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSystemStatusInfo(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSystemStatusInfo(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTriggerInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTriggerInfo() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackBidiHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context) { return this->GetTriggerInfo(context); }));
    }
    ~WithRawCallbackMethod_GetTriggerInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTriggerInfo(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::motor_control::TriggerInfoStatus, ::motor_control::GetTriggerInfoDescription>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* GetTriggerInfo(
      ::grpc::CallbackServerContext* /*context*/)
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartMove() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::StartMoveDescription, ::motor_control::StartMoveStatus>* streamer) {
                       return this->StreamedStartMove(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartMove(::grpc::ServerContext* /*context*/, const ::motor_control::StartMoveDescription* /*request*/, ::motor_control::StartMoveStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartMove(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::StartMoveDescription,::motor_control::StartMoveStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StopMove : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StopMove() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::CommonDescription, ::motor_control::CommonStatus>* streamer) {
                       return this->StreamedStopMove(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StopMove() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StopMove(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStopMove(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::CommonDescription,::motor_control::CommonStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPostId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPostId() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::GetPostIdDescription, ::motor_control::GetPostIdStatus>* streamer) {
                       return this->StreamedGetPostId(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPostId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPostId(::grpc::ServerContext* /*context*/, const ::motor_control::GetPostIdDescription* /*request*/, ::motor_control::GetPostIdStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPostId(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::GetPostIdDescription,::motor_control::GetPostIdStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GainControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GainControl() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::CommonDescription, ::motor_control::CommonStatus>* streamer) {
                       return this->StreamedGainControl(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GainControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GainControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGainControl(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::CommonDescription,::motor_control::CommonStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReleaseControl : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReleaseControl() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::CommonDescription, ::motor_control::CommonStatus>* streamer) {
                       return this->StreamedReleaseControl(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ReleaseControl() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReleaseControl(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReleaseControl(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::CommonDescription,::motor_control::CommonStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_HeartBeatCheck : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_HeartBeatCheck() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::CommonDescription, ::motor_control::CommonStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::CommonDescription, ::motor_control::CommonStatus>* streamer) {
                       return this->StreamedHeartBeatCheck(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_HeartBeatCheck() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status HeartBeatCheck(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::CommonStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedHeartBeatCheck(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::CommonDescription,::motor_control::CommonStatus>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSystemStatusInfo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSystemStatusInfo() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::motor_control::CommonDescription, ::motor_control::SystemStatusInfoStatus>* streamer) {
                       return this->StreamedGetSystemStatusInfo(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSystemStatusInfo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSystemStatusInfo(::grpc::ServerContext* /*context*/, const ::motor_control::CommonDescription* /*request*/, ::motor_control::SystemStatusInfoStatus* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSystemStatusInfo(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::motor_control::CommonDescription,::motor_control::SystemStatusInfoStatus>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_StartMove<WithStreamedUnaryMethod_StopMove<WithStreamedUnaryMethod_GetPostId<WithStreamedUnaryMethod_GainControl<WithStreamedUnaryMethod_ReleaseControl<WithStreamedUnaryMethod_HeartBeatCheck<WithStreamedUnaryMethod_GetSystemStatusInfo<Service > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_StartMove<WithStreamedUnaryMethod_StopMove<WithStreamedUnaryMethod_GetPostId<WithStreamedUnaryMethod_GainControl<WithStreamedUnaryMethod_ReleaseControl<WithStreamedUnaryMethod_HeartBeatCheck<WithStreamedUnaryMethod_GetSystemStatusInfo<Service > > > > > > > StreamedService;
};

}  // namespace motor_control


#endif  // GRPC_motor_5fcontrol_2eproto__INCLUDED
