#include "ErrorCodeMapper.hpp"
#include "SyslogManager.hpp"

// 错误码映射表定义
const std::map<ErrorCodeMapper::BusinessError, uint64_t> ErrorCodeMapper::errorCodeMap_ = {
    {BusinessError::SUCCESS, 0},
    {BusinessError::PERMISSION_DENIED, 1},
    {BusinessError::INVALID_PARAMETER, 2},
    {BusinessError::HARDWARE_ERROR, 3},
    {BusinessError::TIMEOUT, 4},
    {BusinessError::SERVO_MANAGER_NULL, 5},
    {BusinessError::INVALID_BED_TYPE, 6},
    {BusinessError::GAIN_CONTROL_FAILED, 7},
    {BusinessError::RELEASE_CONTROL_FAILED, 8},
    {BusinessError::START_MOVE_FAILED, 9},
    {BusinessError::STOP_OPERATION_FAILED, 10},
    {BusinessError::MOTION_INTERFERENCE, 11},
    {BusinessError::GENERAL_ERROR, 99}
};

// 字符串错误码映射表（兼容现有代码）
const std::map<std::string, uint64_t> ErrorCodeMapper::stringErrorCodeMap_ = {
    {"SUCCESS", 0},
    {"PERMISSION_DENIED", 1},
    {"INVALID_PARAMETER", 2},
    {"HARDWARE_ERROR", 3},
    {"TIMEOUT", 4},
    {"SERVO_MANAGER_NULL", 5},
    {"INVALID_BED_TYPE", 6},
    {"GAIN_CONTROL_FAILED", 7},
    {"RELEASE_CONTROL_FAILED", 8},
    {"START_MOVE_FAILED", 9},
    {"STOP_OPERATION_FAILED", 10},
    {"MOTION_INTERFERENCE", 11},
    {"GENERAL_ERROR", 99}
};

// 错误描述映射表
const std::map<ErrorCodeMapper::BusinessError, std::string> ErrorCodeMapper::errorDescriptionMap_ = {
    {BusinessError::SUCCESS, "操作成功"},
    {BusinessError::PERMISSION_DENIED, "权限被拒绝"},
    {BusinessError::INVALID_PARAMETER, "无效参数"},
    {BusinessError::HARDWARE_ERROR, "硬件错误"},
    {BusinessError::TIMEOUT, "操作超时"},
    {BusinessError::SERVO_MANAGER_NULL, "伺服管理器未初始化"},
    {BusinessError::INVALID_BED_TYPE, "无效的床类型"},
    {BusinessError::GAIN_CONTROL_FAILED, "获取控制权失败"},
    {BusinessError::RELEASE_CONTROL_FAILED, "释放控制权失败"},
    {BusinessError::START_MOVE_FAILED, "开始移动失败"},
    {BusinessError::STOP_OPERATION_FAILED, "停止操作失败"},
    {BusinessError::MOTION_INTERFERENCE, "运动干涉"},
    {BusinessError::GENERAL_ERROR, "通用错误"}
};

uint64_t ErrorCodeMapper::getErrorCode(BusinessError error) {
    auto it = errorCodeMap_.find(error);
    if (it != errorCodeMap_.end()) {
        return it->second;
    }
    
    LOG_WARNING_MSG("ErrorCodeMapper", "未知的业务错误类型: %d", static_cast<int>(error));
    return errorCodeMap_.at(BusinessError::GENERAL_ERROR);
}

uint64_t ErrorCodeMapper::getErrorCode(const std::string& errorType) {
    auto it = stringErrorCodeMap_.find(errorType);
    if (it != stringErrorCodeMap_.end()) {
        return it->second;
    }
    
    LOG_WARNING_MSG("ErrorCodeMapper", "未知的错误类型字符串: %s", errorType.c_str());
    return stringErrorCodeMap_.at("GENERAL_ERROR");
}

std::string ErrorCodeMapper::getErrorDescription(BusinessError error) {
    auto it = errorDescriptionMap_.find(error);
    if (it != errorDescriptionMap_.end()) {
        return it->second;
    }
    
    return "未知错误";
}

ErrorCodeMapper::BusinessError ErrorCodeMapper::checkOperationResult(bool result, const std::string& operation) {
    if (result) {
        return BusinessError::SUCCESS;
    }
    
    // 根据操作类型返回相应的错误
    if (operation == "StartMove") {
        return BusinessError::START_MOVE_FAILED;
    } else if (operation == "StopMove") {
        return BusinessError::STOP_OPERATION_FAILED;
    } else if (operation == "GainControl") {
        return BusinessError::GAIN_CONTROL_FAILED;
    } else if (operation == "ReleaseControl") {
        return BusinessError::RELEASE_CONTROL_FAILED;
    } else {
        return BusinessError::GENERAL_ERROR;
    }
}
