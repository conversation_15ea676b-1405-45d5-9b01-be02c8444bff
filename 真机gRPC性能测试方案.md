# 真机环境gRPC性能测试方案

## 项目概述

基于现有的 `Tests/GrpcPerformanceTest/GrpcCommunicationPerformanceTest.cpp` 测试文件和 `Docs/Performance/Grpc接口性能测试场景.md` 的测试需求，将Mock环境下的本地测试适配到真实的ARM板卡网络环境。

## 1. 现有代码分析

### 1.1 当前测试架构

**基础架构**：
- 基于Google Test和Google Mock框架
- 本地回环通信（localhost:50051）
- 使用Mock对象模拟CANopenMaster和ServoControlManager

**现有测试场景**：
1. **单接口延迟测试**：测试各gRPC接口响应时间
2. **长时间稳定性测试**：监控长期运行性能
3. **流式接口测试**：测试GetTriggerInfo双向流通信性能

### 1.2 需要适配的关键部分

**网络配置适配**：
```cpp
// 现有配置
server_address_ = "localhost:50051";

// 需要改为
server_address_ = "*************:50051";  // ARM板卡IP地址
```

**Mock对象替换**：
```cpp
// 现有Mock配置
mock_canopen_master_ = std::make_unique<MockCANopenMaster>("vcan0");
mock_servo_manager_ = std::make_unique<MockServoControlManager>(mock_canopen_master_.get());

// 真机环境配置
real_canopen_master_ = std::make_unique<CANopenMaster>("can0");
real_servo_manager_ = std::make_unique<ServoControlManager>(real_canopen_master_.get());
```

## 2. 真机测试环境配置

### 2.1 ARM板卡基本要求

**硬件配置**：
- CPU: ARM Cortex-A53/A72 四核 @ 1.2GHz+
- 内存: 1GB+ DDR3/DDR4 RAM
- 网络: 100Mbps+ 以太网接口
- 操作系统: Linux内核4.14+

**网络配置**：
```bash
# ARM板卡网络设置
IP地址: *************/24
网关: ***********

# 防火墙配置
sudo ufw allow 50051/tcp  # gRPC服务端口
```

### 2.2 网络环境要求

**网络质量指标**：
- 带宽: ≥ 100Mbps
- 延迟: RTT < 10ms（局域网）
- 丢包率: < 0.1%

**网络拓扑**：
```
上位机(客户端) ←→ 交换机 ←→ ARM板卡(服务器)
***********0        1Gbps      *************
```

## 3. 核心接口测试场景

基于 `Docs/Performance/Grpc接口性能测试场景.md` 的要求，重点测试以下场景：

### 3.1 StartMove接口测试

**测试场景**：
- 多客户端（最多5个）以10ms频率持续获取系统状态过程中，测试StartMove接口延迟
- 重点测试：ServoControlManager检测到运动完成至客户端接收到返回信息的耗时

**测试配置**：
```cpp
// StartMove测试参数
const int concurrent_clients = 5;           // 并发客户端数
const int status_query_interval_ms = 10;    // 状态查询间隔
const int test_duration_seconds = 300;      // 测试持续时间
```

**并发场景**：
- StartMove针对单客户端多个请求，非多客户端多个请求

### 3.2 GetTriggerInfo接口测试

**测试场景**：
- 多客户端（最多5个）以10ms频率持续获取系统状态过程中，测试GetTriggerInfo接口延迟
- 重点测试：ServoControlManager检测到运动完成至客户端接收到返回信息的耗时

**测试配置**：
```cpp
// GetTriggerInfo流式测试参数
const int concurrent_streams = 5;           // 并发流数量
const int trigger_check_interval_ms = 10;   // 触发检查间隔
const int stream_duration_seconds = 600;    // 流持续时间
```

### 3.3 GetSystemStatusInfo接口测试

**测试场景**：
- 测试并发性能
- 基于StartMove、GetSystemStatusInfo触发的场景下测试该接口延迟

**测试配置**：
```cpp
// GetSystemStatusInfo并发测试参数
const std::vector<int> concurrent_clients = {1, 2, 4, 5, 8};  // 并发客户端数
const int requests_per_client = 100;        // 每客户端请求数
const int request_interval_ms = 50;         // 请求间隔
```

**并发场景**：
- 支持多客户端多请求

### 3.4 其他接口测试

**测试场景**：
- 基础接口延迟测试
- 包括：GainControl、ReleaseControl、GetPostId、HeartBeatCheck

**测试配置**：
```cpp
// 其他接口测试参数
const std::vector<std::string> other_interfaces = {
    "GainControl", "ReleaseControl", "GetPostId", "HeartBeatCheck"
};
const int basic_request_count = 50;         // 基础请求数量
const int single_client_test = 1;           // 单客户端测试
```

## 4. 性能基准调整

### 4.1 延迟基准对比

| 接口名称 | Mock环境P95 | 真机环境P95 | 调整说明 |
|---------|-------------|-------------|----------|
| StartMove | 50ms | 100ms | +网络延迟+ARM处理延迟 |
| GetTriggerInfo | 50ms | 100ms | +流建立+网络延迟 |
| GetSystemStatusInfo | 100ms | 150ms | +复杂查询+网络延迟 |
| HeartBeatCheck | 20ms | 50ms | +网络RTT |
| 其他接口 | 50ms | 100ms | +网络延迟 |

### 4.2 错误率调整

- Mock环境：< 0.1%
- 真机环境：< 0.5%（考虑网络不稳定因素）

### 4.3 并发能力调整

- Mock环境：支持16+并发客户端
- 真机环境：支持5-8并发客户端（基于ARM性能限制）

## 5. 代码适配方案

### 5.1 配置文件方式

创建 `real_machine_config.json`：
```json
{
  "test_environment": "REAL_MACHINE",
  "server_address": "*************",
  "server_port": 50051,
  "connection_timeout_ms": 10000,
  "request_timeout_ms": 15000,
  "max_concurrent_clients": 5,
  "performance_targets": {
    "StartMove_p95_ms": 100,
    "GetTriggerInfo_p95_ms": 100,
    "GetSystemStatusInfo_p95_ms": 150,
    "HeartBeatCheck_p95_ms": 50,
    "max_error_rate": 0.005
  }
}
```

### 5.2 测试类适配

```cpp
class RealMachinePerformanceTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 加载配置
        loadTestConfig("real_machine_config.json");
        
        // 根据配置选择测试环境
        if (config_.test_environment == "REAL_MACHINE") {
            setupRealMachineEnvironment();
        } else {
            setupMockEnvironment();
        }
    }
    
private:
    void setupRealMachineEnvironment() {
        // 连接真机gRPC服务
        server_address_ = config_.server_address + ":" + std::to_string(config_.server_port);
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        stub_ = motor_control::BedMasterAppService::NewStub(channel);
        
        // 验证连接
        if (!checkServerConnection()) {
            FAIL() << "无法连接到ARM板卡gRPC服务: " << server_address_;
        }
    }
    
    bool checkServerConnection() {
        // 简单的连通性检查
        grpc::ClientContext context;
        context.set_deadline(std::chrono::system_clock::now() + 
                           std::chrono::milliseconds(config_.connection_timeout_ms));
        
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        request.set_contextuid("connection-test");
        
        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);
        return status.ok();
    }
};
```

### 5.3 网络延迟测量

```cpp
class NetworkLatencyMeasurer {
public:
    double measureRTT(const std::string& server_ip) {
        // 使用ping命令测量RTT
        std::string cmd = "ping -c 1 -W 1000 " + server_ip + " | grep 'time=' | awk -F'time=' '{print $2}' | awk '{print $1}'";
        
        FILE* pipe = popen(cmd.c_str(), "r");
        if (!pipe) return -1.0;
        
        char buffer[128];
        std::string result;
        while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
            result += buffer;
        }
        pclose(pipe);
        
        try {
            return std::stod(result);
        } catch (...) {
            return -1.0;
        }
    }
};
```

## 6. ARM服务器部署

### 6.1 交叉编译

```bash
# 设置交叉编译环境
export CC=aarch64-linux-gnu-gcc
export CXX=aarch64-linux-gnu-g++

# 编译ARM版本
mkdir build_arm && cd build_arm
cmake -DCMAKE_CROSSCOMPILING=ON ..
make -j4
```

### 6.2 服务器部署脚本

```bash
#!/bin/bash
# deploy_arm_server.sh

ARM_IP="*************"
ARM_USER="ubuntu"

echo "部署ARM服务器..."

# 传输可执行文件
scp ./build_arm/CANopenMasterServerArm ${ARM_USER}@${ARM_IP}:/opt/bedmaster/
scp ./Configuration/*.json ${ARM_USER}@${ARM_IP}:/opt/bedmaster/config/

# 启动服务
ssh ${ARM_USER}@${ARM_IP} << 'EOF'
cd /opt/bedmaster
sudo ./CANopenMasterServerArm &
echo "gRPC服务已启动"
EOF
```

## 7. 测试执行流程

### 7.1 环境检查

```bash
# 1. 网络连通性检查
ping -c 5 *************

# 2. gRPC服务检查
telnet ************* 50051

# 3. 编译测试程序
cd Tests/GrpcPerformanceTest
mkdir build && cd build
cmake .. && make
```

### 7.2 执行测试

```bash
# 运行真机性能测试
./real_machine_perf_test --config=../real_machine_config.json

# 或分别执行各测试场景
./real_machine_perf_test --gtest_filter="*StartMove*"
./real_machine_perf_test --gtest_filter="*GetTriggerInfo*"
./real_machine_perf_test --gtest_filter="*GetSystemStatusInfo*"
```

### 7.3 结果分析

测试完成后会生成：
- `performance_results.csv`：详细性能数据
- `performance_summary.txt`：测试摘要报告
- `network_latency_log.txt`：网络延迟记录

## 8. 真机环境特殊考虑

### 8.1 网络不稳定处理

```cpp
// 重试机制
bool callInterfaceWithRetry(const std::string& interface_name, int max_retries = 3) {
    for (int i = 0; i < max_retries; ++i) {
        if (callInterface(interface_name, stub_.get())) {
            return true;
        }
        
        // 网络错误时等待重试
        std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (i + 1)));
    }
    return false;
}
```

### 8.2 ARM性能限制

- 降低并发客户端数量（从16降到5-8）
- 增加请求间隔时间（避免CPU过载）
- 适当调整性能目标（考虑ARM处理能力）

### 8.3 测试数据收集

```cpp
// 简化的性能数据收集
struct SimplePerformanceStats {
    double avg_latency_ms;
    double p95_latency_ms;
    double error_rate;
    int total_requests;
    double network_rtt_ms;  // 网络延迟
};
```

---

**总结**：
本方案专注于将现有Mock测试适配到真机环境，重点关注核心接口的性能测试方法。通过简单的配置文件和代码适配，实现从Mock环境到真机环境的平滑过渡，满足实际的性能测试需求。
