#ifndef SYSLOG_MANAGER_HPP
#define SYSLOG_MANAGER_HPP

#include <string>
#include <mutex>
#include <syslog.h>
#include <cstdarg>
#include <chrono>
#include <thread>
#include <iomanip>
#include <sstream>

// 设置日志
extern void setLogger(char *programName);

/**
 * @brief 统一的系统日志管理器
 * 
 * 该类提供线程安全的syslog日志记录功能，自动包含时间戳、线程ID和类名信息
 * 使用单例模式确保全局唯一性
 */
class SyslogManager {
public:
    /**
     * @brief 获取SyslogManager单例实例
     * @return SyslogManager引用
     */
    static SyslogManager& getInstance();

    /**
     * @brief 记录日志消息
     * @param priority syslog优先级 (LOG_DEBUG, LOG_INFO, LOG_WARNING, LOG_ERR等)
     * @param className 调用类的名称
     * @param format 格式化字符串
     * @param ... 可变参数
     */
    void logMessage(int priority, const std::string& className, const char* format, ...);

    /**
     * @brief 初始化syslog系统
     * @param ident 程序标识符
     * @param option syslog选项
     * @param facility syslog设施
     * @param enableConsoleOutput 是否同时输出到控制台
     */
    void initialize(const char* ident = "BedMaster", int option = LOG_PID | LOG_CONS, int facility = LOG_USER, int level = LOG_INFO);

    /**
     * @brief 设置是否同时输出到控制台
     * @param enable true表示同时输出到控制台，false表示仅输出到syslog
     */
    void setConsoleOutput(bool enable);

    /**
     * @brief 关闭syslog系统
     */
    void shutdown();

private:
    SyslogManager();
    ~SyslogManager();
    
    // 禁用拷贝构造和赋值操作
    SyslogManager(const SyslogManager&) = delete;
    SyslogManager& operator=(const SyslogManager&) = delete;

    /**
     * @brief 获取当前时间戳字符串（微秒精度）
     * @return 格式化的时间戳字符串
     */
    std::string getCurrentTimestamp();

    /**
     * @brief 获取当前线程ID字符串
     * @return 线程ID字符串
     */
    std::string getCurrentThreadId();

    /**
     * @brief 格式化日志消息
     * @param priority 日志优先级
     * @param className 类名
     * @param format 格式化字符串
     * @param args 参数列表
     * @return 格式化后的完整日志消息
     */
    std::string formatLogMessage(int priority, const std::string& className, const char* format, va_list args);

private:
    std::mutex logMutex_;           // 日志记录互斥锁
    bool initialized_;              // 初始化标志
    bool consoleOutput_;            // 是否同时输出到控制台
    static const size_t MAX_LOG_SIZE = 2048;  // 最大日志消息长度
};

// 便于使用的日志宏定义
#define LOG_DEBUG_MSG(className, format, ...) \
    SyslogManager::getInstance().logMessage(LOG_DEBUG, className, format, ##__VA_ARGS__)

#define LOG_INFO_MSG(className, format, ...) \
    SyslogManager::getInstance().logMessage(LOG_INFO, className, format, ##__VA_ARGS__)

#define LOG_WARNING_MSG(className, format, ...) \
    SyslogManager::getInstance().logMessage(LOG_WARNING, className, format, ##__VA_ARGS__)

#define LOG_ERROR_MSG(className, format, ...) \
    SyslogManager::getInstance().logMessage(LOG_ERR, className, format, ##__VA_ARGS__)

#define LOG_CRITICAL_MSG(className, format, ...) \
    SyslogManager::getInstance().logMessage(LOG_CRIT, className, format, ##__VA_ARGS__)

// 带自动类名推导的宏（需要在类内部使用）
#define LOG_DEBUG_AUTO(format, ...) \
    LOG_DEBUG_MSG(typeid(*this).name(), format, ##__VA_ARGS__)

#define LOG_INFO_AUTO(format, ...) \
    LOG_INFO_MSG(typeid(*this).name(), format, ##__VA_ARGS__)

#define LOG_WARNING_AUTO(format, ...) \
    LOG_WARNING_MSG(typeid(*this).name(), format, ##__VA_ARGS__)

#define LOG_ERROR_AUTO(format, ...) \
    LOG_ERROR_MSG(typeid(*this).name(), format, ##__VA_ARGS__)

#define LOG_CRITICAL_AUTO(format, ...) \
    LOG_CRITICAL_MSG(typeid(*this).name(), format, ##__VA_ARGS__)

#endif // SYSLOG_MANAGER_HPP
