// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: motor_control.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_motor_5fcontrol_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_motor_5fcontrol_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019004 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_motor_5fcontrol_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_motor_5fcontrol_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[36]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_motor_5fcontrol_2eproto;
namespace motor_control {
class BedStatus;
struct BedStatusDefaultTypeInternal;
extern BedStatusDefaultTypeInternal _BedStatus_default_instance_;
class ClearFaultRequest;
struct ClearFaultRequestDefaultTypeInternal;
extern ClearFaultRequestDefaultTypeInternal _ClearFaultRequest_default_instance_;
class ClearFaultResponse;
struct ClearFaultResponseDefaultTypeInternal;
extern ClearFaultResponseDefaultTypeInternal _ClearFaultResponse_default_instance_;
class CommonDescription;
struct CommonDescriptionDefaultTypeInternal;
extern CommonDescriptionDefaultTypeInternal _CommonDescription_default_instance_;
class CommonStatus;
struct CommonStatusDefaultTypeInternal;
extern CommonStatusDefaultTypeInternal _CommonStatus_default_instance_;
class ConfigMessage;
struct ConfigMessageDefaultTypeInternal;
extern ConfigMessageDefaultTypeInternal _ConfigMessage_default_instance_;
class DeviceConfig;
struct DeviceConfigDefaultTypeInternal;
extern DeviceConfigDefaultTypeInternal _DeviceConfig_default_instance_;
class EmergencyStopRequest;
struct EmergencyStopRequestDefaultTypeInternal;
extern EmergencyStopRequestDefaultTypeInternal _EmergencyStopRequest_default_instance_;
class EmergencyStopResponse;
struct EmergencyStopResponseDefaultTypeInternal;
extern EmergencyStopResponseDefaultTypeInternal _EmergencyStopResponse_default_instance_;
class GetAllNodesRequest;
struct GetAllNodesRequestDefaultTypeInternal;
extern GetAllNodesRequestDefaultTypeInternal _GetAllNodesRequest_default_instance_;
class GetAllNodesResponse;
struct GetAllNodesResponseDefaultTypeInternal;
extern GetAllNodesResponseDefaultTypeInternal _GetAllNodesResponse_default_instance_;
class GetPostIdDescription;
struct GetPostIdDescriptionDefaultTypeInternal;
extern GetPostIdDescriptionDefaultTypeInternal _GetPostIdDescription_default_instance_;
class GetPostIdStatus;
struct GetPostIdStatusDefaultTypeInternal;
extern GetPostIdStatusDefaultTypeInternal _GetPostIdStatus_default_instance_;
class GetTriggerInfoDescription;
struct GetTriggerInfoDescriptionDefaultTypeInternal;
extern GetTriggerInfoDescriptionDefaultTypeInternal _GetTriggerInfoDescription_default_instance_;
class HomingRequest;
struct HomingRequestDefaultTypeInternal;
extern HomingRequestDefaultTypeInternal _HomingRequest_default_instance_;
class HomingResponse;
struct HomingResponseDefaultTypeInternal;
extern HomingResponseDefaultTypeInternal _HomingResponse_default_instance_;
class MotionCapability;
struct MotionCapabilityDefaultTypeInternal;
extern MotionCapabilityDefaultTypeInternal _MotionCapability_default_instance_;
class MotionInfo;
struct MotionInfoDefaultTypeInternal;
extern MotionInfoDefaultTypeInternal _MotionInfo_default_instance_;
class MotorControlRequest;
struct MotorControlRequestDefaultTypeInternal;
extern MotorControlRequestDefaultTypeInternal _MotorControlRequest_default_instance_;
class MotorControlResponse;
struct MotorControlResponseDefaultTypeInternal;
extern MotorControlResponseDefaultTypeInternal _MotorControlResponse_default_instance_;
class MotorStatusRequest;
struct MotorStatusRequestDefaultTypeInternal;
extern MotorStatusRequestDefaultTypeInternal _MotorStatusRequest_default_instance_;
class MotorStatusResponse;
struct MotorStatusResponseDefaultTypeInternal;
extern MotorStatusResponseDefaultTypeInternal _MotorStatusResponse_default_instance_;
class NodeInfo;
struct NodeInfoDefaultTypeInternal;
extern NodeInfoDefaultTypeInternal _NodeInfo_default_instance_;
class OperationModeRequest;
struct OperationModeRequestDefaultTypeInternal;
extern OperationModeRequestDefaultTypeInternal _OperationModeRequest_default_instance_;
class OperationModeResponse;
struct OperationModeResponseDefaultTypeInternal;
extern OperationModeResponseDefaultTypeInternal _OperationModeResponse_default_instance_;
class PositionControlRequest;
struct PositionControlRequestDefaultTypeInternal;
extern PositionControlRequestDefaultTypeInternal _PositionControlRequest_default_instance_;
class PositionControlResponse;
struct PositionControlResponseDefaultTypeInternal;
extern PositionControlResponseDefaultTypeInternal _PositionControlResponse_default_instance_;
class PostIdInfo;
struct PostIdInfoDefaultTypeInternal;
extern PostIdInfoDefaultTypeInternal _PostIdInfo_default_instance_;
class StartMoveDescription;
struct StartMoveDescriptionDefaultTypeInternal;
extern StartMoveDescriptionDefaultTypeInternal _StartMoveDescription_default_instance_;
class StartMoveStatus;
struct StartMoveStatusDefaultTypeInternal;
extern StartMoveStatusDefaultTypeInternal _StartMoveStatus_default_instance_;
class SystemStatusInfoStatus;
struct SystemStatusInfoStatusDefaultTypeInternal;
extern SystemStatusInfoStatusDefaultTypeInternal _SystemStatusInfoStatus_default_instance_;
class TorqueControlRequest;
struct TorqueControlRequestDefaultTypeInternal;
extern TorqueControlRequestDefaultTypeInternal _TorqueControlRequest_default_instance_;
class TorqueControlResponse;
struct TorqueControlResponseDefaultTypeInternal;
extern TorqueControlResponseDefaultTypeInternal _TorqueControlResponse_default_instance_;
class TriggerInfoStatus;
struct TriggerInfoStatusDefaultTypeInternal;
extern TriggerInfoStatusDefaultTypeInternal _TriggerInfoStatus_default_instance_;
class VelocityControlRequest;
struct VelocityControlRequestDefaultTypeInternal;
extern VelocityControlRequestDefaultTypeInternal _VelocityControlRequest_default_instance_;
class VelocityControlResponse;
struct VelocityControlResponseDefaultTypeInternal;
extern VelocityControlResponseDefaultTypeInternal _VelocityControlResponse_default_instance_;
}  // namespace motor_control
PROTOBUF_NAMESPACE_OPEN
template<> ::motor_control::BedStatus* Arena::CreateMaybeMessage<::motor_control::BedStatus>(Arena*);
template<> ::motor_control::ClearFaultRequest* Arena::CreateMaybeMessage<::motor_control::ClearFaultRequest>(Arena*);
template<> ::motor_control::ClearFaultResponse* Arena::CreateMaybeMessage<::motor_control::ClearFaultResponse>(Arena*);
template<> ::motor_control::CommonDescription* Arena::CreateMaybeMessage<::motor_control::CommonDescription>(Arena*);
template<> ::motor_control::CommonStatus* Arena::CreateMaybeMessage<::motor_control::CommonStatus>(Arena*);
template<> ::motor_control::ConfigMessage* Arena::CreateMaybeMessage<::motor_control::ConfigMessage>(Arena*);
template<> ::motor_control::DeviceConfig* Arena::CreateMaybeMessage<::motor_control::DeviceConfig>(Arena*);
template<> ::motor_control::EmergencyStopRequest* Arena::CreateMaybeMessage<::motor_control::EmergencyStopRequest>(Arena*);
template<> ::motor_control::EmergencyStopResponse* Arena::CreateMaybeMessage<::motor_control::EmergencyStopResponse>(Arena*);
template<> ::motor_control::GetAllNodesRequest* Arena::CreateMaybeMessage<::motor_control::GetAllNodesRequest>(Arena*);
template<> ::motor_control::GetAllNodesResponse* Arena::CreateMaybeMessage<::motor_control::GetAllNodesResponse>(Arena*);
template<> ::motor_control::GetPostIdDescription* Arena::CreateMaybeMessage<::motor_control::GetPostIdDescription>(Arena*);
template<> ::motor_control::GetPostIdStatus* Arena::CreateMaybeMessage<::motor_control::GetPostIdStatus>(Arena*);
template<> ::motor_control::GetTriggerInfoDescription* Arena::CreateMaybeMessage<::motor_control::GetTriggerInfoDescription>(Arena*);
template<> ::motor_control::HomingRequest* Arena::CreateMaybeMessage<::motor_control::HomingRequest>(Arena*);
template<> ::motor_control::HomingResponse* Arena::CreateMaybeMessage<::motor_control::HomingResponse>(Arena*);
template<> ::motor_control::MotionCapability* Arena::CreateMaybeMessage<::motor_control::MotionCapability>(Arena*);
template<> ::motor_control::MotionInfo* Arena::CreateMaybeMessage<::motor_control::MotionInfo>(Arena*);
template<> ::motor_control::MotorControlRequest* Arena::CreateMaybeMessage<::motor_control::MotorControlRequest>(Arena*);
template<> ::motor_control::MotorControlResponse* Arena::CreateMaybeMessage<::motor_control::MotorControlResponse>(Arena*);
template<> ::motor_control::MotorStatusRequest* Arena::CreateMaybeMessage<::motor_control::MotorStatusRequest>(Arena*);
template<> ::motor_control::MotorStatusResponse* Arena::CreateMaybeMessage<::motor_control::MotorStatusResponse>(Arena*);
template<> ::motor_control::NodeInfo* Arena::CreateMaybeMessage<::motor_control::NodeInfo>(Arena*);
template<> ::motor_control::OperationModeRequest* Arena::CreateMaybeMessage<::motor_control::OperationModeRequest>(Arena*);
template<> ::motor_control::OperationModeResponse* Arena::CreateMaybeMessage<::motor_control::OperationModeResponse>(Arena*);
template<> ::motor_control::PositionControlRequest* Arena::CreateMaybeMessage<::motor_control::PositionControlRequest>(Arena*);
template<> ::motor_control::PositionControlResponse* Arena::CreateMaybeMessage<::motor_control::PositionControlResponse>(Arena*);
template<> ::motor_control::PostIdInfo* Arena::CreateMaybeMessage<::motor_control::PostIdInfo>(Arena*);
template<> ::motor_control::StartMoveDescription* Arena::CreateMaybeMessage<::motor_control::StartMoveDescription>(Arena*);
template<> ::motor_control::StartMoveStatus* Arena::CreateMaybeMessage<::motor_control::StartMoveStatus>(Arena*);
template<> ::motor_control::SystemStatusInfoStatus* Arena::CreateMaybeMessage<::motor_control::SystemStatusInfoStatus>(Arena*);
template<> ::motor_control::TorqueControlRequest* Arena::CreateMaybeMessage<::motor_control::TorqueControlRequest>(Arena*);
template<> ::motor_control::TorqueControlResponse* Arena::CreateMaybeMessage<::motor_control::TorqueControlResponse>(Arena*);
template<> ::motor_control::TriggerInfoStatus* Arena::CreateMaybeMessage<::motor_control::TriggerInfoStatus>(Arena*);
template<> ::motor_control::VelocityControlRequest* Arena::CreateMaybeMessage<::motor_control::VelocityControlRequest>(Arena*);
template<> ::motor_control::VelocityControlResponse* Arena::CreateMaybeMessage<::motor_control::VelocityControlResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace motor_control {

enum BedType : int {
  Primary = 0,
  Secondary = 1,
  Both = 2,
  BedType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  BedType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool BedType_IsValid(int value);
constexpr BedType BedType_MIN = Primary;
constexpr BedType BedType_MAX = Both;
constexpr int BedType_ARRAYSIZE = BedType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* BedType_descriptor();
template<typename T>
inline const std::string& BedType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, BedType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function BedType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    BedType_descriptor(), enum_t_value);
}
inline bool BedType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, BedType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<BedType>(
    BedType_descriptor(), name, value);
}
enum MotionMode : int {
  PositionMode = 0,
  VelocityMode = 1,
  HomingMode = 2,
  MotionMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  MotionMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool MotionMode_IsValid(int value);
constexpr MotionMode MotionMode_MIN = PositionMode;
constexpr MotionMode MotionMode_MAX = HomingMode;
constexpr int MotionMode_ARRAYSIZE = MotionMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MotionMode_descriptor();
template<typename T>
inline const std::string& MotionMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MotionMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MotionMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MotionMode_descriptor(), enum_t_value);
}
inline bool MotionMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MotionMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MotionMode>(
    MotionMode_descriptor(), name, value);
}
enum MotionStatus : int {
  Ready = 0,
  Moving = 1,
  Estop = 2,
  Error = 3,
  MotionStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  MotionStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool MotionStatus_IsValid(int value);
constexpr MotionStatus MotionStatus_MIN = Ready;
constexpr MotionStatus MotionStatus_MAX = Error;
constexpr int MotionStatus_ARRAYSIZE = MotionStatus_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MotionStatus_descriptor();
template<typename T>
inline const std::string& MotionStatus_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MotionStatus>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MotionStatus_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MotionStatus_descriptor(), enum_t_value);
}
inline bool MotionStatus_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MotionStatus* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MotionStatus>(
    MotionStatus_descriptor(), name, value);
}
enum HostType : int {
  CT = 0,
  PET = 1,
  SPECT = 2,
  NONE = 3,
  HostType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  HostType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool HostType_IsValid(int value);
constexpr HostType HostType_MIN = CT;
constexpr HostType HostType_MAX = NONE;
constexpr int HostType_ARRAYSIZE = HostType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HostType_descriptor();
template<typename T>
inline const std::string& HostType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HostType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HostType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HostType_descriptor(), enum_t_value);
}
inline bool HostType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HostType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HostType>(
    HostType_descriptor(), name, value);
}
enum OperationMode : int {
  PROFILE_POSITION = 0,
  VELOCITY = 1,
  PROFILE_VELOCITY = 2,
  PROFILE_TORQUE = 3,
  HOMING = 6,
  INTERPOLATED_POSITION = 7,
  CYCLIC_SYNC_POSITION = 8,
  CYCLIC_SYNC_VELOCITY = 9,
  CYCLIC_SYNC_TORQUE = 10,
  OperationMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  OperationMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool OperationMode_IsValid(int value);
constexpr OperationMode OperationMode_MIN = PROFILE_POSITION;
constexpr OperationMode OperationMode_MAX = CYCLIC_SYNC_TORQUE;
constexpr int OperationMode_ARRAYSIZE = OperationMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* OperationMode_descriptor();
template<typename T>
inline const std::string& OperationMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, OperationMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function OperationMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    OperationMode_descriptor(), enum_t_value);
}
inline bool OperationMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, OperationMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<OperationMode>(
    OperationMode_descriptor(), name, value);
}
enum DriveState : int {
  NOT_READY_TO_SWITCH_ON = 0,
  SWITCH_ON_DISABLED = 1,
  READY_TO_SWITCH_ON = 2,
  SWITCHED_ON = 3,
  OPERATION_ENABLED = 4,
  QUICK_STOP_ACTIVE = 5,
  FAULT_REACTION_ACTIVE = 6,
  FAULT = 7,
  DriveState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DriveState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DriveState_IsValid(int value);
constexpr DriveState DriveState_MIN = NOT_READY_TO_SWITCH_ON;
constexpr DriveState DriveState_MAX = FAULT;
constexpr int DriveState_ARRAYSIZE = DriveState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DriveState_descriptor();
template<typename T>
inline const std::string& DriveState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DriveState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DriveState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DriveState_descriptor(), enum_t_value);
}
inline bool DriveState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DriveState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DriveState>(
    DriveState_descriptor(), name, value);
}
// ===================================================================

class StartMoveDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.StartMoveDescription) */ {
 public:
  inline StartMoveDescription() : StartMoveDescription(nullptr) {}
  ~StartMoveDescription() override;
  explicit constexpr StartMoveDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartMoveDescription(const StartMoveDescription& from);
  StartMoveDescription(StartMoveDescription&& from) noexcept
    : StartMoveDescription() {
    *this = ::std::move(from);
  }

  inline StartMoveDescription& operator=(const StartMoveDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartMoveDescription& operator=(StartMoveDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartMoveDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartMoveDescription* internal_default_instance() {
    return reinterpret_cast<const StartMoveDescription*>(
               &_StartMoveDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StartMoveDescription& a, StartMoveDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(StartMoveDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartMoveDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartMoveDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartMoveDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartMoveDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartMoveDescription& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartMoveDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.StartMoveDescription";
  }
  protected:
  explicit StartMoveDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kTargetMotionInfoFieldNumber = 4,
    kModeFieldNumber = 2,
    kBedTypeFieldNumber = 3,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // .motor_control.MotionInfo targetMotionInfo = 4;
  bool has_targetmotioninfo() const;
  private:
  bool _internal_has_targetmotioninfo() const;
  public:
  void clear_targetmotioninfo();
  const ::motor_control::MotionInfo& targetmotioninfo() const;
  PROTOBUF_NODISCARD ::motor_control::MotionInfo* release_targetmotioninfo();
  ::motor_control::MotionInfo* mutable_targetmotioninfo();
  void set_allocated_targetmotioninfo(::motor_control::MotionInfo* targetmotioninfo);
  private:
  const ::motor_control::MotionInfo& _internal_targetmotioninfo() const;
  ::motor_control::MotionInfo* _internal_mutable_targetmotioninfo();
  public:
  void unsafe_arena_set_allocated_targetmotioninfo(
      ::motor_control::MotionInfo* targetmotioninfo);
  ::motor_control::MotionInfo* unsafe_arena_release_targetmotioninfo();

  // .motor_control.MotionMode mode = 2;
  void clear_mode();
  ::motor_control::MotionMode mode() const;
  void set_mode(::motor_control::MotionMode value);
  private:
  ::motor_control::MotionMode _internal_mode() const;
  void _internal_set_mode(::motor_control::MotionMode value);
  public:

  // .motor_control.BedType bedType = 3;
  void clear_bedtype();
  ::motor_control::BedType bedtype() const;
  void set_bedtype(::motor_control::BedType value);
  private:
  ::motor_control::BedType _internal_bedtype() const;
  void _internal_set_bedtype(::motor_control::BedType value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.StartMoveDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  ::motor_control::MotionInfo* targetmotioninfo_;
  int mode_;
  int bedtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class StartMoveStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.StartMoveStatus) */ {
 public:
  inline StartMoveStatus() : StartMoveStatus(nullptr) {}
  ~StartMoveStatus() override;
  explicit constexpr StartMoveStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartMoveStatus(const StartMoveStatus& from);
  StartMoveStatus(StartMoveStatus&& from) noexcept
    : StartMoveStatus() {
    *this = ::std::move(from);
  }

  inline StartMoveStatus& operator=(const StartMoveStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartMoveStatus& operator=(StartMoveStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartMoveStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartMoveStatus* internal_default_instance() {
    return reinterpret_cast<const StartMoveStatus*>(
               &_StartMoveStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(StartMoveStatus& a, StartMoveStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(StartMoveStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartMoveStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartMoveStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartMoveStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartMoveStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartMoveStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartMoveStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.StartMoveStatus";
  }
  protected:
  explicit StartMoveStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kCurrentMotionInfoFieldNumber = 3,
    kErrorCodeFieldNumber = 2,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // .motor_control.MotionInfo currentMotionInfo = 3;
  bool has_currentmotioninfo() const;
  private:
  bool _internal_has_currentmotioninfo() const;
  public:
  void clear_currentmotioninfo();
  const ::motor_control::MotionInfo& currentmotioninfo() const;
  PROTOBUF_NODISCARD ::motor_control::MotionInfo* release_currentmotioninfo();
  ::motor_control::MotionInfo* mutable_currentmotioninfo();
  void set_allocated_currentmotioninfo(::motor_control::MotionInfo* currentmotioninfo);
  private:
  const ::motor_control::MotionInfo& _internal_currentmotioninfo() const;
  ::motor_control::MotionInfo* _internal_mutable_currentmotioninfo();
  public:
  void unsafe_arena_set_allocated_currentmotioninfo(
      ::motor_control::MotionInfo* currentmotioninfo);
  ::motor_control::MotionInfo* unsafe_arena_release_currentmotioninfo();

  // uint64 errorCode = 2;
  void clear_errorcode();
  uint64_t errorcode() const;
  void set_errorcode(uint64_t value);
  private:
  uint64_t _internal_errorcode() const;
  void _internal_set_errorcode(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.StartMoveStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  ::motor_control::MotionInfo* currentmotioninfo_;
  uint64_t errorcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class CommonDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.CommonDescription) */ {
 public:
  inline CommonDescription() : CommonDescription(nullptr) {}
  ~CommonDescription() override;
  explicit constexpr CommonDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CommonDescription(const CommonDescription& from);
  CommonDescription(CommonDescription&& from) noexcept
    : CommonDescription() {
    *this = ::std::move(from);
  }

  inline CommonDescription& operator=(const CommonDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline CommonDescription& operator=(CommonDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CommonDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const CommonDescription* internal_default_instance() {
    return reinterpret_cast<const CommonDescription*>(
               &_CommonDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CommonDescription& a, CommonDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(CommonDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CommonDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CommonDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CommonDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CommonDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CommonDescription& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommonDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.CommonDescription";
  }
  protected:
  explicit CommonDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // @@protoc_insertion_point(class_scope:motor_control.CommonDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class CommonStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.CommonStatus) */ {
 public:
  inline CommonStatus() : CommonStatus(nullptr) {}
  ~CommonStatus() override;
  explicit constexpr CommonStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CommonStatus(const CommonStatus& from);
  CommonStatus(CommonStatus&& from) noexcept
    : CommonStatus() {
    *this = ::std::move(from);
  }

  inline CommonStatus& operator=(const CommonStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline CommonStatus& operator=(CommonStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CommonStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const CommonStatus* internal_default_instance() {
    return reinterpret_cast<const CommonStatus*>(
               &_CommonStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CommonStatus& a, CommonStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(CommonStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CommonStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CommonStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CommonStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CommonStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CommonStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CommonStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.CommonStatus";
  }
  protected:
  explicit CommonStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kErrorCodeFieldNumber = 2,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // uint64 errorCode = 2;
  void clear_errorcode();
  uint64_t errorcode() const;
  void set_errorcode(uint64_t value);
  private:
  uint64_t _internal_errorcode() const;
  void _internal_set_errorcode(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.CommonStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  uint64_t errorcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetPostIdDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.GetPostIdDescription) */ {
 public:
  inline GetPostIdDescription() : GetPostIdDescription(nullptr) {}
  ~GetPostIdDescription() override;
  explicit constexpr GetPostIdDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPostIdDescription(const GetPostIdDescription& from);
  GetPostIdDescription(GetPostIdDescription&& from) noexcept
    : GetPostIdDescription() {
    *this = ::std::move(from);
  }

  inline GetPostIdDescription& operator=(const GetPostIdDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPostIdDescription& operator=(GetPostIdDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPostIdDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPostIdDescription* internal_default_instance() {
    return reinterpret_cast<const GetPostIdDescription*>(
               &_GetPostIdDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetPostIdDescription& a, GetPostIdDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPostIdDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPostIdDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPostIdDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPostIdDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPostIdDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPostIdDescription& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPostIdDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.GetPostIdDescription";
  }
  protected:
  explicit GetPostIdDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kBedTypeFieldNumber = 2,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // .motor_control.BedType bedType = 2;
  void clear_bedtype();
  ::motor_control::BedType bedtype() const;
  void set_bedtype(::motor_control::BedType value);
  private:
  ::motor_control::BedType _internal_bedtype() const;
  void _internal_set_bedtype(::motor_control::BedType value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.GetPostIdDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  int bedtype_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetPostIdStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.GetPostIdStatus) */ {
 public:
  inline GetPostIdStatus() : GetPostIdStatus(nullptr) {}
  ~GetPostIdStatus() override;
  explicit constexpr GetPostIdStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPostIdStatus(const GetPostIdStatus& from);
  GetPostIdStatus(GetPostIdStatus&& from) noexcept
    : GetPostIdStatus() {
    *this = ::std::move(from);
  }

  inline GetPostIdStatus& operator=(const GetPostIdStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPostIdStatus& operator=(GetPostIdStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPostIdStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPostIdStatus* internal_default_instance() {
    return reinterpret_cast<const GetPostIdStatus*>(
               &_GetPostIdStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetPostIdStatus& a, GetPostIdStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPostIdStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPostIdStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPostIdStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPostIdStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPostIdStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPostIdStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPostIdStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.GetPostIdStatus";
  }
  protected:
  explicit GetPostIdStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kPostIdFieldNumber = 3,
    kErrorCodeFieldNumber = 2,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // .motor_control.PostIdInfo postId = 3;
  bool has_postid() const;
  private:
  bool _internal_has_postid() const;
  public:
  void clear_postid();
  const ::motor_control::PostIdInfo& postid() const;
  PROTOBUF_NODISCARD ::motor_control::PostIdInfo* release_postid();
  ::motor_control::PostIdInfo* mutable_postid();
  void set_allocated_postid(::motor_control::PostIdInfo* postid);
  private:
  const ::motor_control::PostIdInfo& _internal_postid() const;
  ::motor_control::PostIdInfo* _internal_mutable_postid();
  public:
  void unsafe_arena_set_allocated_postid(
      ::motor_control::PostIdInfo* postid);
  ::motor_control::PostIdInfo* unsafe_arena_release_postid();

  // uint64 errorCode = 2;
  void clear_errorcode();
  uint64_t errorcode() const;
  void set_errorcode(uint64_t value);
  private:
  uint64_t _internal_errorcode() const;
  void _internal_set_errorcode(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.GetPostIdStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  ::motor_control::PostIdInfo* postid_;
  uint64_t errorcode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class SystemStatusInfoStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.SystemStatusInfoStatus) */ {
 public:
  inline SystemStatusInfoStatus() : SystemStatusInfoStatus(nullptr) {}
  ~SystemStatusInfoStatus() override;
  explicit constexpr SystemStatusInfoStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SystemStatusInfoStatus(const SystemStatusInfoStatus& from);
  SystemStatusInfoStatus(SystemStatusInfoStatus&& from) noexcept
    : SystemStatusInfoStatus() {
    *this = ::std::move(from);
  }

  inline SystemStatusInfoStatus& operator=(const SystemStatusInfoStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline SystemStatusInfoStatus& operator=(SystemStatusInfoStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SystemStatusInfoStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const SystemStatusInfoStatus* internal_default_instance() {
    return reinterpret_cast<const SystemStatusInfoStatus*>(
               &_SystemStatusInfoStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SystemStatusInfoStatus& a, SystemStatusInfoStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(SystemStatusInfoStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SystemStatusInfoStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SystemStatusInfoStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SystemStatusInfoStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SystemStatusInfoStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SystemStatusInfoStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SystemStatusInfoStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.SystemStatusInfoStatus";
  }
  protected:
  explicit SystemStatusInfoStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSoftwareVersionFieldNumber = 1,
    kTimeStampFieldNumber = 2,
    kFirstBedStatusFieldNumber = 3,
    kSecondaryBedStatusFieldNumber = 4,
    kOwnershipFieldNumber = 5,
  };
  // string softwareVersion = 1;
  void clear_softwareversion();
  const std::string& softwareversion() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_softwareversion(ArgT0&& arg0, ArgT... args);
  std::string* mutable_softwareversion();
  PROTOBUF_NODISCARD std::string* release_softwareversion();
  void set_allocated_softwareversion(std::string* softwareversion);
  private:
  const std::string& _internal_softwareversion() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_softwareversion(const std::string& value);
  std::string* _internal_mutable_softwareversion();
  public:

  // string timeStamp = 2;
  void clear_timestamp();
  const std::string& timestamp() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_timestamp(ArgT0&& arg0, ArgT... args);
  std::string* mutable_timestamp();
  PROTOBUF_NODISCARD std::string* release_timestamp();
  void set_allocated_timestamp(std::string* timestamp);
  private:
  const std::string& _internal_timestamp() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_timestamp(const std::string& value);
  std::string* _internal_mutable_timestamp();
  public:

  // .motor_control.BedStatus firstBedStatus = 3;
  bool has_firstbedstatus() const;
  private:
  bool _internal_has_firstbedstatus() const;
  public:
  void clear_firstbedstatus();
  const ::motor_control::BedStatus& firstbedstatus() const;
  PROTOBUF_NODISCARD ::motor_control::BedStatus* release_firstbedstatus();
  ::motor_control::BedStatus* mutable_firstbedstatus();
  void set_allocated_firstbedstatus(::motor_control::BedStatus* firstbedstatus);
  private:
  const ::motor_control::BedStatus& _internal_firstbedstatus() const;
  ::motor_control::BedStatus* _internal_mutable_firstbedstatus();
  public:
  void unsafe_arena_set_allocated_firstbedstatus(
      ::motor_control::BedStatus* firstbedstatus);
  ::motor_control::BedStatus* unsafe_arena_release_firstbedstatus();

  // .motor_control.BedStatus secondaryBedStatus = 4;
  bool has_secondarybedstatus() const;
  private:
  bool _internal_has_secondarybedstatus() const;
  public:
  void clear_secondarybedstatus();
  const ::motor_control::BedStatus& secondarybedstatus() const;
  PROTOBUF_NODISCARD ::motor_control::BedStatus* release_secondarybedstatus();
  ::motor_control::BedStatus* mutable_secondarybedstatus();
  void set_allocated_secondarybedstatus(::motor_control::BedStatus* secondarybedstatus);
  private:
  const ::motor_control::BedStatus& _internal_secondarybedstatus() const;
  ::motor_control::BedStatus* _internal_mutable_secondarybedstatus();
  public:
  void unsafe_arena_set_allocated_secondarybedstatus(
      ::motor_control::BedStatus* secondarybedstatus);
  ::motor_control::BedStatus* unsafe_arena_release_secondarybedstatus();

  // .motor_control.HostType ownership = 5;
  void clear_ownership();
  ::motor_control::HostType ownership() const;
  void set_ownership(::motor_control::HostType value);
  private:
  ::motor_control::HostType _internal_ownership() const;
  void _internal_set_ownership(::motor_control::HostType value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.SystemStatusInfoStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr softwareversion_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr timestamp_;
  ::motor_control::BedStatus* firstbedstatus_;
  ::motor_control::BedStatus* secondarybedstatus_;
  int ownership_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetTriggerInfoDescription final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.GetTriggerInfoDescription) */ {
 public:
  inline GetTriggerInfoDescription() : GetTriggerInfoDescription(nullptr) {}
  ~GetTriggerInfoDescription() override;
  explicit constexpr GetTriggerInfoDescription(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetTriggerInfoDescription(const GetTriggerInfoDescription& from);
  GetTriggerInfoDescription(GetTriggerInfoDescription&& from) noexcept
    : GetTriggerInfoDescription() {
    *this = ::std::move(from);
  }

  inline GetTriggerInfoDescription& operator=(const GetTriggerInfoDescription& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetTriggerInfoDescription& operator=(GetTriggerInfoDescription&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetTriggerInfoDescription& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetTriggerInfoDescription* internal_default_instance() {
    return reinterpret_cast<const GetTriggerInfoDescription*>(
               &_GetTriggerInfoDescription_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GetTriggerInfoDescription& a, GetTriggerInfoDescription& b) {
    a.Swap(&b);
  }
  inline void Swap(GetTriggerInfoDescription* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetTriggerInfoDescription* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetTriggerInfoDescription* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetTriggerInfoDescription>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetTriggerInfoDescription& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetTriggerInfoDescription& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetTriggerInfoDescription* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.GetTriggerInfoDescription";
  }
  protected:
  explicit GetTriggerInfoDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // @@protoc_insertion_point(class_scope:motor_control.GetTriggerInfoDescription)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class TriggerInfoStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.TriggerInfoStatus) */ {
 public:
  inline TriggerInfoStatus() : TriggerInfoStatus(nullptr) {}
  ~TriggerInfoStatus() override;
  explicit constexpr TriggerInfoStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TriggerInfoStatus(const TriggerInfoStatus& from);
  TriggerInfoStatus(TriggerInfoStatus&& from) noexcept
    : TriggerInfoStatus() {
    *this = ::std::move(from);
  }

  inline TriggerInfoStatus& operator=(const TriggerInfoStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline TriggerInfoStatus& operator=(TriggerInfoStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TriggerInfoStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const TriggerInfoStatus* internal_default_instance() {
    return reinterpret_cast<const TriggerInfoStatus*>(
               &_TriggerInfoStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TriggerInfoStatus& a, TriggerInfoStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(TriggerInfoStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TriggerInfoStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TriggerInfoStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TriggerInfoStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TriggerInfoStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TriggerInfoStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TriggerInfoStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.TriggerInfoStatus";
  }
  protected:
  explicit TriggerInfoStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContextUIDFieldNumber = 1,
    kErrorCodeFieldNumber = 2,
    kTriggerTimestampFieldNumber = 4,
    kTriggerPositionFieldNumber = 3,
    kExposureTimeFieldNumber = 5,
  };
  // string contextUID = 1;
  void clear_contextuid();
  const std::string& contextuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_contextuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_contextuid();
  PROTOBUF_NODISCARD std::string* release_contextuid();
  void set_allocated_contextuid(std::string* contextuid);
  private:
  const std::string& _internal_contextuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_contextuid(const std::string& value);
  std::string* _internal_mutable_contextuid();
  public:

  // uint64 errorCode = 2;
  void clear_errorcode();
  uint64_t errorcode() const;
  void set_errorcode(uint64_t value);
  private:
  uint64_t _internal_errorcode() const;
  void _internal_set_errorcode(uint64_t value);
  public:

  // uint64 triggerTimestamp = 4;
  void clear_triggertimestamp();
  uint64_t triggertimestamp() const;
  void set_triggertimestamp(uint64_t value);
  private:
  uint64_t _internal_triggertimestamp() const;
  void _internal_set_triggertimestamp(uint64_t value);
  public:

  // float triggerPosition = 3;
  void clear_triggerposition();
  float triggerposition() const;
  void set_triggerposition(float value);
  private:
  float _internal_triggerposition() const;
  void _internal_set_triggerposition(float value);
  public:

  // uint32 exposureTime = 5;
  void clear_exposuretime();
  uint32_t exposuretime() const;
  void set_exposuretime(uint32_t value);
  private:
  uint32_t _internal_exposuretime() const;
  void _internal_set_exposuretime(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.TriggerInfoStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr contextuid_;
  uint64_t errorcode_;
  uint64_t triggertimestamp_;
  float triggerposition_;
  uint32_t exposuretime_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotionInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotionInfo) */ {
 public:
  inline MotionInfo() : MotionInfo(nullptr) {}
  ~MotionInfo() override;
  explicit constexpr MotionInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotionInfo(const MotionInfo& from);
  MotionInfo(MotionInfo&& from) noexcept
    : MotionInfo() {
    *this = ::std::move(from);
  }

  inline MotionInfo& operator=(const MotionInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotionInfo& operator=(MotionInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotionInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotionInfo* internal_default_instance() {
    return reinterpret_cast<const MotionInfo*>(
               &_MotionInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(MotionInfo& a, MotionInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(MotionInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotionInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotionInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotionInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotionInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotionInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotionInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotionInfo";
  }
  protected:
  explicit MotionInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPostionFieldNumber = 1,
    kVelocityFieldNumber = 2,
  };
  // float postion = 1;
  void clear_postion();
  float postion() const;
  void set_postion(float value);
  private:
  float _internal_postion() const;
  void _internal_set_postion(float value);
  public:

  // float velocity = 2;
  void clear_velocity();
  float velocity() const;
  void set_velocity(float value);
  private:
  float _internal_velocity() const;
  void _internal_set_velocity(float value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotionInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float postion_;
  float velocity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotionCapability final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotionCapability) */ {
 public:
  inline MotionCapability() : MotionCapability(nullptr) {}
  ~MotionCapability() override;
  explicit constexpr MotionCapability(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotionCapability(const MotionCapability& from);
  MotionCapability(MotionCapability&& from) noexcept
    : MotionCapability() {
    *this = ::std::move(from);
  }

  inline MotionCapability& operator=(const MotionCapability& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotionCapability& operator=(MotionCapability&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotionCapability& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotionCapability* internal_default_instance() {
    return reinterpret_cast<const MotionCapability*>(
               &_MotionCapability_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(MotionCapability& a, MotionCapability& b) {
    a.Swap(&b);
  }
  inline void Swap(MotionCapability* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotionCapability* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotionCapability* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotionCapability>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotionCapability& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotionCapability& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotionCapability* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotionCapability";
  }
  protected:
  explicit MotionCapability(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPositionMinFieldNumber = 1,
    kPositionMaxFieldNumber = 2,
    kVelocityMinFieldNumber = 3,
    kVelocityMaxFieldNumber = 4,
    kAccelerationMaxFieldNumber = 5,
    kDecelerationMaxFieldNumber = 6,
  };
  // float positionMin = 1;
  void clear_positionmin();
  float positionmin() const;
  void set_positionmin(float value);
  private:
  float _internal_positionmin() const;
  void _internal_set_positionmin(float value);
  public:

  // float positionMax = 2;
  void clear_positionmax();
  float positionmax() const;
  void set_positionmax(float value);
  private:
  float _internal_positionmax() const;
  void _internal_set_positionmax(float value);
  public:

  // float velocityMin = 3;
  void clear_velocitymin();
  float velocitymin() const;
  void set_velocitymin(float value);
  private:
  float _internal_velocitymin() const;
  void _internal_set_velocitymin(float value);
  public:

  // float velocityMax = 4;
  void clear_velocitymax();
  float velocitymax() const;
  void set_velocitymax(float value);
  private:
  float _internal_velocitymax() const;
  void _internal_set_velocitymax(float value);
  public:

  // float accelerationMax = 5;
  void clear_accelerationmax();
  float accelerationmax() const;
  void set_accelerationmax(float value);
  private:
  float _internal_accelerationmax() const;
  void _internal_set_accelerationmax(float value);
  public:

  // float decelerationMax = 6;
  void clear_decelerationmax();
  float decelerationmax() const;
  void set_decelerationmax(float value);
  private:
  float _internal_decelerationmax() const;
  void _internal_set_decelerationmax(float value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotionCapability)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float positionmin_;
  float positionmax_;
  float velocitymin_;
  float velocitymax_;
  float accelerationmax_;
  float decelerationmax_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class BedStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.BedStatus) */ {
 public:
  inline BedStatus() : BedStatus(nullptr) {}
  ~BedStatus() override;
  explicit constexpr BedStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BedStatus(const BedStatus& from);
  BedStatus(BedStatus&& from) noexcept
    : BedStatus() {
    *this = ::std::move(from);
  }

  inline BedStatus& operator=(const BedStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline BedStatus& operator=(BedStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BedStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const BedStatus* internal_default_instance() {
    return reinterpret_cast<const BedStatus*>(
               &_BedStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(BedStatus& a, BedStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(BedStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BedStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BedStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BedStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BedStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BedStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BedStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.BedStatus";
  }
  protected:
  explicit BedStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMotionInfoFieldNumber = 1,
    kMotionCapabilityFieldNumber = 3,
    kMotionStatusFieldNumber = 2,
  };
  // .motor_control.MotionInfo motionInfo = 1;
  bool has_motioninfo() const;
  private:
  bool _internal_has_motioninfo() const;
  public:
  void clear_motioninfo();
  const ::motor_control::MotionInfo& motioninfo() const;
  PROTOBUF_NODISCARD ::motor_control::MotionInfo* release_motioninfo();
  ::motor_control::MotionInfo* mutable_motioninfo();
  void set_allocated_motioninfo(::motor_control::MotionInfo* motioninfo);
  private:
  const ::motor_control::MotionInfo& _internal_motioninfo() const;
  ::motor_control::MotionInfo* _internal_mutable_motioninfo();
  public:
  void unsafe_arena_set_allocated_motioninfo(
      ::motor_control::MotionInfo* motioninfo);
  ::motor_control::MotionInfo* unsafe_arena_release_motioninfo();

  // .motor_control.MotionCapability motionCapability = 3;
  bool has_motioncapability() const;
  private:
  bool _internal_has_motioncapability() const;
  public:
  void clear_motioncapability();
  const ::motor_control::MotionCapability& motioncapability() const;
  PROTOBUF_NODISCARD ::motor_control::MotionCapability* release_motioncapability();
  ::motor_control::MotionCapability* mutable_motioncapability();
  void set_allocated_motioncapability(::motor_control::MotionCapability* motioncapability);
  private:
  const ::motor_control::MotionCapability& _internal_motioncapability() const;
  ::motor_control::MotionCapability* _internal_mutable_motioncapability();
  public:
  void unsafe_arena_set_allocated_motioncapability(
      ::motor_control::MotionCapability* motioncapability);
  ::motor_control::MotionCapability* unsafe_arena_release_motioncapability();

  // .motor_control.MotionStatus motionStatus = 2;
  void clear_motionstatus();
  ::motor_control::MotionStatus motionstatus() const;
  void set_motionstatus(::motor_control::MotionStatus value);
  private:
  ::motor_control::MotionStatus _internal_motionstatus() const;
  void _internal_set_motionstatus(::motor_control::MotionStatus value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.BedStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::motor_control::MotionInfo* motioninfo_;
  ::motor_control::MotionCapability* motioncapability_;
  int motionstatus_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class PostIdInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.PostIdInfo) */ {
 public:
  inline PostIdInfo() : PostIdInfo(nullptr) {}
  ~PostIdInfo() override;
  explicit constexpr PostIdInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PostIdInfo(const PostIdInfo& from);
  PostIdInfo(PostIdInfo&& from) noexcept
    : PostIdInfo() {
    *this = ::std::move(from);
  }

  inline PostIdInfo& operator=(const PostIdInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PostIdInfo& operator=(PostIdInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PostIdInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const PostIdInfo* internal_default_instance() {
    return reinterpret_cast<const PostIdInfo*>(
               &_PostIdInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(PostIdInfo& a, PostIdInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(PostIdInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PostIdInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PostIdInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PostIdInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PostIdInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PostIdInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PostIdInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.PostIdInfo";
  }
  protected:
  explicit PostIdInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVIDFieldNumber = 1,
    kDIDFieldNumber = 2,
    kHWIDFieldNumber = 3,
    kRIDFieldNumber = 4,
  };
  // uint32 VID = 1;
  void clear_vid();
  uint32_t vid() const;
  void set_vid(uint32_t value);
  private:
  uint32_t _internal_vid() const;
  void _internal_set_vid(uint32_t value);
  public:

  // uint32 DID = 2;
  void clear_did();
  uint32_t did() const;
  void set_did(uint32_t value);
  private:
  uint32_t _internal_did() const;
  void _internal_set_did(uint32_t value);
  public:

  // uint32 HWID = 3;
  void clear_hwid();
  uint32_t hwid() const;
  void set_hwid(uint32_t value);
  private:
  uint32_t _internal_hwid() const;
  void _internal_set_hwid(uint32_t value);
  public:

  // uint32 RID = 4;
  void clear_rid();
  uint32_t rid() const;
  void set_rid(uint32_t value);
  private:
  uint32_t _internal_rid() const;
  void _internal_set_rid(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.PostIdInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t vid_;
  uint32_t did_;
  uint32_t hwid_;
  uint32_t rid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotorControlRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotorControlRequest) */ {
 public:
  inline MotorControlRequest() : MotorControlRequest(nullptr) {}
  ~MotorControlRequest() override;
  explicit constexpr MotorControlRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotorControlRequest(const MotorControlRequest& from);
  MotorControlRequest(MotorControlRequest&& from) noexcept
    : MotorControlRequest() {
    *this = ::std::move(from);
  }

  inline MotorControlRequest& operator=(const MotorControlRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotorControlRequest& operator=(MotorControlRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotorControlRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotorControlRequest* internal_default_instance() {
    return reinterpret_cast<const MotorControlRequest*>(
               &_MotorControlRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(MotorControlRequest& a, MotorControlRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MotorControlRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotorControlRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotorControlRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotorControlRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotorControlRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotorControlRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotorControlRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotorControlRequest";
  }
  protected:
  explicit MotorControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kEnableFieldNumber = 2,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // bool enable = 2;
  void clear_enable();
  bool enable() const;
  void set_enable(bool value);
  private:
  bool _internal_enable() const;
  void _internal_set_enable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotorControlRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  bool enable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotorControlResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotorControlResponse) */ {
 public:
  inline MotorControlResponse() : MotorControlResponse(nullptr) {}
  ~MotorControlResponse() override;
  explicit constexpr MotorControlResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotorControlResponse(const MotorControlResponse& from);
  MotorControlResponse(MotorControlResponse&& from) noexcept
    : MotorControlResponse() {
    *this = ::std::move(from);
  }

  inline MotorControlResponse& operator=(const MotorControlResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotorControlResponse& operator=(MotorControlResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotorControlResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotorControlResponse* internal_default_instance() {
    return reinterpret_cast<const MotorControlResponse*>(
               &_MotorControlResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(MotorControlResponse& a, MotorControlResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MotorControlResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotorControlResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotorControlResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotorControlResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotorControlResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotorControlResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotorControlResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotorControlResponse";
  }
  protected:
  explicit MotorControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kStateFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // .motor_control.DriveState state = 3;
  void clear_state();
  ::motor_control::DriveState state() const;
  void set_state(::motor_control::DriveState value);
  private:
  ::motor_control::DriveState _internal_state() const;
  void _internal_set_state(::motor_control::DriveState value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotorControlResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  int state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotorStatusRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotorStatusRequest) */ {
 public:
  inline MotorStatusRequest() : MotorStatusRequest(nullptr) {}
  ~MotorStatusRequest() override;
  explicit constexpr MotorStatusRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotorStatusRequest(const MotorStatusRequest& from);
  MotorStatusRequest(MotorStatusRequest&& from) noexcept
    : MotorStatusRequest() {
    *this = ::std::move(from);
  }

  inline MotorStatusRequest& operator=(const MotorStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotorStatusRequest& operator=(MotorStatusRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotorStatusRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotorStatusRequest* internal_default_instance() {
    return reinterpret_cast<const MotorStatusRequest*>(
               &_MotorStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(MotorStatusRequest& a, MotorStatusRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MotorStatusRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotorStatusRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotorStatusRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotorStatusRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotorStatusRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotorStatusRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotorStatusRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotorStatusRequest";
  }
  protected:
  explicit MotorStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotorStatusRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class MotorStatusResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.MotorStatusResponse) */ {
 public:
  inline MotorStatusResponse() : MotorStatusResponse(nullptr) {}
  ~MotorStatusResponse() override;
  explicit constexpr MotorStatusResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MotorStatusResponse(const MotorStatusResponse& from);
  MotorStatusResponse(MotorStatusResponse&& from) noexcept
    : MotorStatusResponse() {
    *this = ::std::move(from);
  }

  inline MotorStatusResponse& operator=(const MotorStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MotorStatusResponse& operator=(MotorStatusResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MotorStatusResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MotorStatusResponse* internal_default_instance() {
    return reinterpret_cast<const MotorStatusResponse*>(
               &_MotorStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(MotorStatusResponse& a, MotorStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MotorStatusResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MotorStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MotorStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MotorStatusResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MotorStatusResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MotorStatusResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MotorStatusResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.MotorStatusResponse";
  }
  protected:
  explicit MotorStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kStateFieldNumber = 3,
    kActualPositionFieldNumber = 4,
    kActualVelocityFieldNumber = 5,
    kActualTorqueFieldNumber = 6,
    kSuccessFieldNumber = 1,
    kIsTargetReachedFieldNumber = 9,
    kHasFaultFieldNumber = 10,
    kCurrentModeFieldNumber = 7,
    kStatusWordFieldNumber = 8,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // .motor_control.DriveState state = 3;
  void clear_state();
  ::motor_control::DriveState state() const;
  void set_state(::motor_control::DriveState value);
  private:
  ::motor_control::DriveState _internal_state() const;
  void _internal_set_state(::motor_control::DriveState value);
  public:

  // int32 actual_position = 4;
  void clear_actual_position();
  int32_t actual_position() const;
  void set_actual_position(int32_t value);
  private:
  int32_t _internal_actual_position() const;
  void _internal_set_actual_position(int32_t value);
  public:

  // int32 actual_velocity = 5;
  void clear_actual_velocity();
  int32_t actual_velocity() const;
  void set_actual_velocity(int32_t value);
  private:
  int32_t _internal_actual_velocity() const;
  void _internal_set_actual_velocity(int32_t value);
  public:

  // int32 actual_torque = 6;
  void clear_actual_torque();
  int32_t actual_torque() const;
  void set_actual_torque(int32_t value);
  private:
  int32_t _internal_actual_torque() const;
  void _internal_set_actual_torque(int32_t value);
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // bool is_target_reached = 9;
  void clear_is_target_reached();
  bool is_target_reached() const;
  void set_is_target_reached(bool value);
  private:
  bool _internal_is_target_reached() const;
  void _internal_set_is_target_reached(bool value);
  public:

  // bool has_fault = 10;
  void clear_has_fault();
  bool has_fault() const;
  void set_has_fault(bool value);
  private:
  bool _internal_has_fault() const;
  void _internal_set_has_fault(bool value);
  public:

  // .motor_control.OperationMode current_mode = 7;
  void clear_current_mode();
  ::motor_control::OperationMode current_mode() const;
  void set_current_mode(::motor_control::OperationMode value);
  private:
  ::motor_control::OperationMode _internal_current_mode() const;
  void _internal_set_current_mode(::motor_control::OperationMode value);
  public:

  // uint32 status_word = 8;
  void clear_status_word();
  uint32_t status_word() const;
  void set_status_word(uint32_t value);
  private:
  uint32_t _internal_status_word() const;
  void _internal_set_status_word(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.MotorStatusResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  int state_;
  int32_t actual_position_;
  int32_t actual_velocity_;
  int32_t actual_torque_;
  bool success_;
  bool is_target_reached_;
  bool has_fault_;
  int current_mode_;
  uint32_t status_word_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class OperationModeRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.OperationModeRequest) */ {
 public:
  inline OperationModeRequest() : OperationModeRequest(nullptr) {}
  ~OperationModeRequest() override;
  explicit constexpr OperationModeRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OperationModeRequest(const OperationModeRequest& from);
  OperationModeRequest(OperationModeRequest&& from) noexcept
    : OperationModeRequest() {
    *this = ::std::move(from);
  }

  inline OperationModeRequest& operator=(const OperationModeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperationModeRequest& operator=(OperationModeRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OperationModeRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const OperationModeRequest* internal_default_instance() {
    return reinterpret_cast<const OperationModeRequest*>(
               &_OperationModeRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(OperationModeRequest& a, OperationModeRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(OperationModeRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OperationModeRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OperationModeRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OperationModeRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OperationModeRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OperationModeRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperationModeRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.OperationModeRequest";
  }
  protected:
  explicit OperationModeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kModeFieldNumber = 2,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // .motor_control.OperationMode mode = 2;
  void clear_mode();
  ::motor_control::OperationMode mode() const;
  void set_mode(::motor_control::OperationMode value);
  private:
  ::motor_control::OperationMode _internal_mode() const;
  void _internal_set_mode(::motor_control::OperationMode value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.OperationModeRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  int mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class OperationModeResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.OperationModeResponse) */ {
 public:
  inline OperationModeResponse() : OperationModeResponse(nullptr) {}
  ~OperationModeResponse() override;
  explicit constexpr OperationModeResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OperationModeResponse(const OperationModeResponse& from);
  OperationModeResponse(OperationModeResponse&& from) noexcept
    : OperationModeResponse() {
    *this = ::std::move(from);
  }

  inline OperationModeResponse& operator=(const OperationModeResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline OperationModeResponse& operator=(OperationModeResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OperationModeResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const OperationModeResponse* internal_default_instance() {
    return reinterpret_cast<const OperationModeResponse*>(
               &_OperationModeResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(OperationModeResponse& a, OperationModeResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(OperationModeResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OperationModeResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OperationModeResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OperationModeResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OperationModeResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const OperationModeResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OperationModeResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.OperationModeResponse";
  }
  protected:
  explicit OperationModeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kCurrentModeFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // .motor_control.OperationMode current_mode = 3;
  void clear_current_mode();
  ::motor_control::OperationMode current_mode() const;
  void set_current_mode(::motor_control::OperationMode value);
  private:
  ::motor_control::OperationMode _internal_current_mode() const;
  void _internal_set_current_mode(::motor_control::OperationMode value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.OperationModeResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  int current_mode_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class PositionControlRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.PositionControlRequest) */ {
 public:
  inline PositionControlRequest() : PositionControlRequest(nullptr) {}
  ~PositionControlRequest() override;
  explicit constexpr PositionControlRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PositionControlRequest(const PositionControlRequest& from);
  PositionControlRequest(PositionControlRequest&& from) noexcept
    : PositionControlRequest() {
    *this = ::std::move(from);
  }

  inline PositionControlRequest& operator=(const PositionControlRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PositionControlRequest& operator=(PositionControlRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PositionControlRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PositionControlRequest* internal_default_instance() {
    return reinterpret_cast<const PositionControlRequest*>(
               &_PositionControlRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(PositionControlRequest& a, PositionControlRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PositionControlRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PositionControlRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PositionControlRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PositionControlRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PositionControlRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PositionControlRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PositionControlRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.PositionControlRequest";
  }
  protected:
  explicit PositionControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kTargetPositionFieldNumber = 2,
    kProfileVelocityFieldNumber = 3,
    kProfileAccelerationFieldNumber = 4,
    kProfileDecelerationFieldNumber = 5,
    kAbsoluteFieldNumber = 6,
    kImmediateFieldNumber = 7,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // int32 target_position = 2;
  void clear_target_position();
  int32_t target_position() const;
  void set_target_position(int32_t value);
  private:
  int32_t _internal_target_position() const;
  void _internal_set_target_position(int32_t value);
  public:

  // int32 profile_velocity = 3;
  void clear_profile_velocity();
  int32_t profile_velocity() const;
  void set_profile_velocity(int32_t value);
  private:
  int32_t _internal_profile_velocity() const;
  void _internal_set_profile_velocity(int32_t value);
  public:

  // int32 profile_acceleration = 4;
  void clear_profile_acceleration();
  int32_t profile_acceleration() const;
  void set_profile_acceleration(int32_t value);
  private:
  int32_t _internal_profile_acceleration() const;
  void _internal_set_profile_acceleration(int32_t value);
  public:

  // int32 profile_deceleration = 5;
  void clear_profile_deceleration();
  int32_t profile_deceleration() const;
  void set_profile_deceleration(int32_t value);
  private:
  int32_t _internal_profile_deceleration() const;
  void _internal_set_profile_deceleration(int32_t value);
  public:

  // bool absolute = 6;
  void clear_absolute();
  bool absolute() const;
  void set_absolute(bool value);
  private:
  bool _internal_absolute() const;
  void _internal_set_absolute(bool value);
  public:

  // bool immediate = 7;
  void clear_immediate();
  bool immediate() const;
  void set_immediate(bool value);
  private:
  bool _internal_immediate() const;
  void _internal_set_immediate(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.PositionControlRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  int32_t target_position_;
  int32_t profile_velocity_;
  int32_t profile_acceleration_;
  int32_t profile_deceleration_;
  bool absolute_;
  bool immediate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class PositionControlResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.PositionControlResponse) */ {
 public:
  inline PositionControlResponse() : PositionControlResponse(nullptr) {}
  ~PositionControlResponse() override;
  explicit constexpr PositionControlResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PositionControlResponse(const PositionControlResponse& from);
  PositionControlResponse(PositionControlResponse&& from) noexcept
    : PositionControlResponse() {
    *this = ::std::move(from);
  }

  inline PositionControlResponse& operator=(const PositionControlResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PositionControlResponse& operator=(PositionControlResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PositionControlResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PositionControlResponse* internal_default_instance() {
    return reinterpret_cast<const PositionControlResponse*>(
               &_PositionControlResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(PositionControlResponse& a, PositionControlResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PositionControlResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PositionControlResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PositionControlResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PositionControlResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PositionControlResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PositionControlResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PositionControlResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.PositionControlResponse";
  }
  protected:
  explicit PositionControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kActualPositionFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // int32 actual_position = 3;
  void clear_actual_position();
  int32_t actual_position() const;
  void set_actual_position(int32_t value);
  private:
  int32_t _internal_actual_position() const;
  void _internal_set_actual_position(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.PositionControlResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  int32_t actual_position_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class VelocityControlRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.VelocityControlRequest) */ {
 public:
  inline VelocityControlRequest() : VelocityControlRequest(nullptr) {}
  ~VelocityControlRequest() override;
  explicit constexpr VelocityControlRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VelocityControlRequest(const VelocityControlRequest& from);
  VelocityControlRequest(VelocityControlRequest&& from) noexcept
    : VelocityControlRequest() {
    *this = ::std::move(from);
  }

  inline VelocityControlRequest& operator=(const VelocityControlRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline VelocityControlRequest& operator=(VelocityControlRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VelocityControlRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const VelocityControlRequest* internal_default_instance() {
    return reinterpret_cast<const VelocityControlRequest*>(
               &_VelocityControlRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(VelocityControlRequest& a, VelocityControlRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(VelocityControlRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VelocityControlRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VelocityControlRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VelocityControlRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VelocityControlRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VelocityControlRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VelocityControlRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.VelocityControlRequest";
  }
  protected:
  explicit VelocityControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kTargetVelocityFieldNumber = 2,
    kProfileAccelerationFieldNumber = 3,
    kProfileDecelerationFieldNumber = 4,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // int32 target_velocity = 2;
  void clear_target_velocity();
  int32_t target_velocity() const;
  void set_target_velocity(int32_t value);
  private:
  int32_t _internal_target_velocity() const;
  void _internal_set_target_velocity(int32_t value);
  public:

  // int32 profile_acceleration = 3;
  void clear_profile_acceleration();
  int32_t profile_acceleration() const;
  void set_profile_acceleration(int32_t value);
  private:
  int32_t _internal_profile_acceleration() const;
  void _internal_set_profile_acceleration(int32_t value);
  public:

  // int32 profile_deceleration = 4;
  void clear_profile_deceleration();
  int32_t profile_deceleration() const;
  void set_profile_deceleration(int32_t value);
  private:
  int32_t _internal_profile_deceleration() const;
  void _internal_set_profile_deceleration(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.VelocityControlRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  int32_t target_velocity_;
  int32_t profile_acceleration_;
  int32_t profile_deceleration_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class VelocityControlResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.VelocityControlResponse) */ {
 public:
  inline VelocityControlResponse() : VelocityControlResponse(nullptr) {}
  ~VelocityControlResponse() override;
  explicit constexpr VelocityControlResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VelocityControlResponse(const VelocityControlResponse& from);
  VelocityControlResponse(VelocityControlResponse&& from) noexcept
    : VelocityControlResponse() {
    *this = ::std::move(from);
  }

  inline VelocityControlResponse& operator=(const VelocityControlResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline VelocityControlResponse& operator=(VelocityControlResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VelocityControlResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const VelocityControlResponse* internal_default_instance() {
    return reinterpret_cast<const VelocityControlResponse*>(
               &_VelocityControlResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(VelocityControlResponse& a, VelocityControlResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(VelocityControlResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VelocityControlResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VelocityControlResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VelocityControlResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VelocityControlResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VelocityControlResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VelocityControlResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.VelocityControlResponse";
  }
  protected:
  explicit VelocityControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kActualVelocityFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // int32 actual_velocity = 3;
  void clear_actual_velocity();
  int32_t actual_velocity() const;
  void set_actual_velocity(int32_t value);
  private:
  int32_t _internal_actual_velocity() const;
  void _internal_set_actual_velocity(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.VelocityControlResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  int32_t actual_velocity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class TorqueControlRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.TorqueControlRequest) */ {
 public:
  inline TorqueControlRequest() : TorqueControlRequest(nullptr) {}
  ~TorqueControlRequest() override;
  explicit constexpr TorqueControlRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TorqueControlRequest(const TorqueControlRequest& from);
  TorqueControlRequest(TorqueControlRequest&& from) noexcept
    : TorqueControlRequest() {
    *this = ::std::move(from);
  }

  inline TorqueControlRequest& operator=(const TorqueControlRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TorqueControlRequest& operator=(TorqueControlRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TorqueControlRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const TorqueControlRequest* internal_default_instance() {
    return reinterpret_cast<const TorqueControlRequest*>(
               &_TorqueControlRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(TorqueControlRequest& a, TorqueControlRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TorqueControlRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TorqueControlRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TorqueControlRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TorqueControlRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TorqueControlRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TorqueControlRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TorqueControlRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.TorqueControlRequest";
  }
  protected:
  explicit TorqueControlRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kTargetTorqueFieldNumber = 2,
    kTorqueSlopeFieldNumber = 3,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // int32 target_torque = 2;
  void clear_target_torque();
  int32_t target_torque() const;
  void set_target_torque(int32_t value);
  private:
  int32_t _internal_target_torque() const;
  void _internal_set_target_torque(int32_t value);
  public:

  // int32 torque_slope = 3;
  void clear_torque_slope();
  int32_t torque_slope() const;
  void set_torque_slope(int32_t value);
  private:
  int32_t _internal_torque_slope() const;
  void _internal_set_torque_slope(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.TorqueControlRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  int32_t target_torque_;
  int32_t torque_slope_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class TorqueControlResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.TorqueControlResponse) */ {
 public:
  inline TorqueControlResponse() : TorqueControlResponse(nullptr) {}
  ~TorqueControlResponse() override;
  explicit constexpr TorqueControlResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TorqueControlResponse(const TorqueControlResponse& from);
  TorqueControlResponse(TorqueControlResponse&& from) noexcept
    : TorqueControlResponse() {
    *this = ::std::move(from);
  }

  inline TorqueControlResponse& operator=(const TorqueControlResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TorqueControlResponse& operator=(TorqueControlResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TorqueControlResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TorqueControlResponse* internal_default_instance() {
    return reinterpret_cast<const TorqueControlResponse*>(
               &_TorqueControlResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(TorqueControlResponse& a, TorqueControlResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TorqueControlResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TorqueControlResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TorqueControlResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TorqueControlResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TorqueControlResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TorqueControlResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TorqueControlResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.TorqueControlResponse";
  }
  protected:
  explicit TorqueControlResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kActualTorqueFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // int32 actual_torque = 3;
  void clear_actual_torque();
  int32_t actual_torque() const;
  void set_actual_torque(int32_t value);
  private:
  int32_t _internal_actual_torque() const;
  void _internal_set_actual_torque(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.TorqueControlResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  int32_t actual_torque_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class HomingRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.HomingRequest) */ {
 public:
  inline HomingRequest() : HomingRequest(nullptr) {}
  ~HomingRequest() override;
  explicit constexpr HomingRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HomingRequest(const HomingRequest& from);
  HomingRequest(HomingRequest&& from) noexcept
    : HomingRequest() {
    *this = ::std::move(from);
  }

  inline HomingRequest& operator=(const HomingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline HomingRequest& operator=(HomingRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HomingRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const HomingRequest* internal_default_instance() {
    return reinterpret_cast<const HomingRequest*>(
               &_HomingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(HomingRequest& a, HomingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(HomingRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HomingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HomingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HomingRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HomingRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HomingRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HomingRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.HomingRequest";
  }
  protected:
  explicit HomingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
    kHomingMethodFieldNumber = 2,
    kHomingSpeedSwitchFieldNumber = 3,
    kHomingSpeedZeroFieldNumber = 4,
    kHomingAccelerationFieldNumber = 5,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // int32 homing_method = 2;
  void clear_homing_method();
  int32_t homing_method() const;
  void set_homing_method(int32_t value);
  private:
  int32_t _internal_homing_method() const;
  void _internal_set_homing_method(int32_t value);
  public:

  // int32 homing_speed_switch = 3;
  void clear_homing_speed_switch();
  int32_t homing_speed_switch() const;
  void set_homing_speed_switch(int32_t value);
  private:
  int32_t _internal_homing_speed_switch() const;
  void _internal_set_homing_speed_switch(int32_t value);
  public:

  // int32 homing_speed_zero = 4;
  void clear_homing_speed_zero();
  int32_t homing_speed_zero() const;
  void set_homing_speed_zero(int32_t value);
  private:
  int32_t _internal_homing_speed_zero() const;
  void _internal_set_homing_speed_zero(int32_t value);
  public:

  // int32 homing_acceleration = 5;
  void clear_homing_acceleration();
  int32_t homing_acceleration() const;
  void set_homing_acceleration(int32_t value);
  private:
  int32_t _internal_homing_acceleration() const;
  void _internal_set_homing_acceleration(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.HomingRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  int32_t homing_method_;
  int32_t homing_speed_switch_;
  int32_t homing_speed_zero_;
  int32_t homing_acceleration_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class HomingResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.HomingResponse) */ {
 public:
  inline HomingResponse() : HomingResponse(nullptr) {}
  ~HomingResponse() override;
  explicit constexpr HomingResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HomingResponse(const HomingResponse& from);
  HomingResponse(HomingResponse&& from) noexcept
    : HomingResponse() {
    *this = ::std::move(from);
  }

  inline HomingResponse& operator=(const HomingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline HomingResponse& operator=(HomingResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HomingResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const HomingResponse* internal_default_instance() {
    return reinterpret_cast<const HomingResponse*>(
               &_HomingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(HomingResponse& a, HomingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(HomingResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HomingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HomingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HomingResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HomingResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HomingResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HomingResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.HomingResponse";
  }
  protected:
  explicit HomingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kHomingCompletedFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // bool homing_completed = 3;
  void clear_homing_completed();
  bool homing_completed() const;
  void set_homing_completed(bool value);
  private:
  bool _internal_homing_completed() const;
  void _internal_set_homing_completed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.HomingResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  bool homing_completed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class EmergencyStopRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.EmergencyStopRequest) */ {
 public:
  inline EmergencyStopRequest() : EmergencyStopRequest(nullptr) {}
  ~EmergencyStopRequest() override;
  explicit constexpr EmergencyStopRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EmergencyStopRequest(const EmergencyStopRequest& from);
  EmergencyStopRequest(EmergencyStopRequest&& from) noexcept
    : EmergencyStopRequest() {
    *this = ::std::move(from);
  }

  inline EmergencyStopRequest& operator=(const EmergencyStopRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline EmergencyStopRequest& operator=(EmergencyStopRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EmergencyStopRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const EmergencyStopRequest* internal_default_instance() {
    return reinterpret_cast<const EmergencyStopRequest*>(
               &_EmergencyStopRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(EmergencyStopRequest& a, EmergencyStopRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(EmergencyStopRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EmergencyStopRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EmergencyStopRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EmergencyStopRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EmergencyStopRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EmergencyStopRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EmergencyStopRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.EmergencyStopRequest";
  }
  protected:
  explicit EmergencyStopRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.EmergencyStopRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class EmergencyStopResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.EmergencyStopResponse) */ {
 public:
  inline EmergencyStopResponse() : EmergencyStopResponse(nullptr) {}
  ~EmergencyStopResponse() override;
  explicit constexpr EmergencyStopResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EmergencyStopResponse(const EmergencyStopResponse& from);
  EmergencyStopResponse(EmergencyStopResponse&& from) noexcept
    : EmergencyStopResponse() {
    *this = ::std::move(from);
  }

  inline EmergencyStopResponse& operator=(const EmergencyStopResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline EmergencyStopResponse& operator=(EmergencyStopResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const EmergencyStopResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const EmergencyStopResponse* internal_default_instance() {
    return reinterpret_cast<const EmergencyStopResponse*>(
               &_EmergencyStopResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(EmergencyStopResponse& a, EmergencyStopResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(EmergencyStopResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EmergencyStopResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EmergencyStopResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EmergencyStopResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const EmergencyStopResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const EmergencyStopResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EmergencyStopResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.EmergencyStopResponse";
  }
  protected:
  explicit EmergencyStopResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.EmergencyStopResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class ClearFaultRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.ClearFaultRequest) */ {
 public:
  inline ClearFaultRequest() : ClearFaultRequest(nullptr) {}
  ~ClearFaultRequest() override;
  explicit constexpr ClearFaultRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClearFaultRequest(const ClearFaultRequest& from);
  ClearFaultRequest(ClearFaultRequest&& from) noexcept
    : ClearFaultRequest() {
    *this = ::std::move(from);
  }

  inline ClearFaultRequest& operator=(const ClearFaultRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClearFaultRequest& operator=(ClearFaultRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClearFaultRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClearFaultRequest* internal_default_instance() {
    return reinterpret_cast<const ClearFaultRequest*>(
               &_ClearFaultRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(ClearFaultRequest& a, ClearFaultRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ClearFaultRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClearFaultRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClearFaultRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClearFaultRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClearFaultRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ClearFaultRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClearFaultRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.ClearFaultRequest";
  }
  protected:
  explicit ClearFaultRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdFieldNumber = 1,
  };
  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.ClearFaultRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t node_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class ClearFaultResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.ClearFaultResponse) */ {
 public:
  inline ClearFaultResponse() : ClearFaultResponse(nullptr) {}
  ~ClearFaultResponse() override;
  explicit constexpr ClearFaultResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClearFaultResponse(const ClearFaultResponse& from);
  ClearFaultResponse(ClearFaultResponse&& from) noexcept
    : ClearFaultResponse() {
    *this = ::std::move(from);
  }

  inline ClearFaultResponse& operator=(const ClearFaultResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClearFaultResponse& operator=(ClearFaultResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClearFaultResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClearFaultResponse* internal_default_instance() {
    return reinterpret_cast<const ClearFaultResponse*>(
               &_ClearFaultResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(ClearFaultResponse& a, ClearFaultResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(ClearFaultResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClearFaultResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClearFaultResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClearFaultResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClearFaultResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ClearFaultResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClearFaultResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.ClearFaultResponse";
  }
  protected:
  explicit ClearFaultResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
    kFaultClearedFieldNumber = 3,
  };
  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // bool fault_cleared = 3;
  void clear_fault_cleared();
  bool fault_cleared() const;
  void set_fault_cleared(bool value);
  private:
  bool _internal_fault_cleared() const;
  void _internal_set_fault_cleared(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.ClearFaultResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  bool fault_cleared_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetAllNodesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:motor_control.GetAllNodesRequest) */ {
 public:
  inline GetAllNodesRequest() : GetAllNodesRequest(nullptr) {}
  explicit constexpr GetAllNodesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetAllNodesRequest(const GetAllNodesRequest& from);
  GetAllNodesRequest(GetAllNodesRequest&& from) noexcept
    : GetAllNodesRequest() {
    *this = ::std::move(from);
  }

  inline GetAllNodesRequest& operator=(const GetAllNodesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetAllNodesRequest& operator=(GetAllNodesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetAllNodesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetAllNodesRequest* internal_default_instance() {
    return reinterpret_cast<const GetAllNodesRequest*>(
               &_GetAllNodesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(GetAllNodesRequest& a, GetAllNodesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetAllNodesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetAllNodesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetAllNodesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetAllNodesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetAllNodesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetAllNodesRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.GetAllNodesRequest";
  }
  protected:
  explicit GetAllNodesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:motor_control.GetAllNodesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class NodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.NodeInfo) */ {
 public:
  inline NodeInfo() : NodeInfo(nullptr) {}
  ~NodeInfo() override;
  explicit constexpr NodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NodeInfo(const NodeInfo& from);
  NodeInfo(NodeInfo&& from) noexcept
    : NodeInfo() {
    *this = ::std::move(from);
  }

  inline NodeInfo& operator=(const NodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline NodeInfo& operator=(NodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const NodeInfo* internal_default_instance() {
    return reinterpret_cast<const NodeInfo*>(
               &_NodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(NodeInfo& a, NodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(NodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NodeInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.NodeInfo";
  }
  protected:
  explicit NodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kVendorNameFieldNumber = 4,
    kProductNameFieldNumber = 5,
    kRevisionNumberFieldNumber = 6,
    kNodeIdFieldNumber = 1,
    kIsConnectedFieldNumber = 2,
    kStateFieldNumber = 3,
  };
  // string vendor_name = 4;
  void clear_vendor_name();
  const std::string& vendor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vendor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vendor_name();
  PROTOBUF_NODISCARD std::string* release_vendor_name();
  void set_allocated_vendor_name(std::string* vendor_name);
  private:
  const std::string& _internal_vendor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vendor_name(const std::string& value);
  std::string* _internal_mutable_vendor_name();
  public:

  // string product_name = 5;
  void clear_product_name();
  const std::string& product_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_product_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_product_name();
  PROTOBUF_NODISCARD std::string* release_product_name();
  void set_allocated_product_name(std::string* product_name);
  private:
  const std::string& _internal_product_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_product_name(const std::string& value);
  std::string* _internal_mutable_product_name();
  public:

  // string revision_number = 6;
  void clear_revision_number();
  const std::string& revision_number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_revision_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_revision_number();
  PROTOBUF_NODISCARD std::string* release_revision_number();
  void set_allocated_revision_number(std::string* revision_number);
  private:
  const std::string& _internal_revision_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_revision_number(const std::string& value);
  std::string* _internal_mutable_revision_number();
  public:

  // uint32 node_id = 1;
  void clear_node_id();
  uint32_t node_id() const;
  void set_node_id(uint32_t value);
  private:
  uint32_t _internal_node_id() const;
  void _internal_set_node_id(uint32_t value);
  public:

  // bool is_connected = 2;
  void clear_is_connected();
  bool is_connected() const;
  void set_is_connected(bool value);
  private:
  bool _internal_is_connected() const;
  void _internal_set_is_connected(bool value);
  public:

  // .motor_control.DriveState state = 3;
  void clear_state();
  ::motor_control::DriveState state() const;
  void set_state(::motor_control::DriveState value);
  private:
  ::motor_control::DriveState _internal_state() const;
  void _internal_set_state(::motor_control::DriveState value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.NodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vendor_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr product_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr revision_number_;
  uint32_t node_id_;
  bool is_connected_;
  int state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetAllNodesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.GetAllNodesResponse) */ {
 public:
  inline GetAllNodesResponse() : GetAllNodesResponse(nullptr) {}
  ~GetAllNodesResponse() override;
  explicit constexpr GetAllNodesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetAllNodesResponse(const GetAllNodesResponse& from);
  GetAllNodesResponse(GetAllNodesResponse&& from) noexcept
    : GetAllNodesResponse() {
    *this = ::std::move(from);
  }

  inline GetAllNodesResponse& operator=(const GetAllNodesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetAllNodesResponse& operator=(GetAllNodesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetAllNodesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetAllNodesResponse* internal_default_instance() {
    return reinterpret_cast<const GetAllNodesResponse*>(
               &_GetAllNodesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(GetAllNodesResponse& a, GetAllNodesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetAllNodesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetAllNodesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetAllNodesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetAllNodesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetAllNodesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetAllNodesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetAllNodesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.GetAllNodesResponse";
  }
  protected:
  explicit GetAllNodesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodesFieldNumber = 3,
    kMessageFieldNumber = 2,
    kSuccessFieldNumber = 1,
  };
  // repeated .motor_control.NodeInfo nodes = 3;
  int nodes_size() const;
  private:
  int _internal_nodes_size() const;
  public:
  void clear_nodes();
  ::motor_control::NodeInfo* mutable_nodes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::NodeInfo >*
      mutable_nodes();
  private:
  const ::motor_control::NodeInfo& _internal_nodes(int index) const;
  ::motor_control::NodeInfo* _internal_add_nodes();
  public:
  const ::motor_control::NodeInfo& nodes(int index) const;
  ::motor_control::NodeInfo* add_nodes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::NodeInfo >&
      nodes() const;

  // string message = 2;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // bool success = 1;
  void clear_success();
  bool success() const;
  void set_success(bool value);
  private:
  bool _internal_success() const;
  void _internal_set_success(bool value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.GetAllNodesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::NodeInfo > nodes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  bool success_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class DeviceConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.DeviceConfig) */ {
 public:
  inline DeviceConfig() : DeviceConfig(nullptr) {}
  ~DeviceConfig() override;
  explicit constexpr DeviceConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceConfig(const DeviceConfig& from);
  DeviceConfig(DeviceConfig&& from) noexcept
    : DeviceConfig() {
    *this = ::std::move(from);
  }

  inline DeviceConfig& operator=(const DeviceConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceConfig& operator=(DeviceConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceConfig* internal_default_instance() {
    return reinterpret_cast<const DeviceConfig*>(
               &_DeviceConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  friend void swap(DeviceConfig& a, DeviceConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeviceConfig& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.DeviceConfig";
  }
  protected:
  explicit DeviceConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  // string device = 1;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:motor_control.DeviceConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class ConfigMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:motor_control.ConfigMessage) */ {
 public:
  inline ConfigMessage() : ConfigMessage(nullptr) {}
  ~ConfigMessage() override;
  explicit constexpr ConfigMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigMessage(const ConfigMessage& from);
  ConfigMessage(ConfigMessage&& from) noexcept
    : ConfigMessage() {
    *this = ::std::move(from);
  }

  inline ConfigMessage& operator=(const ConfigMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigMessage& operator=(ConfigMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigMessage* internal_default_instance() {
    return reinterpret_cast<const ConfigMessage*>(
               &_ConfigMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  friend void swap(ConfigMessage& a, ConfigMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "motor_control.ConfigMessage";
  }
  protected:
  explicit ConfigMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeIdsFieldNumber = 4,
    kUartDevicesFieldNumber = 5,
    kI2CDevicesFieldNumber = 6,
    kCanInterfaceFieldNumber = 1,
    kGrpcServerAddressFieldNumber = 2,
    kStatusSocketPortFieldNumber = 3,
  };
  // repeated uint32 node_ids = 4;
  int node_ids_size() const;
  private:
  int _internal_node_ids_size() const;
  public:
  void clear_node_ids();
  private:
  uint32_t _internal_node_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_node_ids() const;
  void _internal_add_node_ids(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_node_ids();
  public:
  uint32_t node_ids(int index) const;
  void set_node_ids(int index, uint32_t value);
  void add_node_ids(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      node_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_node_ids();

  // repeated .motor_control.DeviceConfig uart_devices = 5;
  int uart_devices_size() const;
  private:
  int _internal_uart_devices_size() const;
  public:
  void clear_uart_devices();
  ::motor_control::DeviceConfig* mutable_uart_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >*
      mutable_uart_devices();
  private:
  const ::motor_control::DeviceConfig& _internal_uart_devices(int index) const;
  ::motor_control::DeviceConfig* _internal_add_uart_devices();
  public:
  const ::motor_control::DeviceConfig& uart_devices(int index) const;
  ::motor_control::DeviceConfig* add_uart_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >&
      uart_devices() const;

  // repeated .motor_control.DeviceConfig i2c_devices = 6;
  int i2c_devices_size() const;
  private:
  int _internal_i2c_devices_size() const;
  public:
  void clear_i2c_devices();
  ::motor_control::DeviceConfig* mutable_i2c_devices(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >*
      mutable_i2c_devices();
  private:
  const ::motor_control::DeviceConfig& _internal_i2c_devices(int index) const;
  ::motor_control::DeviceConfig* _internal_add_i2c_devices();
  public:
  const ::motor_control::DeviceConfig& i2c_devices(int index) const;
  ::motor_control::DeviceConfig* add_i2c_devices();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >&
      i2c_devices() const;

  // string can_interface = 1;
  void clear_can_interface();
  const std::string& can_interface() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_can_interface(ArgT0&& arg0, ArgT... args);
  std::string* mutable_can_interface();
  PROTOBUF_NODISCARD std::string* release_can_interface();
  void set_allocated_can_interface(std::string* can_interface);
  private:
  const std::string& _internal_can_interface() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_can_interface(const std::string& value);
  std::string* _internal_mutable_can_interface();
  public:

  // string grpc_server_address = 2;
  void clear_grpc_server_address();
  const std::string& grpc_server_address() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_grpc_server_address(ArgT0&& arg0, ArgT... args);
  std::string* mutable_grpc_server_address();
  PROTOBUF_NODISCARD std::string* release_grpc_server_address();
  void set_allocated_grpc_server_address(std::string* grpc_server_address);
  private:
  const std::string& _internal_grpc_server_address() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_grpc_server_address(const std::string& value);
  std::string* _internal_mutable_grpc_server_address();
  public:

  // int32 status_socket_port = 3;
  void clear_status_socket_port();
  int32_t status_socket_port() const;
  void set_status_socket_port(int32_t value);
  private:
  int32_t _internal_status_socket_port() const;
  void _internal_set_status_socket_port(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:motor_control.ConfigMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > node_ids_;
  mutable std::atomic<int> _node_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig > uart_devices_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig > i2c_devices_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr can_interface_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr grpc_server_address_;
  int32_t status_socket_port_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_motor_5fcontrol_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StartMoveDescription

// string contextUID = 1;
inline void StartMoveDescription::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& StartMoveDescription::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveDescription.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartMoveDescription::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.StartMoveDescription.contextUID)
}
inline std::string* StartMoveDescription::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.StartMoveDescription.contextUID)
  return _s;
}
inline const std::string& StartMoveDescription::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void StartMoveDescription::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartMoveDescription::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartMoveDescription::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.StartMoveDescription.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartMoveDescription::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.StartMoveDescription.contextUID)
}

// .motor_control.MotionMode mode = 2;
inline void StartMoveDescription::clear_mode() {
  mode_ = 0;
}
inline ::motor_control::MotionMode StartMoveDescription::_internal_mode() const {
  return static_cast< ::motor_control::MotionMode >(mode_);
}
inline ::motor_control::MotionMode StartMoveDescription::mode() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveDescription.mode)
  return _internal_mode();
}
inline void StartMoveDescription::_internal_set_mode(::motor_control::MotionMode value) {
  
  mode_ = value;
}
inline void StartMoveDescription::set_mode(::motor_control::MotionMode value) {
  _internal_set_mode(value);
  // @@protoc_insertion_point(field_set:motor_control.StartMoveDescription.mode)
}

// .motor_control.BedType bedType = 3;
inline void StartMoveDescription::clear_bedtype() {
  bedtype_ = 0;
}
inline ::motor_control::BedType StartMoveDescription::_internal_bedtype() const {
  return static_cast< ::motor_control::BedType >(bedtype_);
}
inline ::motor_control::BedType StartMoveDescription::bedtype() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveDescription.bedType)
  return _internal_bedtype();
}
inline void StartMoveDescription::_internal_set_bedtype(::motor_control::BedType value) {
  
  bedtype_ = value;
}
inline void StartMoveDescription::set_bedtype(::motor_control::BedType value) {
  _internal_set_bedtype(value);
  // @@protoc_insertion_point(field_set:motor_control.StartMoveDescription.bedType)
}

// .motor_control.MotionInfo targetMotionInfo = 4;
inline bool StartMoveDescription::_internal_has_targetmotioninfo() const {
  return this != internal_default_instance() && targetmotioninfo_ != nullptr;
}
inline bool StartMoveDescription::has_targetmotioninfo() const {
  return _internal_has_targetmotioninfo();
}
inline void StartMoveDescription::clear_targetmotioninfo() {
  if (GetArenaForAllocation() == nullptr && targetmotioninfo_ != nullptr) {
    delete targetmotioninfo_;
  }
  targetmotioninfo_ = nullptr;
}
inline const ::motor_control::MotionInfo& StartMoveDescription::_internal_targetmotioninfo() const {
  const ::motor_control::MotionInfo* p = targetmotioninfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::MotionInfo&>(
      ::motor_control::_MotionInfo_default_instance_);
}
inline const ::motor_control::MotionInfo& StartMoveDescription::targetmotioninfo() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveDescription.targetMotionInfo)
  return _internal_targetmotioninfo();
}
inline void StartMoveDescription::unsafe_arena_set_allocated_targetmotioninfo(
    ::motor_control::MotionInfo* targetmotioninfo) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(targetmotioninfo_);
  }
  targetmotioninfo_ = targetmotioninfo;
  if (targetmotioninfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.StartMoveDescription.targetMotionInfo)
}
inline ::motor_control::MotionInfo* StartMoveDescription::release_targetmotioninfo() {
  
  ::motor_control::MotionInfo* temp = targetmotioninfo_;
  targetmotioninfo_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::MotionInfo* StartMoveDescription::unsafe_arena_release_targetmotioninfo() {
  // @@protoc_insertion_point(field_release:motor_control.StartMoveDescription.targetMotionInfo)
  
  ::motor_control::MotionInfo* temp = targetmotioninfo_;
  targetmotioninfo_ = nullptr;
  return temp;
}
inline ::motor_control::MotionInfo* StartMoveDescription::_internal_mutable_targetmotioninfo() {
  
  if (targetmotioninfo_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::MotionInfo>(GetArenaForAllocation());
    targetmotioninfo_ = p;
  }
  return targetmotioninfo_;
}
inline ::motor_control::MotionInfo* StartMoveDescription::mutable_targetmotioninfo() {
  ::motor_control::MotionInfo* _msg = _internal_mutable_targetmotioninfo();
  // @@protoc_insertion_point(field_mutable:motor_control.StartMoveDescription.targetMotionInfo)
  return _msg;
}
inline void StartMoveDescription::set_allocated_targetmotioninfo(::motor_control::MotionInfo* targetmotioninfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete targetmotioninfo_;
  }
  if (targetmotioninfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::MotionInfo>::GetOwningArena(targetmotioninfo);
    if (message_arena != submessage_arena) {
      targetmotioninfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, targetmotioninfo, submessage_arena);
    }
    
  } else {
    
  }
  targetmotioninfo_ = targetmotioninfo;
  // @@protoc_insertion_point(field_set_allocated:motor_control.StartMoveDescription.targetMotionInfo)
}

// -------------------------------------------------------------------

// StartMoveStatus

// string contextUID = 1;
inline void StartMoveStatus::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& StartMoveStatus::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveStatus.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartMoveStatus::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.StartMoveStatus.contextUID)
}
inline std::string* StartMoveStatus::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.StartMoveStatus.contextUID)
  return _s;
}
inline const std::string& StartMoveStatus::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void StartMoveStatus::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartMoveStatus::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartMoveStatus::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.StartMoveStatus.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartMoveStatus::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.StartMoveStatus.contextUID)
}

// uint64 errorCode = 2;
inline void StartMoveStatus::clear_errorcode() {
  errorcode_ = uint64_t{0u};
}
inline uint64_t StartMoveStatus::_internal_errorcode() const {
  return errorcode_;
}
inline uint64_t StartMoveStatus::errorcode() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveStatus.errorCode)
  return _internal_errorcode();
}
inline void StartMoveStatus::_internal_set_errorcode(uint64_t value) {
  
  errorcode_ = value;
}
inline void StartMoveStatus::set_errorcode(uint64_t value) {
  _internal_set_errorcode(value);
  // @@protoc_insertion_point(field_set:motor_control.StartMoveStatus.errorCode)
}

// .motor_control.MotionInfo currentMotionInfo = 3;
inline bool StartMoveStatus::_internal_has_currentmotioninfo() const {
  return this != internal_default_instance() && currentmotioninfo_ != nullptr;
}
inline bool StartMoveStatus::has_currentmotioninfo() const {
  return _internal_has_currentmotioninfo();
}
inline void StartMoveStatus::clear_currentmotioninfo() {
  if (GetArenaForAllocation() == nullptr && currentmotioninfo_ != nullptr) {
    delete currentmotioninfo_;
  }
  currentmotioninfo_ = nullptr;
}
inline const ::motor_control::MotionInfo& StartMoveStatus::_internal_currentmotioninfo() const {
  const ::motor_control::MotionInfo* p = currentmotioninfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::MotionInfo&>(
      ::motor_control::_MotionInfo_default_instance_);
}
inline const ::motor_control::MotionInfo& StartMoveStatus::currentmotioninfo() const {
  // @@protoc_insertion_point(field_get:motor_control.StartMoveStatus.currentMotionInfo)
  return _internal_currentmotioninfo();
}
inline void StartMoveStatus::unsafe_arena_set_allocated_currentmotioninfo(
    ::motor_control::MotionInfo* currentmotioninfo) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(currentmotioninfo_);
  }
  currentmotioninfo_ = currentmotioninfo;
  if (currentmotioninfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.StartMoveStatus.currentMotionInfo)
}
inline ::motor_control::MotionInfo* StartMoveStatus::release_currentmotioninfo() {
  
  ::motor_control::MotionInfo* temp = currentmotioninfo_;
  currentmotioninfo_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::MotionInfo* StartMoveStatus::unsafe_arena_release_currentmotioninfo() {
  // @@protoc_insertion_point(field_release:motor_control.StartMoveStatus.currentMotionInfo)
  
  ::motor_control::MotionInfo* temp = currentmotioninfo_;
  currentmotioninfo_ = nullptr;
  return temp;
}
inline ::motor_control::MotionInfo* StartMoveStatus::_internal_mutable_currentmotioninfo() {
  
  if (currentmotioninfo_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::MotionInfo>(GetArenaForAllocation());
    currentmotioninfo_ = p;
  }
  return currentmotioninfo_;
}
inline ::motor_control::MotionInfo* StartMoveStatus::mutable_currentmotioninfo() {
  ::motor_control::MotionInfo* _msg = _internal_mutable_currentmotioninfo();
  // @@protoc_insertion_point(field_mutable:motor_control.StartMoveStatus.currentMotionInfo)
  return _msg;
}
inline void StartMoveStatus::set_allocated_currentmotioninfo(::motor_control::MotionInfo* currentmotioninfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete currentmotioninfo_;
  }
  if (currentmotioninfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::MotionInfo>::GetOwningArena(currentmotioninfo);
    if (message_arena != submessage_arena) {
      currentmotioninfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, currentmotioninfo, submessage_arena);
    }
    
  } else {
    
  }
  currentmotioninfo_ = currentmotioninfo;
  // @@protoc_insertion_point(field_set_allocated:motor_control.StartMoveStatus.currentMotionInfo)
}

// -------------------------------------------------------------------

// CommonDescription

// string contextUID = 1;
inline void CommonDescription::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& CommonDescription::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.CommonDescription.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CommonDescription::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.CommonDescription.contextUID)
}
inline std::string* CommonDescription::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.CommonDescription.contextUID)
  return _s;
}
inline const std::string& CommonDescription::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void CommonDescription::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CommonDescription::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CommonDescription::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.CommonDescription.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CommonDescription::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.CommonDescription.contextUID)
}

// -------------------------------------------------------------------

// CommonStatus

// string contextUID = 1;
inline void CommonStatus::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& CommonStatus::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.CommonStatus.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CommonStatus::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.CommonStatus.contextUID)
}
inline std::string* CommonStatus::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.CommonStatus.contextUID)
  return _s;
}
inline const std::string& CommonStatus::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void CommonStatus::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CommonStatus::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CommonStatus::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.CommonStatus.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CommonStatus::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.CommonStatus.contextUID)
}

// uint64 errorCode = 2;
inline void CommonStatus::clear_errorcode() {
  errorcode_ = uint64_t{0u};
}
inline uint64_t CommonStatus::_internal_errorcode() const {
  return errorcode_;
}
inline uint64_t CommonStatus::errorcode() const {
  // @@protoc_insertion_point(field_get:motor_control.CommonStatus.errorCode)
  return _internal_errorcode();
}
inline void CommonStatus::_internal_set_errorcode(uint64_t value) {
  
  errorcode_ = value;
}
inline void CommonStatus::set_errorcode(uint64_t value) {
  _internal_set_errorcode(value);
  // @@protoc_insertion_point(field_set:motor_control.CommonStatus.errorCode)
}

// -------------------------------------------------------------------

// GetPostIdDescription

// string contextUID = 1;
inline void GetPostIdDescription::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& GetPostIdDescription::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.GetPostIdDescription.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPostIdDescription::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.GetPostIdDescription.contextUID)
}
inline std::string* GetPostIdDescription::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.GetPostIdDescription.contextUID)
  return _s;
}
inline const std::string& GetPostIdDescription::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void GetPostIdDescription::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPostIdDescription::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPostIdDescription::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.GetPostIdDescription.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPostIdDescription::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.GetPostIdDescription.contextUID)
}

// .motor_control.BedType bedType = 2;
inline void GetPostIdDescription::clear_bedtype() {
  bedtype_ = 0;
}
inline ::motor_control::BedType GetPostIdDescription::_internal_bedtype() const {
  return static_cast< ::motor_control::BedType >(bedtype_);
}
inline ::motor_control::BedType GetPostIdDescription::bedtype() const {
  // @@protoc_insertion_point(field_get:motor_control.GetPostIdDescription.bedType)
  return _internal_bedtype();
}
inline void GetPostIdDescription::_internal_set_bedtype(::motor_control::BedType value) {
  
  bedtype_ = value;
}
inline void GetPostIdDescription::set_bedtype(::motor_control::BedType value) {
  _internal_set_bedtype(value);
  // @@protoc_insertion_point(field_set:motor_control.GetPostIdDescription.bedType)
}

// -------------------------------------------------------------------

// GetPostIdStatus

// string contextUID = 1;
inline void GetPostIdStatus::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& GetPostIdStatus::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.GetPostIdStatus.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPostIdStatus::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.GetPostIdStatus.contextUID)
}
inline std::string* GetPostIdStatus::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.GetPostIdStatus.contextUID)
  return _s;
}
inline const std::string& GetPostIdStatus::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void GetPostIdStatus::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPostIdStatus::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPostIdStatus::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.GetPostIdStatus.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPostIdStatus::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.GetPostIdStatus.contextUID)
}

// uint64 errorCode = 2;
inline void GetPostIdStatus::clear_errorcode() {
  errorcode_ = uint64_t{0u};
}
inline uint64_t GetPostIdStatus::_internal_errorcode() const {
  return errorcode_;
}
inline uint64_t GetPostIdStatus::errorcode() const {
  // @@protoc_insertion_point(field_get:motor_control.GetPostIdStatus.errorCode)
  return _internal_errorcode();
}
inline void GetPostIdStatus::_internal_set_errorcode(uint64_t value) {
  
  errorcode_ = value;
}
inline void GetPostIdStatus::set_errorcode(uint64_t value) {
  _internal_set_errorcode(value);
  // @@protoc_insertion_point(field_set:motor_control.GetPostIdStatus.errorCode)
}

// .motor_control.PostIdInfo postId = 3;
inline bool GetPostIdStatus::_internal_has_postid() const {
  return this != internal_default_instance() && postid_ != nullptr;
}
inline bool GetPostIdStatus::has_postid() const {
  return _internal_has_postid();
}
inline void GetPostIdStatus::clear_postid() {
  if (GetArenaForAllocation() == nullptr && postid_ != nullptr) {
    delete postid_;
  }
  postid_ = nullptr;
}
inline const ::motor_control::PostIdInfo& GetPostIdStatus::_internal_postid() const {
  const ::motor_control::PostIdInfo* p = postid_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::PostIdInfo&>(
      ::motor_control::_PostIdInfo_default_instance_);
}
inline const ::motor_control::PostIdInfo& GetPostIdStatus::postid() const {
  // @@protoc_insertion_point(field_get:motor_control.GetPostIdStatus.postId)
  return _internal_postid();
}
inline void GetPostIdStatus::unsafe_arena_set_allocated_postid(
    ::motor_control::PostIdInfo* postid) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(postid_);
  }
  postid_ = postid;
  if (postid) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.GetPostIdStatus.postId)
}
inline ::motor_control::PostIdInfo* GetPostIdStatus::release_postid() {
  
  ::motor_control::PostIdInfo* temp = postid_;
  postid_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::PostIdInfo* GetPostIdStatus::unsafe_arena_release_postid() {
  // @@protoc_insertion_point(field_release:motor_control.GetPostIdStatus.postId)
  
  ::motor_control::PostIdInfo* temp = postid_;
  postid_ = nullptr;
  return temp;
}
inline ::motor_control::PostIdInfo* GetPostIdStatus::_internal_mutable_postid() {
  
  if (postid_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::PostIdInfo>(GetArenaForAllocation());
    postid_ = p;
  }
  return postid_;
}
inline ::motor_control::PostIdInfo* GetPostIdStatus::mutable_postid() {
  ::motor_control::PostIdInfo* _msg = _internal_mutable_postid();
  // @@protoc_insertion_point(field_mutable:motor_control.GetPostIdStatus.postId)
  return _msg;
}
inline void GetPostIdStatus::set_allocated_postid(::motor_control::PostIdInfo* postid) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete postid_;
  }
  if (postid) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::PostIdInfo>::GetOwningArena(postid);
    if (message_arena != submessage_arena) {
      postid = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, postid, submessage_arena);
    }
    
  } else {
    
  }
  postid_ = postid;
  // @@protoc_insertion_point(field_set_allocated:motor_control.GetPostIdStatus.postId)
}

// -------------------------------------------------------------------

// SystemStatusInfoStatus

// string softwareVersion = 1;
inline void SystemStatusInfoStatus::clear_softwareversion() {
  softwareversion_.ClearToEmpty();
}
inline const std::string& SystemStatusInfoStatus::softwareversion() const {
  // @@protoc_insertion_point(field_get:motor_control.SystemStatusInfoStatus.softwareVersion)
  return _internal_softwareversion();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SystemStatusInfoStatus::set_softwareversion(ArgT0&& arg0, ArgT... args) {
 
 softwareversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.SystemStatusInfoStatus.softwareVersion)
}
inline std::string* SystemStatusInfoStatus::mutable_softwareversion() {
  std::string* _s = _internal_mutable_softwareversion();
  // @@protoc_insertion_point(field_mutable:motor_control.SystemStatusInfoStatus.softwareVersion)
  return _s;
}
inline const std::string& SystemStatusInfoStatus::_internal_softwareversion() const {
  return softwareversion_.Get();
}
inline void SystemStatusInfoStatus::_internal_set_softwareversion(const std::string& value) {
  
  softwareversion_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SystemStatusInfoStatus::_internal_mutable_softwareversion() {
  
  return softwareversion_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SystemStatusInfoStatus::release_softwareversion() {
  // @@protoc_insertion_point(field_release:motor_control.SystemStatusInfoStatus.softwareVersion)
  return softwareversion_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SystemStatusInfoStatus::set_allocated_softwareversion(std::string* softwareversion) {
  if (softwareversion != nullptr) {
    
  } else {
    
  }
  softwareversion_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), softwareversion,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (softwareversion_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    softwareversion_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.SystemStatusInfoStatus.softwareVersion)
}

// string timeStamp = 2;
inline void SystemStatusInfoStatus::clear_timestamp() {
  timestamp_.ClearToEmpty();
}
inline const std::string& SystemStatusInfoStatus::timestamp() const {
  // @@protoc_insertion_point(field_get:motor_control.SystemStatusInfoStatus.timeStamp)
  return _internal_timestamp();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SystemStatusInfoStatus::set_timestamp(ArgT0&& arg0, ArgT... args) {
 
 timestamp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.SystemStatusInfoStatus.timeStamp)
}
inline std::string* SystemStatusInfoStatus::mutable_timestamp() {
  std::string* _s = _internal_mutable_timestamp();
  // @@protoc_insertion_point(field_mutable:motor_control.SystemStatusInfoStatus.timeStamp)
  return _s;
}
inline const std::string& SystemStatusInfoStatus::_internal_timestamp() const {
  return timestamp_.Get();
}
inline void SystemStatusInfoStatus::_internal_set_timestamp(const std::string& value) {
  
  timestamp_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SystemStatusInfoStatus::_internal_mutable_timestamp() {
  
  return timestamp_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SystemStatusInfoStatus::release_timestamp() {
  // @@protoc_insertion_point(field_release:motor_control.SystemStatusInfoStatus.timeStamp)
  return timestamp_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SystemStatusInfoStatus::set_allocated_timestamp(std::string* timestamp) {
  if (timestamp != nullptr) {
    
  } else {
    
  }
  timestamp_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), timestamp,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (timestamp_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    timestamp_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.SystemStatusInfoStatus.timeStamp)
}

// .motor_control.BedStatus firstBedStatus = 3;
inline bool SystemStatusInfoStatus::_internal_has_firstbedstatus() const {
  return this != internal_default_instance() && firstbedstatus_ != nullptr;
}
inline bool SystemStatusInfoStatus::has_firstbedstatus() const {
  return _internal_has_firstbedstatus();
}
inline void SystemStatusInfoStatus::clear_firstbedstatus() {
  if (GetArenaForAllocation() == nullptr && firstbedstatus_ != nullptr) {
    delete firstbedstatus_;
  }
  firstbedstatus_ = nullptr;
}
inline const ::motor_control::BedStatus& SystemStatusInfoStatus::_internal_firstbedstatus() const {
  const ::motor_control::BedStatus* p = firstbedstatus_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::BedStatus&>(
      ::motor_control::_BedStatus_default_instance_);
}
inline const ::motor_control::BedStatus& SystemStatusInfoStatus::firstbedstatus() const {
  // @@protoc_insertion_point(field_get:motor_control.SystemStatusInfoStatus.firstBedStatus)
  return _internal_firstbedstatus();
}
inline void SystemStatusInfoStatus::unsafe_arena_set_allocated_firstbedstatus(
    ::motor_control::BedStatus* firstbedstatus) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(firstbedstatus_);
  }
  firstbedstatus_ = firstbedstatus;
  if (firstbedstatus) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.SystemStatusInfoStatus.firstBedStatus)
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::release_firstbedstatus() {
  
  ::motor_control::BedStatus* temp = firstbedstatus_;
  firstbedstatus_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::unsafe_arena_release_firstbedstatus() {
  // @@protoc_insertion_point(field_release:motor_control.SystemStatusInfoStatus.firstBedStatus)
  
  ::motor_control::BedStatus* temp = firstbedstatus_;
  firstbedstatus_ = nullptr;
  return temp;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::_internal_mutable_firstbedstatus() {
  
  if (firstbedstatus_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::BedStatus>(GetArenaForAllocation());
    firstbedstatus_ = p;
  }
  return firstbedstatus_;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::mutable_firstbedstatus() {
  ::motor_control::BedStatus* _msg = _internal_mutable_firstbedstatus();
  // @@protoc_insertion_point(field_mutable:motor_control.SystemStatusInfoStatus.firstBedStatus)
  return _msg;
}
inline void SystemStatusInfoStatus::set_allocated_firstbedstatus(::motor_control::BedStatus* firstbedstatus) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete firstbedstatus_;
  }
  if (firstbedstatus) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::BedStatus>::GetOwningArena(firstbedstatus);
    if (message_arena != submessage_arena) {
      firstbedstatus = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, firstbedstatus, submessage_arena);
    }
    
  } else {
    
  }
  firstbedstatus_ = firstbedstatus;
  // @@protoc_insertion_point(field_set_allocated:motor_control.SystemStatusInfoStatus.firstBedStatus)
}

// .motor_control.BedStatus secondaryBedStatus = 4;
inline bool SystemStatusInfoStatus::_internal_has_secondarybedstatus() const {
  return this != internal_default_instance() && secondarybedstatus_ != nullptr;
}
inline bool SystemStatusInfoStatus::has_secondarybedstatus() const {
  return _internal_has_secondarybedstatus();
}
inline void SystemStatusInfoStatus::clear_secondarybedstatus() {
  if (GetArenaForAllocation() == nullptr && secondarybedstatus_ != nullptr) {
    delete secondarybedstatus_;
  }
  secondarybedstatus_ = nullptr;
}
inline const ::motor_control::BedStatus& SystemStatusInfoStatus::_internal_secondarybedstatus() const {
  const ::motor_control::BedStatus* p = secondarybedstatus_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::BedStatus&>(
      ::motor_control::_BedStatus_default_instance_);
}
inline const ::motor_control::BedStatus& SystemStatusInfoStatus::secondarybedstatus() const {
  // @@protoc_insertion_point(field_get:motor_control.SystemStatusInfoStatus.secondaryBedStatus)
  return _internal_secondarybedstatus();
}
inline void SystemStatusInfoStatus::unsafe_arena_set_allocated_secondarybedstatus(
    ::motor_control::BedStatus* secondarybedstatus) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(secondarybedstatus_);
  }
  secondarybedstatus_ = secondarybedstatus;
  if (secondarybedstatus) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.SystemStatusInfoStatus.secondaryBedStatus)
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::release_secondarybedstatus() {
  
  ::motor_control::BedStatus* temp = secondarybedstatus_;
  secondarybedstatus_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::unsafe_arena_release_secondarybedstatus() {
  // @@protoc_insertion_point(field_release:motor_control.SystemStatusInfoStatus.secondaryBedStatus)
  
  ::motor_control::BedStatus* temp = secondarybedstatus_;
  secondarybedstatus_ = nullptr;
  return temp;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::_internal_mutable_secondarybedstatus() {
  
  if (secondarybedstatus_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::BedStatus>(GetArenaForAllocation());
    secondarybedstatus_ = p;
  }
  return secondarybedstatus_;
}
inline ::motor_control::BedStatus* SystemStatusInfoStatus::mutable_secondarybedstatus() {
  ::motor_control::BedStatus* _msg = _internal_mutable_secondarybedstatus();
  // @@protoc_insertion_point(field_mutable:motor_control.SystemStatusInfoStatus.secondaryBedStatus)
  return _msg;
}
inline void SystemStatusInfoStatus::set_allocated_secondarybedstatus(::motor_control::BedStatus* secondarybedstatus) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete secondarybedstatus_;
  }
  if (secondarybedstatus) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::BedStatus>::GetOwningArena(secondarybedstatus);
    if (message_arena != submessage_arena) {
      secondarybedstatus = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, secondarybedstatus, submessage_arena);
    }
    
  } else {
    
  }
  secondarybedstatus_ = secondarybedstatus;
  // @@protoc_insertion_point(field_set_allocated:motor_control.SystemStatusInfoStatus.secondaryBedStatus)
}

// .motor_control.HostType ownership = 5;
inline void SystemStatusInfoStatus::clear_ownership() {
  ownership_ = 0;
}
inline ::motor_control::HostType SystemStatusInfoStatus::_internal_ownership() const {
  return static_cast< ::motor_control::HostType >(ownership_);
}
inline ::motor_control::HostType SystemStatusInfoStatus::ownership() const {
  // @@protoc_insertion_point(field_get:motor_control.SystemStatusInfoStatus.ownership)
  return _internal_ownership();
}
inline void SystemStatusInfoStatus::_internal_set_ownership(::motor_control::HostType value) {
  
  ownership_ = value;
}
inline void SystemStatusInfoStatus::set_ownership(::motor_control::HostType value) {
  _internal_set_ownership(value);
  // @@protoc_insertion_point(field_set:motor_control.SystemStatusInfoStatus.ownership)
}

// -------------------------------------------------------------------

// GetTriggerInfoDescription

// string contextUID = 1;
inline void GetTriggerInfoDescription::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& GetTriggerInfoDescription::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.GetTriggerInfoDescription.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetTriggerInfoDescription::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.GetTriggerInfoDescription.contextUID)
}
inline std::string* GetTriggerInfoDescription::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.GetTriggerInfoDescription.contextUID)
  return _s;
}
inline const std::string& GetTriggerInfoDescription::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void GetTriggerInfoDescription::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetTriggerInfoDescription::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetTriggerInfoDescription::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.GetTriggerInfoDescription.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetTriggerInfoDescription::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.GetTriggerInfoDescription.contextUID)
}

// -------------------------------------------------------------------

// TriggerInfoStatus

// string contextUID = 1;
inline void TriggerInfoStatus::clear_contextuid() {
  contextuid_.ClearToEmpty();
}
inline const std::string& TriggerInfoStatus::contextuid() const {
  // @@protoc_insertion_point(field_get:motor_control.TriggerInfoStatus.contextUID)
  return _internal_contextuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TriggerInfoStatus::set_contextuid(ArgT0&& arg0, ArgT... args) {
 
 contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.TriggerInfoStatus.contextUID)
}
inline std::string* TriggerInfoStatus::mutable_contextuid() {
  std::string* _s = _internal_mutable_contextuid();
  // @@protoc_insertion_point(field_mutable:motor_control.TriggerInfoStatus.contextUID)
  return _s;
}
inline const std::string& TriggerInfoStatus::_internal_contextuid() const {
  return contextuid_.Get();
}
inline void TriggerInfoStatus::_internal_set_contextuid(const std::string& value) {
  
  contextuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TriggerInfoStatus::_internal_mutable_contextuid() {
  
  return contextuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TriggerInfoStatus::release_contextuid() {
  // @@protoc_insertion_point(field_release:motor_control.TriggerInfoStatus.contextUID)
  return contextuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TriggerInfoStatus::set_allocated_contextuid(std::string* contextuid) {
  if (contextuid != nullptr) {
    
  } else {
    
  }
  contextuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), contextuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (contextuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    contextuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.TriggerInfoStatus.contextUID)
}

// uint64 errorCode = 2;
inline void TriggerInfoStatus::clear_errorcode() {
  errorcode_ = uint64_t{0u};
}
inline uint64_t TriggerInfoStatus::_internal_errorcode() const {
  return errorcode_;
}
inline uint64_t TriggerInfoStatus::errorcode() const {
  // @@protoc_insertion_point(field_get:motor_control.TriggerInfoStatus.errorCode)
  return _internal_errorcode();
}
inline void TriggerInfoStatus::_internal_set_errorcode(uint64_t value) {
  
  errorcode_ = value;
}
inline void TriggerInfoStatus::set_errorcode(uint64_t value) {
  _internal_set_errorcode(value);
  // @@protoc_insertion_point(field_set:motor_control.TriggerInfoStatus.errorCode)
}

// float triggerPosition = 3;
inline void TriggerInfoStatus::clear_triggerposition() {
  triggerposition_ = 0;
}
inline float TriggerInfoStatus::_internal_triggerposition() const {
  return triggerposition_;
}
inline float TriggerInfoStatus::triggerposition() const {
  // @@protoc_insertion_point(field_get:motor_control.TriggerInfoStatus.triggerPosition)
  return _internal_triggerposition();
}
inline void TriggerInfoStatus::_internal_set_triggerposition(float value) {
  
  triggerposition_ = value;
}
inline void TriggerInfoStatus::set_triggerposition(float value) {
  _internal_set_triggerposition(value);
  // @@protoc_insertion_point(field_set:motor_control.TriggerInfoStatus.triggerPosition)
}

// uint64 triggerTimestamp = 4;
inline void TriggerInfoStatus::clear_triggertimestamp() {
  triggertimestamp_ = uint64_t{0u};
}
inline uint64_t TriggerInfoStatus::_internal_triggertimestamp() const {
  return triggertimestamp_;
}
inline uint64_t TriggerInfoStatus::triggertimestamp() const {
  // @@protoc_insertion_point(field_get:motor_control.TriggerInfoStatus.triggerTimestamp)
  return _internal_triggertimestamp();
}
inline void TriggerInfoStatus::_internal_set_triggertimestamp(uint64_t value) {
  
  triggertimestamp_ = value;
}
inline void TriggerInfoStatus::set_triggertimestamp(uint64_t value) {
  _internal_set_triggertimestamp(value);
  // @@protoc_insertion_point(field_set:motor_control.TriggerInfoStatus.triggerTimestamp)
}

// uint32 exposureTime = 5;
inline void TriggerInfoStatus::clear_exposuretime() {
  exposuretime_ = 0u;
}
inline uint32_t TriggerInfoStatus::_internal_exposuretime() const {
  return exposuretime_;
}
inline uint32_t TriggerInfoStatus::exposuretime() const {
  // @@protoc_insertion_point(field_get:motor_control.TriggerInfoStatus.exposureTime)
  return _internal_exposuretime();
}
inline void TriggerInfoStatus::_internal_set_exposuretime(uint32_t value) {
  
  exposuretime_ = value;
}
inline void TriggerInfoStatus::set_exposuretime(uint32_t value) {
  _internal_set_exposuretime(value);
  // @@protoc_insertion_point(field_set:motor_control.TriggerInfoStatus.exposureTime)
}

// -------------------------------------------------------------------

// MotionInfo

// float postion = 1;
inline void MotionInfo::clear_postion() {
  postion_ = 0;
}
inline float MotionInfo::_internal_postion() const {
  return postion_;
}
inline float MotionInfo::postion() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionInfo.postion)
  return _internal_postion();
}
inline void MotionInfo::_internal_set_postion(float value) {
  
  postion_ = value;
}
inline void MotionInfo::set_postion(float value) {
  _internal_set_postion(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionInfo.postion)
}

// float velocity = 2;
inline void MotionInfo::clear_velocity() {
  velocity_ = 0;
}
inline float MotionInfo::_internal_velocity() const {
  return velocity_;
}
inline float MotionInfo::velocity() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionInfo.velocity)
  return _internal_velocity();
}
inline void MotionInfo::_internal_set_velocity(float value) {
  
  velocity_ = value;
}
inline void MotionInfo::set_velocity(float value) {
  _internal_set_velocity(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionInfo.velocity)
}

// -------------------------------------------------------------------

// MotionCapability

// float positionMin = 1;
inline void MotionCapability::clear_positionmin() {
  positionmin_ = 0;
}
inline float MotionCapability::_internal_positionmin() const {
  return positionmin_;
}
inline float MotionCapability::positionmin() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.positionMin)
  return _internal_positionmin();
}
inline void MotionCapability::_internal_set_positionmin(float value) {
  
  positionmin_ = value;
}
inline void MotionCapability::set_positionmin(float value) {
  _internal_set_positionmin(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.positionMin)
}

// float positionMax = 2;
inline void MotionCapability::clear_positionmax() {
  positionmax_ = 0;
}
inline float MotionCapability::_internal_positionmax() const {
  return positionmax_;
}
inline float MotionCapability::positionmax() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.positionMax)
  return _internal_positionmax();
}
inline void MotionCapability::_internal_set_positionmax(float value) {
  
  positionmax_ = value;
}
inline void MotionCapability::set_positionmax(float value) {
  _internal_set_positionmax(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.positionMax)
}

// float velocityMin = 3;
inline void MotionCapability::clear_velocitymin() {
  velocitymin_ = 0;
}
inline float MotionCapability::_internal_velocitymin() const {
  return velocitymin_;
}
inline float MotionCapability::velocitymin() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.velocityMin)
  return _internal_velocitymin();
}
inline void MotionCapability::_internal_set_velocitymin(float value) {
  
  velocitymin_ = value;
}
inline void MotionCapability::set_velocitymin(float value) {
  _internal_set_velocitymin(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.velocityMin)
}

// float velocityMax = 4;
inline void MotionCapability::clear_velocitymax() {
  velocitymax_ = 0;
}
inline float MotionCapability::_internal_velocitymax() const {
  return velocitymax_;
}
inline float MotionCapability::velocitymax() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.velocityMax)
  return _internal_velocitymax();
}
inline void MotionCapability::_internal_set_velocitymax(float value) {
  
  velocitymax_ = value;
}
inline void MotionCapability::set_velocitymax(float value) {
  _internal_set_velocitymax(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.velocityMax)
}

// float accelerationMax = 5;
inline void MotionCapability::clear_accelerationmax() {
  accelerationmax_ = 0;
}
inline float MotionCapability::_internal_accelerationmax() const {
  return accelerationmax_;
}
inline float MotionCapability::accelerationmax() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.accelerationMax)
  return _internal_accelerationmax();
}
inline void MotionCapability::_internal_set_accelerationmax(float value) {
  
  accelerationmax_ = value;
}
inline void MotionCapability::set_accelerationmax(float value) {
  _internal_set_accelerationmax(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.accelerationMax)
}

// float decelerationMax = 6;
inline void MotionCapability::clear_decelerationmax() {
  decelerationmax_ = 0;
}
inline float MotionCapability::_internal_decelerationmax() const {
  return decelerationmax_;
}
inline float MotionCapability::decelerationmax() const {
  // @@protoc_insertion_point(field_get:motor_control.MotionCapability.decelerationMax)
  return _internal_decelerationmax();
}
inline void MotionCapability::_internal_set_decelerationmax(float value) {
  
  decelerationmax_ = value;
}
inline void MotionCapability::set_decelerationmax(float value) {
  _internal_set_decelerationmax(value);
  // @@protoc_insertion_point(field_set:motor_control.MotionCapability.decelerationMax)
}

// -------------------------------------------------------------------

// BedStatus

// .motor_control.MotionInfo motionInfo = 1;
inline bool BedStatus::_internal_has_motioninfo() const {
  return this != internal_default_instance() && motioninfo_ != nullptr;
}
inline bool BedStatus::has_motioninfo() const {
  return _internal_has_motioninfo();
}
inline void BedStatus::clear_motioninfo() {
  if (GetArenaForAllocation() == nullptr && motioninfo_ != nullptr) {
    delete motioninfo_;
  }
  motioninfo_ = nullptr;
}
inline const ::motor_control::MotionInfo& BedStatus::_internal_motioninfo() const {
  const ::motor_control::MotionInfo* p = motioninfo_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::MotionInfo&>(
      ::motor_control::_MotionInfo_default_instance_);
}
inline const ::motor_control::MotionInfo& BedStatus::motioninfo() const {
  // @@protoc_insertion_point(field_get:motor_control.BedStatus.motionInfo)
  return _internal_motioninfo();
}
inline void BedStatus::unsafe_arena_set_allocated_motioninfo(
    ::motor_control::MotionInfo* motioninfo) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(motioninfo_);
  }
  motioninfo_ = motioninfo;
  if (motioninfo) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.BedStatus.motionInfo)
}
inline ::motor_control::MotionInfo* BedStatus::release_motioninfo() {
  
  ::motor_control::MotionInfo* temp = motioninfo_;
  motioninfo_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::MotionInfo* BedStatus::unsafe_arena_release_motioninfo() {
  // @@protoc_insertion_point(field_release:motor_control.BedStatus.motionInfo)
  
  ::motor_control::MotionInfo* temp = motioninfo_;
  motioninfo_ = nullptr;
  return temp;
}
inline ::motor_control::MotionInfo* BedStatus::_internal_mutable_motioninfo() {
  
  if (motioninfo_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::MotionInfo>(GetArenaForAllocation());
    motioninfo_ = p;
  }
  return motioninfo_;
}
inline ::motor_control::MotionInfo* BedStatus::mutable_motioninfo() {
  ::motor_control::MotionInfo* _msg = _internal_mutable_motioninfo();
  // @@protoc_insertion_point(field_mutable:motor_control.BedStatus.motionInfo)
  return _msg;
}
inline void BedStatus::set_allocated_motioninfo(::motor_control::MotionInfo* motioninfo) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete motioninfo_;
  }
  if (motioninfo) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::MotionInfo>::GetOwningArena(motioninfo);
    if (message_arena != submessage_arena) {
      motioninfo = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, motioninfo, submessage_arena);
    }
    
  } else {
    
  }
  motioninfo_ = motioninfo;
  // @@protoc_insertion_point(field_set_allocated:motor_control.BedStatus.motionInfo)
}

// .motor_control.MotionStatus motionStatus = 2;
inline void BedStatus::clear_motionstatus() {
  motionstatus_ = 0;
}
inline ::motor_control::MotionStatus BedStatus::_internal_motionstatus() const {
  return static_cast< ::motor_control::MotionStatus >(motionstatus_);
}
inline ::motor_control::MotionStatus BedStatus::motionstatus() const {
  // @@protoc_insertion_point(field_get:motor_control.BedStatus.motionStatus)
  return _internal_motionstatus();
}
inline void BedStatus::_internal_set_motionstatus(::motor_control::MotionStatus value) {
  
  motionstatus_ = value;
}
inline void BedStatus::set_motionstatus(::motor_control::MotionStatus value) {
  _internal_set_motionstatus(value);
  // @@protoc_insertion_point(field_set:motor_control.BedStatus.motionStatus)
}

// .motor_control.MotionCapability motionCapability = 3;
inline bool BedStatus::_internal_has_motioncapability() const {
  return this != internal_default_instance() && motioncapability_ != nullptr;
}
inline bool BedStatus::has_motioncapability() const {
  return _internal_has_motioncapability();
}
inline void BedStatus::clear_motioncapability() {
  if (GetArenaForAllocation() == nullptr && motioncapability_ != nullptr) {
    delete motioncapability_;
  }
  motioncapability_ = nullptr;
}
inline const ::motor_control::MotionCapability& BedStatus::_internal_motioncapability() const {
  const ::motor_control::MotionCapability* p = motioncapability_;
  return p != nullptr ? *p : reinterpret_cast<const ::motor_control::MotionCapability&>(
      ::motor_control::_MotionCapability_default_instance_);
}
inline const ::motor_control::MotionCapability& BedStatus::motioncapability() const {
  // @@protoc_insertion_point(field_get:motor_control.BedStatus.motionCapability)
  return _internal_motioncapability();
}
inline void BedStatus::unsafe_arena_set_allocated_motioncapability(
    ::motor_control::MotionCapability* motioncapability) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(motioncapability_);
  }
  motioncapability_ = motioncapability;
  if (motioncapability) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:motor_control.BedStatus.motionCapability)
}
inline ::motor_control::MotionCapability* BedStatus::release_motioncapability() {
  
  ::motor_control::MotionCapability* temp = motioncapability_;
  motioncapability_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::motor_control::MotionCapability* BedStatus::unsafe_arena_release_motioncapability() {
  // @@protoc_insertion_point(field_release:motor_control.BedStatus.motionCapability)
  
  ::motor_control::MotionCapability* temp = motioncapability_;
  motioncapability_ = nullptr;
  return temp;
}
inline ::motor_control::MotionCapability* BedStatus::_internal_mutable_motioncapability() {
  
  if (motioncapability_ == nullptr) {
    auto* p = CreateMaybeMessage<::motor_control::MotionCapability>(GetArenaForAllocation());
    motioncapability_ = p;
  }
  return motioncapability_;
}
inline ::motor_control::MotionCapability* BedStatus::mutable_motioncapability() {
  ::motor_control::MotionCapability* _msg = _internal_mutable_motioncapability();
  // @@protoc_insertion_point(field_mutable:motor_control.BedStatus.motionCapability)
  return _msg;
}
inline void BedStatus::set_allocated_motioncapability(::motor_control::MotionCapability* motioncapability) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete motioncapability_;
  }
  if (motioncapability) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::motor_control::MotionCapability>::GetOwningArena(motioncapability);
    if (message_arena != submessage_arena) {
      motioncapability = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, motioncapability, submessage_arena);
    }
    
  } else {
    
  }
  motioncapability_ = motioncapability;
  // @@protoc_insertion_point(field_set_allocated:motor_control.BedStatus.motionCapability)
}

// -------------------------------------------------------------------

// PostIdInfo

// uint32 VID = 1;
inline void PostIdInfo::clear_vid() {
  vid_ = 0u;
}
inline uint32_t PostIdInfo::_internal_vid() const {
  return vid_;
}
inline uint32_t PostIdInfo::vid() const {
  // @@protoc_insertion_point(field_get:motor_control.PostIdInfo.VID)
  return _internal_vid();
}
inline void PostIdInfo::_internal_set_vid(uint32_t value) {
  
  vid_ = value;
}
inline void PostIdInfo::set_vid(uint32_t value) {
  _internal_set_vid(value);
  // @@protoc_insertion_point(field_set:motor_control.PostIdInfo.VID)
}

// uint32 DID = 2;
inline void PostIdInfo::clear_did() {
  did_ = 0u;
}
inline uint32_t PostIdInfo::_internal_did() const {
  return did_;
}
inline uint32_t PostIdInfo::did() const {
  // @@protoc_insertion_point(field_get:motor_control.PostIdInfo.DID)
  return _internal_did();
}
inline void PostIdInfo::_internal_set_did(uint32_t value) {
  
  did_ = value;
}
inline void PostIdInfo::set_did(uint32_t value) {
  _internal_set_did(value);
  // @@protoc_insertion_point(field_set:motor_control.PostIdInfo.DID)
}

// uint32 HWID = 3;
inline void PostIdInfo::clear_hwid() {
  hwid_ = 0u;
}
inline uint32_t PostIdInfo::_internal_hwid() const {
  return hwid_;
}
inline uint32_t PostIdInfo::hwid() const {
  // @@protoc_insertion_point(field_get:motor_control.PostIdInfo.HWID)
  return _internal_hwid();
}
inline void PostIdInfo::_internal_set_hwid(uint32_t value) {
  
  hwid_ = value;
}
inline void PostIdInfo::set_hwid(uint32_t value) {
  _internal_set_hwid(value);
  // @@protoc_insertion_point(field_set:motor_control.PostIdInfo.HWID)
}

// uint32 RID = 4;
inline void PostIdInfo::clear_rid() {
  rid_ = 0u;
}
inline uint32_t PostIdInfo::_internal_rid() const {
  return rid_;
}
inline uint32_t PostIdInfo::rid() const {
  // @@protoc_insertion_point(field_get:motor_control.PostIdInfo.RID)
  return _internal_rid();
}
inline void PostIdInfo::_internal_set_rid(uint32_t value) {
  
  rid_ = value;
}
inline void PostIdInfo::set_rid(uint32_t value) {
  _internal_set_rid(value);
  // @@protoc_insertion_point(field_set:motor_control.PostIdInfo.RID)
}

// -------------------------------------------------------------------

// MotorControlRequest

// uint32 node_id = 1;
inline void MotorControlRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t MotorControlRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t MotorControlRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorControlRequest.node_id)
  return _internal_node_id();
}
inline void MotorControlRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void MotorControlRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorControlRequest.node_id)
}

// bool enable = 2;
inline void MotorControlRequest::clear_enable() {
  enable_ = false;
}
inline bool MotorControlRequest::_internal_enable() const {
  return enable_;
}
inline bool MotorControlRequest::enable() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorControlRequest.enable)
  return _internal_enable();
}
inline void MotorControlRequest::_internal_set_enable(bool value) {
  
  enable_ = value;
}
inline void MotorControlRequest::set_enable(bool value) {
  _internal_set_enable(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorControlRequest.enable)
}

// -------------------------------------------------------------------

// MotorControlResponse

// bool success = 1;
inline void MotorControlResponse::clear_success() {
  success_ = false;
}
inline bool MotorControlResponse::_internal_success() const {
  return success_;
}
inline bool MotorControlResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorControlResponse.success)
  return _internal_success();
}
inline void MotorControlResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void MotorControlResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorControlResponse.success)
}

// string message = 2;
inline void MotorControlResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& MotorControlResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorControlResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MotorControlResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.MotorControlResponse.message)
}
inline std::string* MotorControlResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.MotorControlResponse.message)
  return _s;
}
inline const std::string& MotorControlResponse::_internal_message() const {
  return message_.Get();
}
inline void MotorControlResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MotorControlResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MotorControlResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.MotorControlResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MotorControlResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.MotorControlResponse.message)
}

// .motor_control.DriveState state = 3;
inline void MotorControlResponse::clear_state() {
  state_ = 0;
}
inline ::motor_control::DriveState MotorControlResponse::_internal_state() const {
  return static_cast< ::motor_control::DriveState >(state_);
}
inline ::motor_control::DriveState MotorControlResponse::state() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorControlResponse.state)
  return _internal_state();
}
inline void MotorControlResponse::_internal_set_state(::motor_control::DriveState value) {
  
  state_ = value;
}
inline void MotorControlResponse::set_state(::motor_control::DriveState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorControlResponse.state)
}

// -------------------------------------------------------------------

// MotorStatusRequest

// uint32 node_id = 1;
inline void MotorStatusRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t MotorStatusRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t MotorStatusRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusRequest.node_id)
  return _internal_node_id();
}
inline void MotorStatusRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void MotorStatusRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusRequest.node_id)
}

// -------------------------------------------------------------------

// MotorStatusResponse

// bool success = 1;
inline void MotorStatusResponse::clear_success() {
  success_ = false;
}
inline bool MotorStatusResponse::_internal_success() const {
  return success_;
}
inline bool MotorStatusResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.success)
  return _internal_success();
}
inline void MotorStatusResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void MotorStatusResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.success)
}

// string message = 2;
inline void MotorStatusResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& MotorStatusResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MotorStatusResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.message)
}
inline std::string* MotorStatusResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.MotorStatusResponse.message)
  return _s;
}
inline const std::string& MotorStatusResponse::_internal_message() const {
  return message_.Get();
}
inline void MotorStatusResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MotorStatusResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MotorStatusResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.MotorStatusResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MotorStatusResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.MotorStatusResponse.message)
}

// .motor_control.DriveState state = 3;
inline void MotorStatusResponse::clear_state() {
  state_ = 0;
}
inline ::motor_control::DriveState MotorStatusResponse::_internal_state() const {
  return static_cast< ::motor_control::DriveState >(state_);
}
inline ::motor_control::DriveState MotorStatusResponse::state() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.state)
  return _internal_state();
}
inline void MotorStatusResponse::_internal_set_state(::motor_control::DriveState value) {
  
  state_ = value;
}
inline void MotorStatusResponse::set_state(::motor_control::DriveState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.state)
}

// int32 actual_position = 4;
inline void MotorStatusResponse::clear_actual_position() {
  actual_position_ = 0;
}
inline int32_t MotorStatusResponse::_internal_actual_position() const {
  return actual_position_;
}
inline int32_t MotorStatusResponse::actual_position() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.actual_position)
  return _internal_actual_position();
}
inline void MotorStatusResponse::_internal_set_actual_position(int32_t value) {
  
  actual_position_ = value;
}
inline void MotorStatusResponse::set_actual_position(int32_t value) {
  _internal_set_actual_position(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.actual_position)
}

// int32 actual_velocity = 5;
inline void MotorStatusResponse::clear_actual_velocity() {
  actual_velocity_ = 0;
}
inline int32_t MotorStatusResponse::_internal_actual_velocity() const {
  return actual_velocity_;
}
inline int32_t MotorStatusResponse::actual_velocity() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.actual_velocity)
  return _internal_actual_velocity();
}
inline void MotorStatusResponse::_internal_set_actual_velocity(int32_t value) {
  
  actual_velocity_ = value;
}
inline void MotorStatusResponse::set_actual_velocity(int32_t value) {
  _internal_set_actual_velocity(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.actual_velocity)
}

// int32 actual_torque = 6;
inline void MotorStatusResponse::clear_actual_torque() {
  actual_torque_ = 0;
}
inline int32_t MotorStatusResponse::_internal_actual_torque() const {
  return actual_torque_;
}
inline int32_t MotorStatusResponse::actual_torque() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.actual_torque)
  return _internal_actual_torque();
}
inline void MotorStatusResponse::_internal_set_actual_torque(int32_t value) {
  
  actual_torque_ = value;
}
inline void MotorStatusResponse::set_actual_torque(int32_t value) {
  _internal_set_actual_torque(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.actual_torque)
}

// .motor_control.OperationMode current_mode = 7;
inline void MotorStatusResponse::clear_current_mode() {
  current_mode_ = 0;
}
inline ::motor_control::OperationMode MotorStatusResponse::_internal_current_mode() const {
  return static_cast< ::motor_control::OperationMode >(current_mode_);
}
inline ::motor_control::OperationMode MotorStatusResponse::current_mode() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.current_mode)
  return _internal_current_mode();
}
inline void MotorStatusResponse::_internal_set_current_mode(::motor_control::OperationMode value) {
  
  current_mode_ = value;
}
inline void MotorStatusResponse::set_current_mode(::motor_control::OperationMode value) {
  _internal_set_current_mode(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.current_mode)
}

// uint32 status_word = 8;
inline void MotorStatusResponse::clear_status_word() {
  status_word_ = 0u;
}
inline uint32_t MotorStatusResponse::_internal_status_word() const {
  return status_word_;
}
inline uint32_t MotorStatusResponse::status_word() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.status_word)
  return _internal_status_word();
}
inline void MotorStatusResponse::_internal_set_status_word(uint32_t value) {
  
  status_word_ = value;
}
inline void MotorStatusResponse::set_status_word(uint32_t value) {
  _internal_set_status_word(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.status_word)
}

// bool is_target_reached = 9;
inline void MotorStatusResponse::clear_is_target_reached() {
  is_target_reached_ = false;
}
inline bool MotorStatusResponse::_internal_is_target_reached() const {
  return is_target_reached_;
}
inline bool MotorStatusResponse::is_target_reached() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.is_target_reached)
  return _internal_is_target_reached();
}
inline void MotorStatusResponse::_internal_set_is_target_reached(bool value) {
  
  is_target_reached_ = value;
}
inline void MotorStatusResponse::set_is_target_reached(bool value) {
  _internal_set_is_target_reached(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.is_target_reached)
}

// bool has_fault = 10;
inline void MotorStatusResponse::clear_has_fault() {
  has_fault_ = false;
}
inline bool MotorStatusResponse::_internal_has_fault() const {
  return has_fault_;
}
inline bool MotorStatusResponse::has_fault() const {
  // @@protoc_insertion_point(field_get:motor_control.MotorStatusResponse.has_fault)
  return _internal_has_fault();
}
inline void MotorStatusResponse::_internal_set_has_fault(bool value) {
  
  has_fault_ = value;
}
inline void MotorStatusResponse::set_has_fault(bool value) {
  _internal_set_has_fault(value);
  // @@protoc_insertion_point(field_set:motor_control.MotorStatusResponse.has_fault)
}

// -------------------------------------------------------------------

// OperationModeRequest

// uint32 node_id = 1;
inline void OperationModeRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t OperationModeRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t OperationModeRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.OperationModeRequest.node_id)
  return _internal_node_id();
}
inline void OperationModeRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void OperationModeRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.OperationModeRequest.node_id)
}

// .motor_control.OperationMode mode = 2;
inline void OperationModeRequest::clear_mode() {
  mode_ = 0;
}
inline ::motor_control::OperationMode OperationModeRequest::_internal_mode() const {
  return static_cast< ::motor_control::OperationMode >(mode_);
}
inline ::motor_control::OperationMode OperationModeRequest::mode() const {
  // @@protoc_insertion_point(field_get:motor_control.OperationModeRequest.mode)
  return _internal_mode();
}
inline void OperationModeRequest::_internal_set_mode(::motor_control::OperationMode value) {
  
  mode_ = value;
}
inline void OperationModeRequest::set_mode(::motor_control::OperationMode value) {
  _internal_set_mode(value);
  // @@protoc_insertion_point(field_set:motor_control.OperationModeRequest.mode)
}

// -------------------------------------------------------------------

// OperationModeResponse

// bool success = 1;
inline void OperationModeResponse::clear_success() {
  success_ = false;
}
inline bool OperationModeResponse::_internal_success() const {
  return success_;
}
inline bool OperationModeResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.OperationModeResponse.success)
  return _internal_success();
}
inline void OperationModeResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void OperationModeResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.OperationModeResponse.success)
}

// string message = 2;
inline void OperationModeResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& OperationModeResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.OperationModeResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void OperationModeResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.OperationModeResponse.message)
}
inline std::string* OperationModeResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.OperationModeResponse.message)
  return _s;
}
inline const std::string& OperationModeResponse::_internal_message() const {
  return message_.Get();
}
inline void OperationModeResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* OperationModeResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* OperationModeResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.OperationModeResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void OperationModeResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.OperationModeResponse.message)
}

// .motor_control.OperationMode current_mode = 3;
inline void OperationModeResponse::clear_current_mode() {
  current_mode_ = 0;
}
inline ::motor_control::OperationMode OperationModeResponse::_internal_current_mode() const {
  return static_cast< ::motor_control::OperationMode >(current_mode_);
}
inline ::motor_control::OperationMode OperationModeResponse::current_mode() const {
  // @@protoc_insertion_point(field_get:motor_control.OperationModeResponse.current_mode)
  return _internal_current_mode();
}
inline void OperationModeResponse::_internal_set_current_mode(::motor_control::OperationMode value) {
  
  current_mode_ = value;
}
inline void OperationModeResponse::set_current_mode(::motor_control::OperationMode value) {
  _internal_set_current_mode(value);
  // @@protoc_insertion_point(field_set:motor_control.OperationModeResponse.current_mode)
}

// -------------------------------------------------------------------

// PositionControlRequest

// uint32 node_id = 1;
inline void PositionControlRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t PositionControlRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t PositionControlRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.node_id)
  return _internal_node_id();
}
inline void PositionControlRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void PositionControlRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.node_id)
}

// int32 target_position = 2;
inline void PositionControlRequest::clear_target_position() {
  target_position_ = 0;
}
inline int32_t PositionControlRequest::_internal_target_position() const {
  return target_position_;
}
inline int32_t PositionControlRequest::target_position() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.target_position)
  return _internal_target_position();
}
inline void PositionControlRequest::_internal_set_target_position(int32_t value) {
  
  target_position_ = value;
}
inline void PositionControlRequest::set_target_position(int32_t value) {
  _internal_set_target_position(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.target_position)
}

// int32 profile_velocity = 3;
inline void PositionControlRequest::clear_profile_velocity() {
  profile_velocity_ = 0;
}
inline int32_t PositionControlRequest::_internal_profile_velocity() const {
  return profile_velocity_;
}
inline int32_t PositionControlRequest::profile_velocity() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.profile_velocity)
  return _internal_profile_velocity();
}
inline void PositionControlRequest::_internal_set_profile_velocity(int32_t value) {
  
  profile_velocity_ = value;
}
inline void PositionControlRequest::set_profile_velocity(int32_t value) {
  _internal_set_profile_velocity(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.profile_velocity)
}

// int32 profile_acceleration = 4;
inline void PositionControlRequest::clear_profile_acceleration() {
  profile_acceleration_ = 0;
}
inline int32_t PositionControlRequest::_internal_profile_acceleration() const {
  return profile_acceleration_;
}
inline int32_t PositionControlRequest::profile_acceleration() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.profile_acceleration)
  return _internal_profile_acceleration();
}
inline void PositionControlRequest::_internal_set_profile_acceleration(int32_t value) {
  
  profile_acceleration_ = value;
}
inline void PositionControlRequest::set_profile_acceleration(int32_t value) {
  _internal_set_profile_acceleration(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.profile_acceleration)
}

// int32 profile_deceleration = 5;
inline void PositionControlRequest::clear_profile_deceleration() {
  profile_deceleration_ = 0;
}
inline int32_t PositionControlRequest::_internal_profile_deceleration() const {
  return profile_deceleration_;
}
inline int32_t PositionControlRequest::profile_deceleration() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.profile_deceleration)
  return _internal_profile_deceleration();
}
inline void PositionControlRequest::_internal_set_profile_deceleration(int32_t value) {
  
  profile_deceleration_ = value;
}
inline void PositionControlRequest::set_profile_deceleration(int32_t value) {
  _internal_set_profile_deceleration(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.profile_deceleration)
}

// bool absolute = 6;
inline void PositionControlRequest::clear_absolute() {
  absolute_ = false;
}
inline bool PositionControlRequest::_internal_absolute() const {
  return absolute_;
}
inline bool PositionControlRequest::absolute() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.absolute)
  return _internal_absolute();
}
inline void PositionControlRequest::_internal_set_absolute(bool value) {
  
  absolute_ = value;
}
inline void PositionControlRequest::set_absolute(bool value) {
  _internal_set_absolute(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.absolute)
}

// bool immediate = 7;
inline void PositionControlRequest::clear_immediate() {
  immediate_ = false;
}
inline bool PositionControlRequest::_internal_immediate() const {
  return immediate_;
}
inline bool PositionControlRequest::immediate() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlRequest.immediate)
  return _internal_immediate();
}
inline void PositionControlRequest::_internal_set_immediate(bool value) {
  
  immediate_ = value;
}
inline void PositionControlRequest::set_immediate(bool value) {
  _internal_set_immediate(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlRequest.immediate)
}

// -------------------------------------------------------------------

// PositionControlResponse

// bool success = 1;
inline void PositionControlResponse::clear_success() {
  success_ = false;
}
inline bool PositionControlResponse::_internal_success() const {
  return success_;
}
inline bool PositionControlResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlResponse.success)
  return _internal_success();
}
inline void PositionControlResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void PositionControlResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlResponse.success)
}

// string message = 2;
inline void PositionControlResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& PositionControlResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PositionControlResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.PositionControlResponse.message)
}
inline std::string* PositionControlResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.PositionControlResponse.message)
  return _s;
}
inline const std::string& PositionControlResponse::_internal_message() const {
  return message_.Get();
}
inline void PositionControlResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PositionControlResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PositionControlResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.PositionControlResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PositionControlResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.PositionControlResponse.message)
}

// int32 actual_position = 3;
inline void PositionControlResponse::clear_actual_position() {
  actual_position_ = 0;
}
inline int32_t PositionControlResponse::_internal_actual_position() const {
  return actual_position_;
}
inline int32_t PositionControlResponse::actual_position() const {
  // @@protoc_insertion_point(field_get:motor_control.PositionControlResponse.actual_position)
  return _internal_actual_position();
}
inline void PositionControlResponse::_internal_set_actual_position(int32_t value) {
  
  actual_position_ = value;
}
inline void PositionControlResponse::set_actual_position(int32_t value) {
  _internal_set_actual_position(value);
  // @@protoc_insertion_point(field_set:motor_control.PositionControlResponse.actual_position)
}

// -------------------------------------------------------------------

// VelocityControlRequest

// uint32 node_id = 1;
inline void VelocityControlRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t VelocityControlRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t VelocityControlRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlRequest.node_id)
  return _internal_node_id();
}
inline void VelocityControlRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void VelocityControlRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlRequest.node_id)
}

// int32 target_velocity = 2;
inline void VelocityControlRequest::clear_target_velocity() {
  target_velocity_ = 0;
}
inline int32_t VelocityControlRequest::_internal_target_velocity() const {
  return target_velocity_;
}
inline int32_t VelocityControlRequest::target_velocity() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlRequest.target_velocity)
  return _internal_target_velocity();
}
inline void VelocityControlRequest::_internal_set_target_velocity(int32_t value) {
  
  target_velocity_ = value;
}
inline void VelocityControlRequest::set_target_velocity(int32_t value) {
  _internal_set_target_velocity(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlRequest.target_velocity)
}

// int32 profile_acceleration = 3;
inline void VelocityControlRequest::clear_profile_acceleration() {
  profile_acceleration_ = 0;
}
inline int32_t VelocityControlRequest::_internal_profile_acceleration() const {
  return profile_acceleration_;
}
inline int32_t VelocityControlRequest::profile_acceleration() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlRequest.profile_acceleration)
  return _internal_profile_acceleration();
}
inline void VelocityControlRequest::_internal_set_profile_acceleration(int32_t value) {
  
  profile_acceleration_ = value;
}
inline void VelocityControlRequest::set_profile_acceleration(int32_t value) {
  _internal_set_profile_acceleration(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlRequest.profile_acceleration)
}

// int32 profile_deceleration = 4;
inline void VelocityControlRequest::clear_profile_deceleration() {
  profile_deceleration_ = 0;
}
inline int32_t VelocityControlRequest::_internal_profile_deceleration() const {
  return profile_deceleration_;
}
inline int32_t VelocityControlRequest::profile_deceleration() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlRequest.profile_deceleration)
  return _internal_profile_deceleration();
}
inline void VelocityControlRequest::_internal_set_profile_deceleration(int32_t value) {
  
  profile_deceleration_ = value;
}
inline void VelocityControlRequest::set_profile_deceleration(int32_t value) {
  _internal_set_profile_deceleration(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlRequest.profile_deceleration)
}

// -------------------------------------------------------------------

// VelocityControlResponse

// bool success = 1;
inline void VelocityControlResponse::clear_success() {
  success_ = false;
}
inline bool VelocityControlResponse::_internal_success() const {
  return success_;
}
inline bool VelocityControlResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlResponse.success)
  return _internal_success();
}
inline void VelocityControlResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void VelocityControlResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlResponse.success)
}

// string message = 2;
inline void VelocityControlResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& VelocityControlResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VelocityControlResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlResponse.message)
}
inline std::string* VelocityControlResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.VelocityControlResponse.message)
  return _s;
}
inline const std::string& VelocityControlResponse::_internal_message() const {
  return message_.Get();
}
inline void VelocityControlResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* VelocityControlResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* VelocityControlResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.VelocityControlResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void VelocityControlResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.VelocityControlResponse.message)
}

// int32 actual_velocity = 3;
inline void VelocityControlResponse::clear_actual_velocity() {
  actual_velocity_ = 0;
}
inline int32_t VelocityControlResponse::_internal_actual_velocity() const {
  return actual_velocity_;
}
inline int32_t VelocityControlResponse::actual_velocity() const {
  // @@protoc_insertion_point(field_get:motor_control.VelocityControlResponse.actual_velocity)
  return _internal_actual_velocity();
}
inline void VelocityControlResponse::_internal_set_actual_velocity(int32_t value) {
  
  actual_velocity_ = value;
}
inline void VelocityControlResponse::set_actual_velocity(int32_t value) {
  _internal_set_actual_velocity(value);
  // @@protoc_insertion_point(field_set:motor_control.VelocityControlResponse.actual_velocity)
}

// -------------------------------------------------------------------

// TorqueControlRequest

// uint32 node_id = 1;
inline void TorqueControlRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t TorqueControlRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t TorqueControlRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlRequest.node_id)
  return _internal_node_id();
}
inline void TorqueControlRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void TorqueControlRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlRequest.node_id)
}

// int32 target_torque = 2;
inline void TorqueControlRequest::clear_target_torque() {
  target_torque_ = 0;
}
inline int32_t TorqueControlRequest::_internal_target_torque() const {
  return target_torque_;
}
inline int32_t TorqueControlRequest::target_torque() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlRequest.target_torque)
  return _internal_target_torque();
}
inline void TorqueControlRequest::_internal_set_target_torque(int32_t value) {
  
  target_torque_ = value;
}
inline void TorqueControlRequest::set_target_torque(int32_t value) {
  _internal_set_target_torque(value);
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlRequest.target_torque)
}

// int32 torque_slope = 3;
inline void TorqueControlRequest::clear_torque_slope() {
  torque_slope_ = 0;
}
inline int32_t TorqueControlRequest::_internal_torque_slope() const {
  return torque_slope_;
}
inline int32_t TorqueControlRequest::torque_slope() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlRequest.torque_slope)
  return _internal_torque_slope();
}
inline void TorqueControlRequest::_internal_set_torque_slope(int32_t value) {
  
  torque_slope_ = value;
}
inline void TorqueControlRequest::set_torque_slope(int32_t value) {
  _internal_set_torque_slope(value);
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlRequest.torque_slope)
}

// -------------------------------------------------------------------

// TorqueControlResponse

// bool success = 1;
inline void TorqueControlResponse::clear_success() {
  success_ = false;
}
inline bool TorqueControlResponse::_internal_success() const {
  return success_;
}
inline bool TorqueControlResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlResponse.success)
  return _internal_success();
}
inline void TorqueControlResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void TorqueControlResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlResponse.success)
}

// string message = 2;
inline void TorqueControlResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& TorqueControlResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TorqueControlResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlResponse.message)
}
inline std::string* TorqueControlResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.TorqueControlResponse.message)
  return _s;
}
inline const std::string& TorqueControlResponse::_internal_message() const {
  return message_.Get();
}
inline void TorqueControlResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TorqueControlResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TorqueControlResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.TorqueControlResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TorqueControlResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.TorqueControlResponse.message)
}

// int32 actual_torque = 3;
inline void TorqueControlResponse::clear_actual_torque() {
  actual_torque_ = 0;
}
inline int32_t TorqueControlResponse::_internal_actual_torque() const {
  return actual_torque_;
}
inline int32_t TorqueControlResponse::actual_torque() const {
  // @@protoc_insertion_point(field_get:motor_control.TorqueControlResponse.actual_torque)
  return _internal_actual_torque();
}
inline void TorqueControlResponse::_internal_set_actual_torque(int32_t value) {
  
  actual_torque_ = value;
}
inline void TorqueControlResponse::set_actual_torque(int32_t value) {
  _internal_set_actual_torque(value);
  // @@protoc_insertion_point(field_set:motor_control.TorqueControlResponse.actual_torque)
}

// -------------------------------------------------------------------

// HomingRequest

// uint32 node_id = 1;
inline void HomingRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t HomingRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t HomingRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingRequest.node_id)
  return _internal_node_id();
}
inline void HomingRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void HomingRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingRequest.node_id)
}

// int32 homing_method = 2;
inline void HomingRequest::clear_homing_method() {
  homing_method_ = 0;
}
inline int32_t HomingRequest::_internal_homing_method() const {
  return homing_method_;
}
inline int32_t HomingRequest::homing_method() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingRequest.homing_method)
  return _internal_homing_method();
}
inline void HomingRequest::_internal_set_homing_method(int32_t value) {
  
  homing_method_ = value;
}
inline void HomingRequest::set_homing_method(int32_t value) {
  _internal_set_homing_method(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingRequest.homing_method)
}

// int32 homing_speed_switch = 3;
inline void HomingRequest::clear_homing_speed_switch() {
  homing_speed_switch_ = 0;
}
inline int32_t HomingRequest::_internal_homing_speed_switch() const {
  return homing_speed_switch_;
}
inline int32_t HomingRequest::homing_speed_switch() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingRequest.homing_speed_switch)
  return _internal_homing_speed_switch();
}
inline void HomingRequest::_internal_set_homing_speed_switch(int32_t value) {
  
  homing_speed_switch_ = value;
}
inline void HomingRequest::set_homing_speed_switch(int32_t value) {
  _internal_set_homing_speed_switch(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingRequest.homing_speed_switch)
}

// int32 homing_speed_zero = 4;
inline void HomingRequest::clear_homing_speed_zero() {
  homing_speed_zero_ = 0;
}
inline int32_t HomingRequest::_internal_homing_speed_zero() const {
  return homing_speed_zero_;
}
inline int32_t HomingRequest::homing_speed_zero() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingRequest.homing_speed_zero)
  return _internal_homing_speed_zero();
}
inline void HomingRequest::_internal_set_homing_speed_zero(int32_t value) {
  
  homing_speed_zero_ = value;
}
inline void HomingRequest::set_homing_speed_zero(int32_t value) {
  _internal_set_homing_speed_zero(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingRequest.homing_speed_zero)
}

// int32 homing_acceleration = 5;
inline void HomingRequest::clear_homing_acceleration() {
  homing_acceleration_ = 0;
}
inline int32_t HomingRequest::_internal_homing_acceleration() const {
  return homing_acceleration_;
}
inline int32_t HomingRequest::homing_acceleration() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingRequest.homing_acceleration)
  return _internal_homing_acceleration();
}
inline void HomingRequest::_internal_set_homing_acceleration(int32_t value) {
  
  homing_acceleration_ = value;
}
inline void HomingRequest::set_homing_acceleration(int32_t value) {
  _internal_set_homing_acceleration(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingRequest.homing_acceleration)
}

// -------------------------------------------------------------------

// HomingResponse

// bool success = 1;
inline void HomingResponse::clear_success() {
  success_ = false;
}
inline bool HomingResponse::_internal_success() const {
  return success_;
}
inline bool HomingResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingResponse.success)
  return _internal_success();
}
inline void HomingResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void HomingResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingResponse.success)
}

// string message = 2;
inline void HomingResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& HomingResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HomingResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.HomingResponse.message)
}
inline std::string* HomingResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.HomingResponse.message)
  return _s;
}
inline const std::string& HomingResponse::_internal_message() const {
  return message_.Get();
}
inline void HomingResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HomingResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HomingResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.HomingResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HomingResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.HomingResponse.message)
}

// bool homing_completed = 3;
inline void HomingResponse::clear_homing_completed() {
  homing_completed_ = false;
}
inline bool HomingResponse::_internal_homing_completed() const {
  return homing_completed_;
}
inline bool HomingResponse::homing_completed() const {
  // @@protoc_insertion_point(field_get:motor_control.HomingResponse.homing_completed)
  return _internal_homing_completed();
}
inline void HomingResponse::_internal_set_homing_completed(bool value) {
  
  homing_completed_ = value;
}
inline void HomingResponse::set_homing_completed(bool value) {
  _internal_set_homing_completed(value);
  // @@protoc_insertion_point(field_set:motor_control.HomingResponse.homing_completed)
}

// -------------------------------------------------------------------

// EmergencyStopRequest

// uint32 node_id = 1;
inline void EmergencyStopRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t EmergencyStopRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t EmergencyStopRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.EmergencyStopRequest.node_id)
  return _internal_node_id();
}
inline void EmergencyStopRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void EmergencyStopRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.EmergencyStopRequest.node_id)
}

// -------------------------------------------------------------------

// EmergencyStopResponse

// bool success = 1;
inline void EmergencyStopResponse::clear_success() {
  success_ = false;
}
inline bool EmergencyStopResponse::_internal_success() const {
  return success_;
}
inline bool EmergencyStopResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.EmergencyStopResponse.success)
  return _internal_success();
}
inline void EmergencyStopResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void EmergencyStopResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.EmergencyStopResponse.success)
}

// string message = 2;
inline void EmergencyStopResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& EmergencyStopResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.EmergencyStopResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EmergencyStopResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.EmergencyStopResponse.message)
}
inline std::string* EmergencyStopResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.EmergencyStopResponse.message)
  return _s;
}
inline const std::string& EmergencyStopResponse::_internal_message() const {
  return message_.Get();
}
inline void EmergencyStopResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* EmergencyStopResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* EmergencyStopResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.EmergencyStopResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void EmergencyStopResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.EmergencyStopResponse.message)
}

// -------------------------------------------------------------------

// ClearFaultRequest

// uint32 node_id = 1;
inline void ClearFaultRequest::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t ClearFaultRequest::_internal_node_id() const {
  return node_id_;
}
inline uint32_t ClearFaultRequest::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.ClearFaultRequest.node_id)
  return _internal_node_id();
}
inline void ClearFaultRequest::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void ClearFaultRequest::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.ClearFaultRequest.node_id)
}

// -------------------------------------------------------------------

// ClearFaultResponse

// bool success = 1;
inline void ClearFaultResponse::clear_success() {
  success_ = false;
}
inline bool ClearFaultResponse::_internal_success() const {
  return success_;
}
inline bool ClearFaultResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.ClearFaultResponse.success)
  return _internal_success();
}
inline void ClearFaultResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void ClearFaultResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.ClearFaultResponse.success)
}

// string message = 2;
inline void ClearFaultResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& ClearFaultResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.ClearFaultResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ClearFaultResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.ClearFaultResponse.message)
}
inline std::string* ClearFaultResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.ClearFaultResponse.message)
  return _s;
}
inline const std::string& ClearFaultResponse::_internal_message() const {
  return message_.Get();
}
inline void ClearFaultResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ClearFaultResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ClearFaultResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.ClearFaultResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ClearFaultResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.ClearFaultResponse.message)
}

// bool fault_cleared = 3;
inline void ClearFaultResponse::clear_fault_cleared() {
  fault_cleared_ = false;
}
inline bool ClearFaultResponse::_internal_fault_cleared() const {
  return fault_cleared_;
}
inline bool ClearFaultResponse::fault_cleared() const {
  // @@protoc_insertion_point(field_get:motor_control.ClearFaultResponse.fault_cleared)
  return _internal_fault_cleared();
}
inline void ClearFaultResponse::_internal_set_fault_cleared(bool value) {
  
  fault_cleared_ = value;
}
inline void ClearFaultResponse::set_fault_cleared(bool value) {
  _internal_set_fault_cleared(value);
  // @@protoc_insertion_point(field_set:motor_control.ClearFaultResponse.fault_cleared)
}

// -------------------------------------------------------------------

// GetAllNodesRequest

// -------------------------------------------------------------------

// NodeInfo

// uint32 node_id = 1;
inline void NodeInfo::clear_node_id() {
  node_id_ = 0u;
}
inline uint32_t NodeInfo::_internal_node_id() const {
  return node_id_;
}
inline uint32_t NodeInfo::node_id() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.node_id)
  return _internal_node_id();
}
inline void NodeInfo::_internal_set_node_id(uint32_t value) {
  
  node_id_ = value;
}
inline void NodeInfo::set_node_id(uint32_t value) {
  _internal_set_node_id(value);
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.node_id)
}

// bool is_connected = 2;
inline void NodeInfo::clear_is_connected() {
  is_connected_ = false;
}
inline bool NodeInfo::_internal_is_connected() const {
  return is_connected_;
}
inline bool NodeInfo::is_connected() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.is_connected)
  return _internal_is_connected();
}
inline void NodeInfo::_internal_set_is_connected(bool value) {
  
  is_connected_ = value;
}
inline void NodeInfo::set_is_connected(bool value) {
  _internal_set_is_connected(value);
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.is_connected)
}

// .motor_control.DriveState state = 3;
inline void NodeInfo::clear_state() {
  state_ = 0;
}
inline ::motor_control::DriveState NodeInfo::_internal_state() const {
  return static_cast< ::motor_control::DriveState >(state_);
}
inline ::motor_control::DriveState NodeInfo::state() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.state)
  return _internal_state();
}
inline void NodeInfo::_internal_set_state(::motor_control::DriveState value) {
  
  state_ = value;
}
inline void NodeInfo::set_state(::motor_control::DriveState value) {
  _internal_set_state(value);
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.state)
}

// string vendor_name = 4;
inline void NodeInfo::clear_vendor_name() {
  vendor_name_.ClearToEmpty();
}
inline const std::string& NodeInfo::vendor_name() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.vendor_name)
  return _internal_vendor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeInfo::set_vendor_name(ArgT0&& arg0, ArgT... args) {
 
 vendor_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.vendor_name)
}
inline std::string* NodeInfo::mutable_vendor_name() {
  std::string* _s = _internal_mutable_vendor_name();
  // @@protoc_insertion_point(field_mutable:motor_control.NodeInfo.vendor_name)
  return _s;
}
inline const std::string& NodeInfo::_internal_vendor_name() const {
  return vendor_name_.Get();
}
inline void NodeInfo::_internal_set_vendor_name(const std::string& value) {
  
  vendor_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeInfo::_internal_mutable_vendor_name() {
  
  return vendor_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeInfo::release_vendor_name() {
  // @@protoc_insertion_point(field_release:motor_control.NodeInfo.vendor_name)
  return vendor_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NodeInfo::set_allocated_vendor_name(std::string* vendor_name) {
  if (vendor_name != nullptr) {
    
  } else {
    
  }
  vendor_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), vendor_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (vendor_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    vendor_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.NodeInfo.vendor_name)
}

// string product_name = 5;
inline void NodeInfo::clear_product_name() {
  product_name_.ClearToEmpty();
}
inline const std::string& NodeInfo::product_name() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.product_name)
  return _internal_product_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeInfo::set_product_name(ArgT0&& arg0, ArgT... args) {
 
 product_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.product_name)
}
inline std::string* NodeInfo::mutable_product_name() {
  std::string* _s = _internal_mutable_product_name();
  // @@protoc_insertion_point(field_mutable:motor_control.NodeInfo.product_name)
  return _s;
}
inline const std::string& NodeInfo::_internal_product_name() const {
  return product_name_.Get();
}
inline void NodeInfo::_internal_set_product_name(const std::string& value) {
  
  product_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeInfo::_internal_mutable_product_name() {
  
  return product_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeInfo::release_product_name() {
  // @@protoc_insertion_point(field_release:motor_control.NodeInfo.product_name)
  return product_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NodeInfo::set_allocated_product_name(std::string* product_name) {
  if (product_name != nullptr) {
    
  } else {
    
  }
  product_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), product_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (product_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    product_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.NodeInfo.product_name)
}

// string revision_number = 6;
inline void NodeInfo::clear_revision_number() {
  revision_number_.ClearToEmpty();
}
inline const std::string& NodeInfo::revision_number() const {
  // @@protoc_insertion_point(field_get:motor_control.NodeInfo.revision_number)
  return _internal_revision_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NodeInfo::set_revision_number(ArgT0&& arg0, ArgT... args) {
 
 revision_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.NodeInfo.revision_number)
}
inline std::string* NodeInfo::mutable_revision_number() {
  std::string* _s = _internal_mutable_revision_number();
  // @@protoc_insertion_point(field_mutable:motor_control.NodeInfo.revision_number)
  return _s;
}
inline const std::string& NodeInfo::_internal_revision_number() const {
  return revision_number_.Get();
}
inline void NodeInfo::_internal_set_revision_number(const std::string& value) {
  
  revision_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* NodeInfo::_internal_mutable_revision_number() {
  
  return revision_number_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* NodeInfo::release_revision_number() {
  // @@protoc_insertion_point(field_release:motor_control.NodeInfo.revision_number)
  return revision_number_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void NodeInfo::set_allocated_revision_number(std::string* revision_number) {
  if (revision_number != nullptr) {
    
  } else {
    
  }
  revision_number_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), revision_number,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (revision_number_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    revision_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.NodeInfo.revision_number)
}

// -------------------------------------------------------------------

// GetAllNodesResponse

// bool success = 1;
inline void GetAllNodesResponse::clear_success() {
  success_ = false;
}
inline bool GetAllNodesResponse::_internal_success() const {
  return success_;
}
inline bool GetAllNodesResponse::success() const {
  // @@protoc_insertion_point(field_get:motor_control.GetAllNodesResponse.success)
  return _internal_success();
}
inline void GetAllNodesResponse::_internal_set_success(bool value) {
  
  success_ = value;
}
inline void GetAllNodesResponse::set_success(bool value) {
  _internal_set_success(value);
  // @@protoc_insertion_point(field_set:motor_control.GetAllNodesResponse.success)
}

// string message = 2;
inline void GetAllNodesResponse::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& GetAllNodesResponse::message() const {
  // @@protoc_insertion_point(field_get:motor_control.GetAllNodesResponse.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetAllNodesResponse::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.GetAllNodesResponse.message)
}
inline std::string* GetAllNodesResponse::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:motor_control.GetAllNodesResponse.message)
  return _s;
}
inline const std::string& GetAllNodesResponse::_internal_message() const {
  return message_.Get();
}
inline void GetAllNodesResponse::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetAllNodesResponse::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetAllNodesResponse::release_message() {
  // @@protoc_insertion_point(field_release:motor_control.GetAllNodesResponse.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetAllNodesResponse::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.GetAllNodesResponse.message)
}

// repeated .motor_control.NodeInfo nodes = 3;
inline int GetAllNodesResponse::_internal_nodes_size() const {
  return nodes_.size();
}
inline int GetAllNodesResponse::nodes_size() const {
  return _internal_nodes_size();
}
inline void GetAllNodesResponse::clear_nodes() {
  nodes_.Clear();
}
inline ::motor_control::NodeInfo* GetAllNodesResponse::mutable_nodes(int index) {
  // @@protoc_insertion_point(field_mutable:motor_control.GetAllNodesResponse.nodes)
  return nodes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::NodeInfo >*
GetAllNodesResponse::mutable_nodes() {
  // @@protoc_insertion_point(field_mutable_list:motor_control.GetAllNodesResponse.nodes)
  return &nodes_;
}
inline const ::motor_control::NodeInfo& GetAllNodesResponse::_internal_nodes(int index) const {
  return nodes_.Get(index);
}
inline const ::motor_control::NodeInfo& GetAllNodesResponse::nodes(int index) const {
  // @@protoc_insertion_point(field_get:motor_control.GetAllNodesResponse.nodes)
  return _internal_nodes(index);
}
inline ::motor_control::NodeInfo* GetAllNodesResponse::_internal_add_nodes() {
  return nodes_.Add();
}
inline ::motor_control::NodeInfo* GetAllNodesResponse::add_nodes() {
  ::motor_control::NodeInfo* _add = _internal_add_nodes();
  // @@protoc_insertion_point(field_add:motor_control.GetAllNodesResponse.nodes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::NodeInfo >&
GetAllNodesResponse::nodes() const {
  // @@protoc_insertion_point(field_list:motor_control.GetAllNodesResponse.nodes)
  return nodes_;
}

// -------------------------------------------------------------------

// DeviceConfig

// string device = 1;
inline void DeviceConfig::clear_device() {
  device_.ClearToEmpty();
}
inline const std::string& DeviceConfig::device() const {
  // @@protoc_insertion_point(field_get:motor_control.DeviceConfig.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceConfig::set_device(ArgT0&& arg0, ArgT... args) {
 
 device_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.DeviceConfig.device)
}
inline std::string* DeviceConfig::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:motor_control.DeviceConfig.device)
  return _s;
}
inline const std::string& DeviceConfig::_internal_device() const {
  return device_.Get();
}
inline void DeviceConfig::_internal_set_device(const std::string& value) {
  
  device_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeviceConfig::_internal_mutable_device() {
  
  return device_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeviceConfig::release_device() {
  // @@protoc_insertion_point(field_release:motor_control.DeviceConfig.device)
  return device_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeviceConfig::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  device_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), device,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (device_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    device_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.DeviceConfig.device)
}

// string name = 2;
inline void DeviceConfig::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DeviceConfig::name() const {
  // @@protoc_insertion_point(field_get:motor_control.DeviceConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceConfig::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.DeviceConfig.name)
}
inline std::string* DeviceConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:motor_control.DeviceConfig.name)
  return _s;
}
inline const std::string& DeviceConfig::_internal_name() const {
  return name_.Get();
}
inline void DeviceConfig::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeviceConfig::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeviceConfig::release_name() {
  // @@protoc_insertion_point(field_release:motor_control.DeviceConfig.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeviceConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.DeviceConfig.name)
}

// -------------------------------------------------------------------

// ConfigMessage

// string can_interface = 1;
inline void ConfigMessage::clear_can_interface() {
  can_interface_.ClearToEmpty();
}
inline const std::string& ConfigMessage::can_interface() const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.can_interface)
  return _internal_can_interface();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigMessage::set_can_interface(ArgT0&& arg0, ArgT... args) {
 
 can_interface_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.ConfigMessage.can_interface)
}
inline std::string* ConfigMessage::mutable_can_interface() {
  std::string* _s = _internal_mutable_can_interface();
  // @@protoc_insertion_point(field_mutable:motor_control.ConfigMessage.can_interface)
  return _s;
}
inline const std::string& ConfigMessage::_internal_can_interface() const {
  return can_interface_.Get();
}
inline void ConfigMessage::_internal_set_can_interface(const std::string& value) {
  
  can_interface_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigMessage::_internal_mutable_can_interface() {
  
  return can_interface_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigMessage::release_can_interface() {
  // @@protoc_insertion_point(field_release:motor_control.ConfigMessage.can_interface)
  return can_interface_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigMessage::set_allocated_can_interface(std::string* can_interface) {
  if (can_interface != nullptr) {
    
  } else {
    
  }
  can_interface_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), can_interface,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (can_interface_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    can_interface_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.ConfigMessage.can_interface)
}

// string grpc_server_address = 2;
inline void ConfigMessage::clear_grpc_server_address() {
  grpc_server_address_.ClearToEmpty();
}
inline const std::string& ConfigMessage::grpc_server_address() const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.grpc_server_address)
  return _internal_grpc_server_address();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigMessage::set_grpc_server_address(ArgT0&& arg0, ArgT... args) {
 
 grpc_server_address_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:motor_control.ConfigMessage.grpc_server_address)
}
inline std::string* ConfigMessage::mutable_grpc_server_address() {
  std::string* _s = _internal_mutable_grpc_server_address();
  // @@protoc_insertion_point(field_mutable:motor_control.ConfigMessage.grpc_server_address)
  return _s;
}
inline const std::string& ConfigMessage::_internal_grpc_server_address() const {
  return grpc_server_address_.Get();
}
inline void ConfigMessage::_internal_set_grpc_server_address(const std::string& value) {
  
  grpc_server_address_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigMessage::_internal_mutable_grpc_server_address() {
  
  return grpc_server_address_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigMessage::release_grpc_server_address() {
  // @@protoc_insertion_point(field_release:motor_control.ConfigMessage.grpc_server_address)
  return grpc_server_address_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigMessage::set_allocated_grpc_server_address(std::string* grpc_server_address) {
  if (grpc_server_address != nullptr) {
    
  } else {
    
  }
  grpc_server_address_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), grpc_server_address,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (grpc_server_address_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    grpc_server_address_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:motor_control.ConfigMessage.grpc_server_address)
}

// int32 status_socket_port = 3;
inline void ConfigMessage::clear_status_socket_port() {
  status_socket_port_ = 0;
}
inline int32_t ConfigMessage::_internal_status_socket_port() const {
  return status_socket_port_;
}
inline int32_t ConfigMessage::status_socket_port() const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.status_socket_port)
  return _internal_status_socket_port();
}
inline void ConfigMessage::_internal_set_status_socket_port(int32_t value) {
  
  status_socket_port_ = value;
}
inline void ConfigMessage::set_status_socket_port(int32_t value) {
  _internal_set_status_socket_port(value);
  // @@protoc_insertion_point(field_set:motor_control.ConfigMessage.status_socket_port)
}

// repeated uint32 node_ids = 4;
inline int ConfigMessage::_internal_node_ids_size() const {
  return node_ids_.size();
}
inline int ConfigMessage::node_ids_size() const {
  return _internal_node_ids_size();
}
inline void ConfigMessage::clear_node_ids() {
  node_ids_.Clear();
}
inline uint32_t ConfigMessage::_internal_node_ids(int index) const {
  return node_ids_.Get(index);
}
inline uint32_t ConfigMessage::node_ids(int index) const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.node_ids)
  return _internal_node_ids(index);
}
inline void ConfigMessage::set_node_ids(int index, uint32_t value) {
  node_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:motor_control.ConfigMessage.node_ids)
}
inline void ConfigMessage::_internal_add_node_ids(uint32_t value) {
  node_ids_.Add(value);
}
inline void ConfigMessage::add_node_ids(uint32_t value) {
  _internal_add_node_ids(value);
  // @@protoc_insertion_point(field_add:motor_control.ConfigMessage.node_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
ConfigMessage::_internal_node_ids() const {
  return node_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
ConfigMessage::node_ids() const {
  // @@protoc_insertion_point(field_list:motor_control.ConfigMessage.node_ids)
  return _internal_node_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
ConfigMessage::_internal_mutable_node_ids() {
  return &node_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
ConfigMessage::mutable_node_ids() {
  // @@protoc_insertion_point(field_mutable_list:motor_control.ConfigMessage.node_ids)
  return _internal_mutable_node_ids();
}

// repeated .motor_control.DeviceConfig uart_devices = 5;
inline int ConfigMessage::_internal_uart_devices_size() const {
  return uart_devices_.size();
}
inline int ConfigMessage::uart_devices_size() const {
  return _internal_uart_devices_size();
}
inline void ConfigMessage::clear_uart_devices() {
  uart_devices_.Clear();
}
inline ::motor_control::DeviceConfig* ConfigMessage::mutable_uart_devices(int index) {
  // @@protoc_insertion_point(field_mutable:motor_control.ConfigMessage.uart_devices)
  return uart_devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >*
ConfigMessage::mutable_uart_devices() {
  // @@protoc_insertion_point(field_mutable_list:motor_control.ConfigMessage.uart_devices)
  return &uart_devices_;
}
inline const ::motor_control::DeviceConfig& ConfigMessage::_internal_uart_devices(int index) const {
  return uart_devices_.Get(index);
}
inline const ::motor_control::DeviceConfig& ConfigMessage::uart_devices(int index) const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.uart_devices)
  return _internal_uart_devices(index);
}
inline ::motor_control::DeviceConfig* ConfigMessage::_internal_add_uart_devices() {
  return uart_devices_.Add();
}
inline ::motor_control::DeviceConfig* ConfigMessage::add_uart_devices() {
  ::motor_control::DeviceConfig* _add = _internal_add_uart_devices();
  // @@protoc_insertion_point(field_add:motor_control.ConfigMessage.uart_devices)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >&
ConfigMessage::uart_devices() const {
  // @@protoc_insertion_point(field_list:motor_control.ConfigMessage.uart_devices)
  return uart_devices_;
}

// repeated .motor_control.DeviceConfig i2c_devices = 6;
inline int ConfigMessage::_internal_i2c_devices_size() const {
  return i2c_devices_.size();
}
inline int ConfigMessage::i2c_devices_size() const {
  return _internal_i2c_devices_size();
}
inline void ConfigMessage::clear_i2c_devices() {
  i2c_devices_.Clear();
}
inline ::motor_control::DeviceConfig* ConfigMessage::mutable_i2c_devices(int index) {
  // @@protoc_insertion_point(field_mutable:motor_control.ConfigMessage.i2c_devices)
  return i2c_devices_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >*
ConfigMessage::mutable_i2c_devices() {
  // @@protoc_insertion_point(field_mutable_list:motor_control.ConfigMessage.i2c_devices)
  return &i2c_devices_;
}
inline const ::motor_control::DeviceConfig& ConfigMessage::_internal_i2c_devices(int index) const {
  return i2c_devices_.Get(index);
}
inline const ::motor_control::DeviceConfig& ConfigMessage::i2c_devices(int index) const {
  // @@protoc_insertion_point(field_get:motor_control.ConfigMessage.i2c_devices)
  return _internal_i2c_devices(index);
}
inline ::motor_control::DeviceConfig* ConfigMessage::_internal_add_i2c_devices() {
  return i2c_devices_.Add();
}
inline ::motor_control::DeviceConfig* ConfigMessage::add_i2c_devices() {
  ::motor_control::DeviceConfig* _add = _internal_add_i2c_devices();
  // @@protoc_insertion_point(field_add:motor_control.ConfigMessage.i2c_devices)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::motor_control::DeviceConfig >&
ConfigMessage::i2c_devices() const {
  // @@protoc_insertion_point(field_list:motor_control.ConfigMessage.i2c_devices)
  return i2c_devices_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace motor_control

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::motor_control::BedType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::BedType>() {
  return ::motor_control::BedType_descriptor();
}
template <> struct is_proto_enum< ::motor_control::MotionMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::MotionMode>() {
  return ::motor_control::MotionMode_descriptor();
}
template <> struct is_proto_enum< ::motor_control::MotionStatus> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::MotionStatus>() {
  return ::motor_control::MotionStatus_descriptor();
}
template <> struct is_proto_enum< ::motor_control::HostType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::HostType>() {
  return ::motor_control::HostType_descriptor();
}
template <> struct is_proto_enum< ::motor_control::OperationMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::OperationMode>() {
  return ::motor_control::OperationMode_descriptor();
}
template <> struct is_proto_enum< ::motor_control::DriveState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::motor_control::DriveState>() {
  return ::motor_control::DriveState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_motor_5fcontrol_2eproto
