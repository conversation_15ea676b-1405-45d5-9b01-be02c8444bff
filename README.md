# CANopen电机控制GUI客户端

这是一个基于Python的GUI客户端程序，用于与CANopen电机控制系统进行交互。通过gRPC协议与服务端通信，提供友好的图形用户界面来控制和监控电机。

## 功能特点

- 连接gRPC服务器
- 查看所有节点信息和状态
- 控制电机（使能/禁用）
- 设置操作模式
- 位置控制
- 速度控制
- 扭矩控制
- 回零操作
- 紧急停止
- 故障清除
- 状态监控和自动刷新

## 安装要求

- Python 3.6 或更高版本
- PyQt5
- gRPC

## 安装步骤

1. 克隆或下载此仓库到本地

2. 安装所需的依赖包：

```bash
pip install grpcio grpcio-tools protobuf PyQt5
```

3. 生成Proto文件的Python代码：

```bash
python generate_proto.py
```
  
此命令将根据proto/motor_control.proto文件生成以下两个文件：
- motor_control_pb2.py
- motor_control_pb2_grpc.py

## 使用方法

1. 启动程序：

```bash
python motor_control_gui.py
```

2. 在界面顶部输入gRPC服务器地址（默认为localhost:50051），然后点击"连接"按钮

3. 连接成功后，可以在各个标签页中执行不同的操作：

   - **节点状态**：查看所有已连接的节点信息
   - **电机控制**：选择一个节点，进行基本的电机控制（使能/禁用、设置模式等）
   - **位置控制**：执行位置控制指令
   - **速度控制**：执行速度控制指令
   - **扭矩控制**：执行扭矩控制指令
   - **回零操作**：执行回零指令

## 调试提示

- 如果连接服务器失败，请检查服务器地址是否正确，以及服务器是否已经启动
- 如果看不到节点信息，请点击"刷新节点信息"按钮
- 在执行任何控制操作前，请确保已经成功连接到服务器并选择了一个节点
- 紧急情况下可以使用"紧急停止"按钮立即停止电机

## 项目结构

- `motor_control_gui.py` - 主程序和GUI界面实现
- `generate_proto.py` - 用于生成Proto相关Python文件的脚本
- `proto/motor_control.proto` - gRPC服务和消息定义文件
- `motor_control_pb2.py` - 由proto文件生成的消息类（运行generate_proto.py后生成）
- `motor_control_pb2_grpc.py` - 由proto文件生成的服务类（运行generate_proto.py后生成）

## 问题反馈

如有任何问题或建议，请提交issue或联系管理员。


# 修复windows与linux行尾结束符问题
dos2unix ./Create_Vitrtual_Can_Device.sh
