#ifndef GRPC_SERVER_HPP
#define GRPC_SERVER_HPP

#include <memory>
#include <string>
#include <thread>
#include <atomic>
#include <grpcpp/grpcpp.h>
#include "motor_control.grpc.pb.h"
#include "CANopenMaster.hpp"

// 前向声明ServoControlManager
class ServoControlManager;

class BedMasterAppServiceImpl final : public motor_control::BedMasterAppService::Service {
public:
    explicit BedMasterAppServiceImpl(ServoControlManager *servo_manager = nullptr);

    ~BedMasterAppServiceImpl() override;

    // 开始移床
    grpc::Status StartMove(grpc::ServerContext *context,
                           const motor_control::StartMoveDescription *request,
                           motor_control::StartMoveStatus *response) override;

    // 停止移床
    grpc::Status StopMove(grpc::ServerContext *context,
                          const motor_control::CommonDescription *request,
                          motor_control::CommonStatus *response) override;

    // 获取PostId
    grpc::Status GetPostId(grpc::ServerContext *context,
                           const motor_control::GetPostIdDescription *request,
                           motor_control::GetPostIdStatus *response) override;

    // 获取动物床控制权
    grpc::Status GainControl(grpc::ServerContext *context,
                             const motor_control::CommonDescription *request,
                             motor_control::CommonStatus *response) override;

    // 释放动物床控制权
    grpc::Status ReleaseControl(grpc::ServerContext *context,
                                const motor_control::CommonDescription *request,
                                motor_control::CommonStatus *response) override;

    // 心跳检测
    grpc::Status HeartBeatCheck(grpc::ServerContext *context,
                                const motor_control::CommonDescription *request,
                                motor_control::CommonStatus *response) override;

    // 获取动物床状态
    grpc::Status GetSystemStatusInfo(grpc::ServerContext *context,
                                     const motor_control::CommonDescription *request,
                                     motor_control::SystemStatusInfoStatus *response) override;

    // 获取Trigger信息（双向流）
    grpc::Status GetTriggerInfo(grpc::ServerContext *context,
                                grpc::ServerReaderWriter<motor_control::TriggerInfoStatus,
                                        motor_control::GetTriggerInfoDescription> *stream) override;

private:
    ServoControlManager *servo_manager_; // 伺服电机控制管理器

    // 通信层辅助方法
    motor_control::HostType getClientHostType(grpc::ServerContext *context);

    // 错误处理和日志记录
    void logOperation(const std::string &operation, const std::string &contextUID,
                      motor_control::HostType hostType, bool success, const std::string &details = "");
};

class GrpcServer {
public:
    explicit GrpcServer(std::string server_address, ServoControlManager *servo_manager = nullptr);

    ~GrpcServer();

    // 启动gRPC服务器
    bool start();

    // 停止gRPC服务器
    void stop();

    // 等待服务器终止
    void wait();

private:
    std::string server_address_;
    std::unique_ptr<grpc::Server> server_;
    std::unique_ptr<BedMasterAppServiceImpl> bed_service_;
    std::atomic<bool> running_;
    ServoControlManager *servo_manager_; // 伺服电机控制管理器
};

#endif // GRPC_SERVER_HPP 