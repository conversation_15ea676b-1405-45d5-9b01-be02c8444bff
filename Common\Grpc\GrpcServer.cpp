#include "GrpcServer.hpp"
#include "SyslogManager.hpp"
#include "ProtobufConverter.hpp"
#include "ErrorCodeMapper.hpp"
#include <memory>
#include <string>
#include <chrono>
#include <map>
#include <thread>
#include <utility>
#include "ServoControlManager.hpp"  // 添加ServoControlManager头文件

// BedMasterAppServiceImpl 实现

BedMasterAppServiceImpl::BedMasterAppServiceImpl(ServoControlManager *servo_manager)
        : servo_manager_(servo_manager) {
}

BedMasterAppServiceImpl::~BedMasterAppServiceImpl() = default;

grpc::Status BedMasterAppServiceImpl::StartMove(
        grpc::ServerContext *context,
        const motor_control::StartMoveDescription *request,
        motor_control::StartMoveStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        // 设置响应的contextUID
        response->set_contextuid(contextUID);

        // 步骤1: 检查伺服控制管理器是否可用
        if (!servo_manager_) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            logOperation("StartMove", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status::OK;
        }

        // 步骤2: 参数转换
        BedOwnerType requiredOwner = ProtobufConverter::convertHostType(clientHost);
        BedType bedType = ProtobufConverter::convertBedType(request->bedtype());
        motor_control::MotionMode mode = ProtobufConverter::convertMotionMode(request->mode());

        // 获取位置和速度参数
        const motor_control::MotionInfo &targetInfo = request->targetmotioninfo();
        float position = targetInfo.postion();
        float velocity = targetInfo.velocity();

        // 步骤3: 调用业务层统一的运动模式方法
        bool success = servo_manager_->startMoveBed(bedType, mode, position, velocity, requiredOwner);

        // 步骤4: 设置响应
        if (success) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));

            // 获取床状态并填充响应
            BedStatus bedStatus = servo_manager_->getBedStatus(bedType);
            motor_control::MotionInfo *currentInfo = response->mutable_currentmotioninfo();
            currentInfo->set_postion(bedStatus.motionInfo.position);
            currentInfo->set_velocity(bedStatus.motionInfo.velocity);

            logOperation("StartMove", contextUID, clientHost, true, "移动命令执行成功");
        } else {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::START_MOVE_FAILED));
            logOperation("StartMove", contextUID, clientHost, false, "移动命令执行失败");
        }

        return grpc::Status::OK;
    } catch (const std::exception &e) {
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        logOperation("StartMove", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::StopMove(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        // 设置响应的contextUID
        response->set_contextuid(contextUID);

        // 步骤1: 检查伺服控制管理器是否可用
        if (!servo_manager_) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            logOperation("StopMove", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status::OK;
        }

        // 步骤2: 参数转换
        BedOwnerType requiredOwner = ProtobufConverter::convertHostType(clientHost);

        // 步骤3: 设置停止标志（通知正在运行的StartMove提前结束）
        servo_manager_->setStopFlag(true);

        // 步骤4: 调用业务层停止方法（包含权限检查）
        bool success = servo_manager_->stopMoveBed(BedType::Both, requiredOwner);

        // 步骤5: 设置响应
        if (success) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));
            logOperation("StopMove", contextUID, clientHost, true, "停止命令执行成功");
        } else {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::STOP_OPERATION_FAILED));
            logOperation("StopMove", contextUID, clientHost, false, "停止命令执行失败");
        }

        return grpc::Status::OK;
    } catch (const std::exception &e) {
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        logOperation("StopMove", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetPostId(
        grpc::ServerContext *context,
        const motor_control::GetPostIdDescription *request,
        motor_control::GetPostIdStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        response->set_contextuid(request->contextuid());

        if (!servo_manager_) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            logOperation("GetPostId", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status::OK;
        }

        // 参数转换
        BedType bedType = ProtobufConverter::convertBedType(request->bedtype());

        // 调用业务层获取PostId
        PostIdInfo postIdInfo = servo_manager_->getPostId(bedType);

        // 检查是否获取到有效的PostId
        if (postIdInfo.vid == 0 && postIdInfo.did == 0 && postIdInfo.hwid == 0 && postIdInfo.rid == 0) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::INVALID_PARAMETER));
            logOperation("GetPostId", contextUID, clientHost, false, "无效的床类型或获取PostId失败");
            return grpc::Status::OK;
        }

        // 使用参数转换工具填充PostId信息
        ProtobufConverter::fillPostIdInfo(postIdInfo, response);
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));

        logOperation("GetPostId", contextUID, clientHost, true, "PostId获取成功");
        return grpc::Status::OK;
    } catch (const std::exception &e) {
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        logOperation("GetPostId", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GainControl(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        // 设置响应的contextUID
        response->set_contextuid(contextUID);

        // 步骤1: 检查伺服控制管理器是否可用
        if (!servo_manager_) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            logOperation("GainControl", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status::OK;
        }

        // 步骤2: 参数转换
        BedOwnerType newOwner = ProtobufConverter::convertHostType(clientHost);

        // 步骤3: 调用业务层获取控制权方法（包含权限检查）
        bool success = servo_manager_->gainBedOwner(newOwner);

        // 步骤4: 设置响应
        if (success) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));
            logOperation("GainControl", contextUID, clientHost, true, "成功获取控制权");
        } else {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GAIN_CONTROL_FAILED));
            logOperation("GainControl", contextUID, clientHost, false, "获取控制权失败");
        }

        return grpc::Status::OK;
    } catch (const std::exception &e) {
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        logOperation("GainControl", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::ReleaseControl(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    std::string contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        // 设置响应的contextUID
        response->set_contextuid(contextUID);

        // 步骤1: 检查伺服控制管理器是否可用
        if (!servo_manager_) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            logOperation("ReleaseControl", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status::OK;
        }

        // 步骤2: 参数转换
        BedOwnerType ownerToRelease = ProtobufConverter::convertHostType(clientHost);

        // 步骤3: 调用业务层释放控制权方法（包含权限检查）
        bool success = servo_manager_->releaseBedOwner(ownerToRelease);

        // 步骤4: 设置响应
        if (success) {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));
            logOperation("ReleaseControl", contextUID, clientHost, true, "成功释放控制权");
        } else {
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::RELEASE_CONTROL_FAILED));
            logOperation("ReleaseControl", contextUID, clientHost, false, "释放控制权失败");
        }

        return grpc::Status::OK;
    } catch (const std::exception &e) {
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        logOperation("ReleaseControl", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::HeartBeatCheck(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::CommonStatus *response) {

    const std::string& contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    response->set_contextuid(contextUID);

    try {
        if (!servo_manager_) {
            logOperation("HeartBeatCheck", contextUID, clientHost, false, "伺服控制管理器未初始化");
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SERVO_MANAGER_NULL));
            return grpc::Status::OK;
        }

        // 参数转换
        BedOwnerType ownerType = ProtobufConverter::convertHostType(clientHost);

        // 更新心跳时间戳
        bool success = servo_manager_->updateHeartbeat(ownerType);

        if (success) {
            logOperation("HeartBeatCheck", contextUID, clientHost, true, "心跳更新成功");
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));
            return grpc::Status::OK;
        } else {
            logOperation("HeartBeatCheck", contextUID, clientHost, false, "心跳更新失败");
            response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::PERMISSION_DENIED));
            return grpc::Status::OK;
        }

    } catch (const std::exception& e) {
        logOperation("HeartBeatCheck", contextUID, clientHost, false, std::string("异常: ") + e.what());
        response->set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::GENERAL_ERROR));
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetSystemStatusInfo(
        grpc::ServerContext *context,
        const motor_control::CommonDescription *request,
        motor_control::SystemStatusInfoStatus *response) {

    const std::string& contextUID = request->contextuid();
    motor_control::HostType clientHost = getClientHostType(context);

    try {
        if (!servo_manager_) {
            logOperation("GetSystemStatusInfo", contextUID, clientHost, false, "伺服控制管理器未初始化");
            return grpc::Status(grpc::StatusCode::INTERNAL, "伺服控制管理器未初始化");
        }

        // 获取床状态和当前控制权拥有者
        BedStatus primaryBedStatus = servo_manager_->getBedStatus(BedType::Primary);
        BedStatus secondaryBedStatus = servo_manager_->getBedStatus(BedType::Secondary);
        BedOwnerType currentOwner = servo_manager_->getBedOwner();

        // 使用参数转换工具填充系统状态信息
        ProtobufConverter::fillSystemStatusInfo(primaryBedStatus, secondaryBedStatus, currentOwner, response);

        logOperation("GetSystemStatusInfo", contextUID, clientHost, true, "系统状态获取成功");

        return grpc::Status::OK;
    } catch (const std::exception &e) {
        logOperation("GetSystemStatusInfo", contextUID, clientHost, false, std::string("异常: ") + e.what());
        return grpc::Status(grpc::StatusCode::INTERNAL, e.what());
    }
}

grpc::Status BedMasterAppServiceImpl::GetTriggerInfo(
        grpc::ServerContext *context,
        grpc::ServerReaderWriter<motor_control::TriggerInfoStatus, motor_control::GetTriggerInfoDescription> *stream) {

    LOG_INFO_MSG("BedMasterAppServiceImpl", "Client connected for TriggerInfo streaming.");
    if (!servo_manager_) {
        return grpc::Status{grpc::StatusCode::FAILED_PRECONDITION, "Servo manager is not initialized."};
    }

    // 检查HostType和权限
    auto clientHost = getClientHostType(context);
    BedOwnerType requiredOwner = ProtobufConverter::convertHostType(clientHost);

    if (!servo_manager_->checkBedControlPermission(requiredOwner, "GetTriggerInfo")) {
        // 返回后该gRPC流式请求会立即断开，客户端会收到流结束（EOF），不会再收到后续数据。
        return grpc::Status{grpc::StatusCode::PERMISSION_DENIED, "Permission denied."};
    }

    // 用于从客户端读取线程通知主线程停止的原子标志
    std::atomic<bool> stop_stream(false);
    // 从客户端读取启动流请求
    motor_control::GetTriggerInfoDescription request;
    std::thread reader_thread = std::thread([stream, &stop_stream, context]() {
            motor_control::GetTriggerInfoDescription request;
            // stream->Read() 是一个阻塞操作，当有新消息时才会返回 true
            // 当客户端关闭写入流 (WritesDone) 或者连接断开时，返回 false
            while (stream->Read(&request)) {
                LOG_INFO_MSG("BedMasterAppServiceImpl", "Received message from client with contextUID: %s", request.contextuid().c_str());
            }

            LOG_INFO_MSG("BedMasterAppServiceImpl", "Client has finished sending messages. Signalling writer to stop.");
            stop_stream = true; // 如果客户端关闭流，也应停止
        });
    // }

    // --- 服务器消息写入循环 (在主 RPC 线程中) ---
    TriggerInfo last_known_info = {0, 0, 0, 0, false}; // 初始化一个旧的TriggerInfo

    uint64_t total_messages_sent = 0;
    // 循环等待并发送更新
    while (!context->IsCancelled() && !stop_stream.load()) {
        if (!servo_manager_->waitForTriggerInfoChange(last_known_info, 100)) {
            continue;
        } else {
            // 成功获取到新的Trigger信息
            motor_control::TriggerInfoStatus status_update;
            status_update.set_contextuid(request.contextuid()); // 使用初始请求的contextUID
            status_update.set_errorcode(ErrorCodeMapper::getErrorCode(ErrorCodeMapper::BusinessError::SUCCESS));
            status_update.set_triggerposition((float)last_known_info.position / 1000.f);
            status_update.set_triggertimestamp(last_known_info.triggerTimestamp);
            status_update.set_exposuretime(last_known_info.interval);

            //  Double check to shut down the stream faster.
            if (stop_stream.load()) {
                break;
            }

            // 向客户端发送更新
            if (!stream->Write(status_update)) {
                // 写入失败，可能客户端已断开
                LOG_ERROR_MSG("BedMasterAppServiceImpl", "Failed to write trigger info to client. Client may have disconnected.");
                break;
            } else {
                total_messages_sent++;
            }
        }
        // 如果waitForTriggerInfoChange返回false，说明是超时，循环将继续，并检查context->IsCancelled()
    }

    // 等待读取线程执行结束
    reader_thread.join();
    logOperation("GetTriggerInfo", request.contextuid(), clientHost, true, "Stream finished.");
    LOG_INFO_MSG("BedMasterAppServiceImpl", "Total messages sent from GrpcServer: %lu", total_messages_sent);
    return grpc::Status::OK;
}

// GrpcServer 实现
GrpcServer::GrpcServer(std::string server_address, ServoControlManager *servo_manager)
        : server_address_(std::move(server_address)),
          running_(false),
          servo_manager_(servo_manager){
}

GrpcServer::~GrpcServer() {
    stop();
}

bool GrpcServer::start() {
    if (running_) {
        return true;  // 已经在运行
    }

    try {
        bed_service_ = std::make_unique<BedMasterAppServiceImpl>(servo_manager_);

        // 构建服务器
        grpc::ServerBuilder builder;
        builder.AddListeningPort(server_address_, grpc::InsecureServerCredentials());

        builder.RegisterService(bed_service_.get());

        // 启动服务器
        server_ = builder.BuildAndStart();
        if (!server_) {
            LOG_ERROR_MSG("GrpcServer", "无法启动gRPC服务器");
            return false;
        }

        LOG_INFO_MSG("GrpcServer", "gRPC服务器启动在: %s", server_address_.c_str());
        running_ = true;
        return true;
    } catch (const std::exception &e) {
        LOG_ERROR_MSG("GrpcServer", "启动gRPC服务器时发生异常: %s", e.what());
        return false;
    }
}

void GrpcServer::stop() {
    if (!running_) {
        return;  // 已经停止
    }

    LOG_INFO_MSG("GrpcServer", "正在停止gRPC服务器...");

    if (server_) {
        server_->Shutdown();
        server_.reset();
    }

    running_ = false;
    LOG_INFO_MSG("GrpcServer", "gRPC服务器已停止");
}

void GrpcServer::wait() {
    if (server_ && running_) {
        server_->Wait();
    }
}

// ==== BedMasterAppServiceImpl 辅助方法实现 ====

motor_control::HostType BedMasterAppServiceImpl::getClientHostType(grpc::ServerContext *context) {
    // 从gRPC上下文中获取客户端信息
    // 这里简化实现，实际应该根据客户端连接信息或认证信息来确定
    // 可以通过context->peer()获取客户端地址，或通过metadata获取客户端类型

    auto metadata = context->client_metadata();
    auto it = metadata.find("client-type");
    if (it != metadata.end()) {
        std::string clientType(it->second.data(), it->second.length());
        if (clientType == "CT") return motor_control::HostType::CT;
        if (clientType == "PET") return motor_control::HostType::PET;
        if (clientType == "SPECT") return motor_control::HostType::SPECT;
    }

    // 默认返回类型
    return motor_control::HostType::NONE;
}

void BedMasterAppServiceImpl::logOperation(const std::string &operation, const std::string &contextUID,
                                           motor_control::HostType hostType, bool success, const std::string &details) {
    std::string logMessage = "操作: " + operation +
                           ", contextUID: " + contextUID +
                           ", 主机类型: " + std::to_string(static_cast<int>(hostType)) +
                           ", 结果: " + (success ? "成功" : "失败");

    if (!details.empty()) {
        logMessage += ", 详情: " + details;
    }

    if (success) {
        LOG_INFO_MSG("BedMasterAppServiceImpl", "%s", logMessage.c_str());
    } else {
        LOG_WARNING_MSG("BedMasterAppServiceImpl", "%s", logMessage.c_str());
    }
}

