import sys
import grpc
import threading
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QPushButton, QLabel, QComboBox, QTabWidget,
                            QGroupBox, QFormLayout, QLineEdit, QCheckBox, QMessageBox,
                            QTextEdit, QSplitter, QDoubleSpinBox)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, pyqtSlot

# 导入生成的proto文件
import sys
import os

# 添加proto目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
proto_dir = os.path.join(current_dir, 'proto')
if proto_dir not in sys.path:
    sys.path.insert(0, proto_dir)

try:
    import motor_control_pb2
    import motor_control_pb2_grpc
    print(f"成功导入proto文件: {motor_control_pb2.__file__}")
except ImportError as e:
    print(f"警告: 未找到proto生成的Python文件: {e}")
    print("请先运行 generate_proto.py 生成")
    print("或手动运行: python -m grpc_tools.protoc --proto_path=./proto --python_out=./proto --grpc_python_out=./proto ./proto/motor_control.proto")
    motor_control_pb2 = None
    motor_control_pb2_grpc = None

class MotorControlGUI(QMainWindow):
    """动物床控制GUI主窗口"""

    # 定义信号用于线程间通信
    move_operation_finished = pyqtSignal(bool, str, object)  # success, message, response

    def __init__(self):
        super().__init__()

        # 检查proto文件是否可用
        if motor_control_pb2 is None or motor_control_pb2_grpc is None:
            QMessageBox.critical(None, "错误",
                               "未找到proto生成的Python文件！\n"
                               "请先生成proto文件：\n"
                               "python generate_proto.py")
            sys.exit(1)

        # 默认服务器地址
        self.server_address = "*************:50051"
        self.channel = None
        self.bed_stub = None  # 床控制服务stub
        self.connected = False

        # 连接状态监控
        self.connection_healthy = False
        self.connection_check_failures = 0
        self.max_connection_failures = 3  # 连续失败3次认为连接断开

        # Trigger监控相关
        self.trigger_monitoring = False
        self.trigger_thread = None

        # 创建床状态刷新定时器
        self.bed_status_timer = QTimer(self)
        self.bed_status_timer.timeout.connect(self.update_bed_status)

        # 创建连接状态检测定时器
        self.connection_timer = QTimer(self)
        self.connection_timer.timeout.connect(self.check_connection_status)
        self.connection_timer.setInterval(5000)  # 每5秒检测一次连接状态

        # 连接信号
        self.move_operation_finished.connect(self.on_move_operation_finished)

        # 初始化UI
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("动物床控制测试工具")
        self.setGeometry(100, 100, 1000, 800)

        # 创建主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)

        # 创建连接部分
        connection_group = QGroupBox("gRPC服务器连接")
        connection_layout = QVBoxLayout()

        # 第一行：服务器地址和连接按钮
        addr_layout = QHBoxLayout()
        self.server_address_input = QLineEdit(self.server_address)
        self.connect_button = QPushButton("连接")
        self.connect_button.clicked.connect(self.toggle_connection)
        self.connect_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

        addr_layout.addWidget(QLabel("服务器地址:"))
        addr_layout.addWidget(self.server_address_input)
        addr_layout.addWidget(self.connect_button)

        # 第二行：连接状态指示器
        status_layout = QHBoxLayout()
        self.connection_status_label = QLabel("● 未连接")
        self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")

        self.last_check_label = QLabel("最后检测: --")
        self.last_check_label.setStyleSheet("color: gray; font-size: 10px;")

        status_layout.addWidget(self.connection_status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.last_check_label)

        connection_layout.addLayout(addr_layout)
        connection_layout.addLayout(status_layout)

        connection_group.setLayout(connection_layout)
        main_layout.addWidget(connection_group)

        # 创建主标签页
        self.main_tabs = QTabWidget()
        
        # 床控制标签页
        bed_control_widget = QWidget()
        bed_layout = QVBoxLayout(bed_control_widget)

        # 创建水平分割器
        bed_splitter = QSplitter(Qt.Horizontal)

        # 左侧：床控制面板
        self.init_bed_control_panel(bed_splitter)

        # 右侧：床状态显示
        self.init_bed_status_panel(bed_splitter)

        # 设置分割器比例
        bed_splitter.setSizes([600, 400])
        bed_layout.addWidget(bed_splitter)

        # 添加日志显示区域
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout()

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(150)

        clear_log_button = QPushButton("清除日志")
        clear_log_button.clicked.connect(self.clear_log)

        log_layout.addWidget(self.log_text)
        log_layout.addWidget(clear_log_button)
        log_group.setLayout(log_layout)
        bed_layout.addWidget(log_group)
        
        self.main_tabs.addTab(bed_control_widget, "动物床控制")
        
        main_layout.addWidget(self.main_tabs)

        self.setCentralWidget(central_widget)

        # 加载保存的设置
        self.load_context_settings()

        # 添加日志
        self.log_message("动物床控制GUI初始化完成，请连接到gRPC服务器开始测试")

    def generate_context_uid(self):
        """自动生成ContextUID"""
        import uuid
        timestamp = int(time.time())
        unique_id = str(uuid.uuid4())[:8]
        context_uid = f"gui-{timestamp}-{unique_id}"
        self.context_uid_input.setText(context_uid)
        self.log_message(f"已生成新的Context UID: {context_uid}")

    def get_current_context_info(self):
        """获取当前设置的上下文信息"""
        client_type = self.client_type_selector.currentText()
        context_uid = self.context_uid_input.text().strip()

        if not context_uid:
            # 如果没有设置ContextUID，自动生成一个
            self.generate_context_uid()
            context_uid = self.context_uid_input.text().strip()

        return client_type, context_uid

    def create_grpc_metadata(self):
        """创建包含上下文信息的gRPC metadata"""
        client_type, _ = self.get_current_context_info()
        return [('client-type', client_type)]

    def save_context_settings(self):
        """保存上下文设置到文件"""
        try:
            import json
            settings = {
                "client_type": self.client_type_selector.currentText(),
                "context_uid": self.context_uid_input.text(),
                "server_address": self.server_address
            }

            with open("gui_context_settings.json", "w", encoding="utf-8") as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            self.log_message("已保存上下文设置")
        except Exception as e:
            self.log_message(f"保存设置失败: {str(e)}")

    def load_context_settings(self):
        """从文件加载上下文设置"""
        try:
            import json
            import os

            if os.path.exists("gui_context_settings.json"):
                with open("gui_context_settings.json", "r", encoding="utf-8") as f:
                    settings = json.load(f)

                # 恢复客户端类型
                if "client_type" in settings:
                    index = self.client_type_selector.findText(settings["client_type"])
                    if index >= 0:
                        self.client_type_selector.setCurrentIndex(index)

                # 恢复Context UID
                if "context_uid" in settings:
                    self.context_uid_input.setText(settings["context_uid"])

                # 恢复服务器地址
                if "server_address" in settings:
                    self.server_address = settings["server_address"]

                self.log_message("已加载保存的上下文设置")
        except Exception as e:
            self.log_message(f"加载设置失败: {str(e)}")

    def closeEvent(self, event):
        """窗口关闭时保存设置"""
        self.save_context_settings()
        super().closeEvent(event)

    def check_connection_ready(self):
        """检查连接是否准备就绪"""
        if not self.connected or not self.bed_stub:
            QMessageBox.warning(self, "未连接", "请先连接到gRPC服务器")
            return False

        if not self.connection_healthy:
            QMessageBox.warning(self, "连接不稳定", "gRPC连接不稳定，请等待连接恢复或重新连接")
            return False

        return True

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        if hasattr(self, 'log_text'):
            self.log_text.append(log_entry)
            # 自动滚动到底部
            self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())
        else:
            print(log_entry)  # 如果日志文本框还没创建，输出到控制台

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text'):
            self.log_text.clear()
            self.log_message("日志已清除")

    def toggle_connection(self):
        """连接或断开与gRPC服务器的连接"""
        if not self.connected:
            self.server_address = self.server_address_input.text()
            self.log_message(f"尝试连接到gRPC服务器: {self.server_address}")
            try:
                self.channel = grpc.insecure_channel(self.server_address)
                self.bed_stub = motor_control_pb2_grpc.BedMasterAppServiceStub(self.channel)

                # 测试连接 - 使用床控制服务的心跳检测
                client_type, context_uid = self.get_current_context_info()
                metadata = self.create_grpc_metadata()
                request = motor_control_pb2.CommonDescription(contextUID=f"connect-test-{int(time.time())}")
                response = self.bed_stub.HeartBeatCheck(request, metadata=metadata)

                self.connected = True
                self.connection_healthy = True
                self.connection_check_failures = 0
                self.connect_button.setText("断开连接")
                self.connect_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; }")

                # 更新连接状态显示
                self.update_connection_status_display(True, "连接正常")

                # 启动连接状态监控
                self.connection_timer.start()

                self.log_message(f"成功连接到gRPC服务器: {self.server_address}")
                self.get_bed_system_status()  # 获取动物床状态

                QMessageBox.information(self, "连接成功", f"已成功连接到床控制gRPC服务器: {self.server_address}")
            except Exception as e:
                self.log_message(f"连接失败: {str(e)}")
                QMessageBox.critical(self, "连接错误", f"连接服务器失败: {str(e)}")
        else:
            self.disconnect_from_server()
            QMessageBox.information(self, "断开连接", "已断开与gRPC服务器的连接")

    def disconnect_from_server(self):
        """断开与服务器的连接"""
        # 停止所有定时器
        self.connection_timer.stop()
        self.bed_status_timer.stop()  # 停止床状态刷新

        # 停止Trigger监控
        if self.trigger_monitoring:
            self.stop_trigger_monitoring()

        # 关闭连接
        if self.channel:
            try:
                self.channel.close()
            except:
                pass

        # 重置状态
        self.channel = None
        self.bed_stub = None  # 清除床控制服务stub
        self.connected = False
        self.connection_healthy = False
        self.connection_check_failures = 0

        # 更新UI
        self.connect_button.setText("连接")
        self.connect_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.update_connection_status_display(False, "未连接")

        self.log_message("已断开与gRPC服务器的连接")

    def check_connection_status(self):
        """检测gRPC连接状态"""
        if not self.connected or not self.bed_stub:
            return

        try:
            # # 使用心跳检测来检测连接状态
            # request = motor_control_pb2.CommonDescription(contextUID=f"check_{int(time.time())}")
            # # 设置较短的超时时间
            # response = self.bed_stub.HeartBeatCheck(request, timeout=2.0)

            # # 连接正常
            # if self.connection_check_failures > 0:
            #     self.log_message("gRPC连接已恢复")

            self.connection_healthy = True
            self.connection_check_failures = 0
            self.update_connection_status_display(True, "连接正常")

        except grpc.RpcError as e:
            self.connection_check_failures += 1
            error_msg = f"连接检测失败 ({self.connection_check_failures}/{self.max_connection_failures}): {e.code().name}"

            if self.connection_check_failures >= self.max_connection_failures:
                # 连接已断开
                self.connection_healthy = False
                self.update_connection_status_display(False, "连接断开")
                self.log_message(f"gRPC连接断开: {str(e)}")

                # 询问用户是否重新连接
                reply = QMessageBox.question(self, "连接断开",
                                           f"与gRPC服务器的连接已断开。\n错误: {str(e)}\n\n是否尝试重新连接？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply == QMessageBox.Yes:
                    self.reconnect_to_server()
                else:
                    self.disconnect_from_server()
            else:
                # 连接不稳定
                self.connection_healthy = False
                self.update_connection_status_display(False, f"连接不稳定 ({self.connection_check_failures})")
                self.log_message(error_msg)

        except Exception as e:
            self.connection_check_failures += 1
            self.connection_healthy = False
            error_msg = f"连接检测异常 ({self.connection_check_failures}/{self.max_connection_failures}): {str(e)}"
            self.update_connection_status_display(False, "连接异常")
            self.log_message(error_msg)

    def update_connection_status_display(self, is_healthy, status_text):
        """更新连接状态显示"""
        timestamp = time.strftime("%H:%M:%S")

        if is_healthy:
            self.connection_status_label.setText(f"● {status_text}")
            self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.connection_status_label.setText(f"● {status_text}")
            self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")

        self.last_check_label.setText(f"最后检测: {timestamp}")

    def reconnect_to_server(self):
        """重新连接到服务器"""
        self.log_message("尝试重新连接到gRPC服务器...")

        # 先断开现有连接
        if self.channel:
            try:
                self.channel.close()
            except:
                pass

        # 重置状态
        self.channel = None
        self.bed_stub = None
        self.connected = False
        self.connection_healthy = False
        self.connection_check_failures = 0

        # 尝试重新连接
        try:
            self.channel = grpc.insecure_channel(self.server_address)
            self.bed_stub = motor_control_pb2_grpc.BedMasterAppServiceStub(self.channel)

            # 测试连接
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=f"reconnect_{int(time.time())}")
            response = self.bed_stub.HeartBeatCheck(request, timeout=5.0, metadata=metadata)

            # 重连成功
            self.connected = True
            self.connection_healthy = True
            self.connection_check_failures = 0

            self.update_connection_status_display(True, "重连成功")
            self.log_message("重新连接成功")

            # 重启连接监控
            self.connection_timer.start()

        except Exception as e:
            self.update_connection_status_display(False, "重连失败")
            self.log_message(f"重新连接失败: {str(e)}")

            # 完全断开连接
            self.disconnect_from_server()

    def init_bed_control_panel(self, parent):
        """初始化床控制面板"""
        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)

        # 上下文信息设置组
        context_group = QGroupBox("gRPC上下文信息设置")
        context_layout = QFormLayout()

        # 客户端类型选择
        self.client_type_selector = QComboBox()
        self.client_type_selector.addItems(["CT", "PET", "SPECT", "NONE"])
        self.client_type_selector.setCurrentText("CT")  # 默认值
        context_layout.addRow("客户端类型:", self.client_type_selector)

        # ContextUID设置
        self.context_uid_input = QLineEdit()
        self.context_uid_input.setText("gui-test-001")  # 默认值
        self.context_uid_input.setPlaceholderText("输入唯一的上下文ID")
        context_layout.addRow("Context UID:", self.context_uid_input)

        # 操作按钮
        buttons_layout = QHBoxLayout()

        self.auto_generate_uid_button = QPushButton("自动生成UID")
        self.auto_generate_uid_button.clicked.connect(self.generate_context_uid)
        self.auto_generate_uid_button.setStyleSheet("""
            QPushButton {
                background-color: #9C27B0;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 3px;
            }
        """)

        self.save_settings_button = QPushButton("保存设置")
        self.save_settings_button.clicked.connect(self.save_context_settings)
        self.save_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 3px;
            }
        """)

        self.load_settings_button = QPushButton("加载设置")
        self.load_settings_button.clicked.connect(self.load_context_settings)
        self.load_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 6px;
                border-radius: 3px;
            }
        """)

        buttons_layout.addWidget(self.auto_generate_uid_button)
        buttons_layout.addWidget(self.save_settings_button)
        buttons_layout.addWidget(self.load_settings_button)
        buttons_layout.addStretch()

        context_group.setLayout(context_layout)
        control_layout.addWidget(context_group)
        control_layout.addLayout(buttons_layout)

        # 控制权管理组
        control_group = QGroupBox("控制权管理")
        control_group_layout = QVBoxLayout()
        
        # 控制权选择
        # owner_layout = QHBoxLayout()
        # owner_layout.addWidget(QLabel("控制者:"))
        # self.owner_selector = QComboBox()
        # self.owner_selector.addItems(["CT", "PET", "SPECT"])
        # owner_layout.addWidget(self.owner_selector)
        
        # 获取/释放控制权按钮
        buttons_layout = QHBoxLayout()
        self.gain_control_button = QPushButton("获取控制权")
        self.gain_control_button.clicked.connect(self.gain_bed_control)
        self.gain_control_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)

        self.release_control_button = QPushButton("释放控制权")
        self.release_control_button.clicked.connect(self.release_bed_control)
        self.release_control_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)
        
        buttons_layout.addWidget(self.gain_control_button)
        buttons_layout.addWidget(self.release_control_button)
        
        # control_group_layout.addLayout(owner_layout)
        control_group_layout.addLayout(buttons_layout)
        control_group.setLayout(control_group_layout)
        control_layout.addWidget(control_group)
        
        # 床移动控制组
        move_group = QGroupBox("床移动控制")
        move_group_layout = QVBoxLayout()
        
        # 床类型选择
        bed_type_layout = QHBoxLayout()
        bed_type_layout.addWidget(QLabel("床类型:"))
        self.bed_type_selector = QComboBox()
        self.bed_type_selector.addItems(["Primary (一级床)", "Secondary (二级床)", "Both (两级床)"])
        bed_type_layout.addWidget(self.bed_type_selector)
        
        # 运动模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("运动模式:"))
        self.motion_mode_selector = QComboBox()
        self.motion_mode_selector.addItems(["Position (位置)", "Velocity (速度)", "Homing (回零)"])
        mode_layout.addWidget(self.motion_mode_selector)
        
        # 目标位置和速度
        target_layout = QFormLayout()
        self.target_position = QDoubleSpinBox()
        self.target_position.setRange(0, 100000000)
        self.target_position.setSingleStep(100)
        
        self.target_velocity = QDoubleSpinBox()
        self.target_velocity.setRange(-81920, 81920)
        self.target_velocity.setSingleStep(100)
        
        target_layout.addRow("目标位置(mm):", self.target_position)
        target_layout.addRow("目标速度(mm/s):", self.target_velocity)
        
        # 开始和停止按钮
        move_buttons_layout = QHBoxLayout()
        self.start_move_button = QPushButton("开始移动")
        self.start_move_button.clicked.connect(self.start_bed_move)
        self.start_move_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)

        self.stop_move_button = QPushButton("停止移动")
        self.stop_move_button.clicked.connect(self.stop_bed_move)
        self.stop_move_button.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                font-weight: bold;
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:disabled {
                background-color: #CCCCCC;
                color: #666666;
            }
        """)
        self.stop_move_button.setEnabled(True)  # 初始时禁用停止按钮
        
        move_buttons_layout.addWidget(self.start_move_button)
        move_buttons_layout.addWidget(self.stop_move_button)
        
        move_group_layout.addLayout(bed_type_layout)
        move_group_layout.addLayout(mode_layout)
        move_group_layout.addLayout(target_layout)
        move_group_layout.addLayout(move_buttons_layout)
        move_group.setLayout(move_group_layout)
        control_layout.addWidget(move_group)
        
        # PostId获取组
        postid_group = QGroupBox("PostId获取")
        postid_layout = QVBoxLayout()
        
        # 床类型选择
        postid_bed_layout = QHBoxLayout()
        postid_bed_layout.addWidget(QLabel("床类型:"))
        self.postid_bed_selector = QComboBox()
        self.postid_bed_selector.addItems(["Primary (一级床)", "Secondary (二级床)"])
        postid_bed_layout.addWidget(self.postid_bed_selector)
        
        # 获取按钮和显示
        postid_button_layout = QHBoxLayout()
        self.get_postid_button = QPushButton("获取PostId")
        self.get_postid_button.clicked.connect(self.get_bed_post_id)
        self.get_postid_button.setStyleSheet("QPushButton { background-color: #9C27B0; color: white; }")
        postid_button_layout.addWidget(self.get_postid_button)
        
        # PostId信息
        self.postid_info = QLabel("暂无数据")
        
        postid_layout.addLayout(postid_bed_layout)
        postid_layout.addLayout(postid_button_layout)
        postid_layout.addWidget(self.postid_info)
        postid_group.setLayout(postid_layout)
        control_layout.addWidget(postid_group)
        
        # 心跳检测
        heartbeat_group = QGroupBox("心跳检测")
        heartbeat_layout = QVBoxLayout()

        self.heartbeat_button = QPushButton("发送心跳")
        self.heartbeat_button.clicked.connect(self.send_heartbeat)
        self.heartbeat_button.setStyleSheet("QPushButton { background-color: #00BCD4; color: white; }")

        self.heartbeat_status = QLabel("未发送")

        heartbeat_layout.addWidget(self.heartbeat_button)
        heartbeat_layout.addWidget(self.heartbeat_status)
        heartbeat_group.setLayout(heartbeat_layout)
        control_layout.addWidget(heartbeat_group)

        # Trigger信息监控
        trigger_group = QGroupBox("Trigger信息监控")
        trigger_layout = QVBoxLayout()

        trigger_buttons_layout = QHBoxLayout()
        self.start_trigger_button = QPushButton("开始监控")
        self.start_trigger_button.clicked.connect(self.start_trigger_monitoring)
        self.start_trigger_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")

        self.stop_trigger_button = QPushButton("停止监控")
        self.stop_trigger_button.clicked.connect(self.stop_trigger_monitoring)
        self.stop_trigger_button.setStyleSheet("QPushButton { background-color: #F44336; color: white; }")
        self.stop_trigger_button.setEnabled(False)

        trigger_buttons_layout.addWidget(self.start_trigger_button)
        trigger_buttons_layout.addWidget(self.stop_trigger_button)

        self.trigger_status = QLabel("未监控")
        self.trigger_info_display = QTextEdit()
        self.trigger_info_display.setMaximumHeight(100)
        self.trigger_info_display.setReadOnly(True)

        trigger_layout.addLayout(trigger_buttons_layout)
        trigger_layout.addWidget(self.trigger_status)
        trigger_layout.addWidget(self.trigger_info_display)
        trigger_group.setLayout(trigger_layout)
        control_layout.addWidget(trigger_group)
        
        parent.addWidget(control_widget)

    def init_bed_status_panel(self, parent):
        """初始化床状态面板"""
        status_widget = QWidget()
        status_layout = QVBoxLayout(status_widget)
        
        # 系统状态
        system_group = QGroupBox("系统状态")
        system_layout = QFormLayout()
        
        self.sw_version_label = QLabel("")
        self.timestamp_label = QLabel("")
        self.current_owner_label = QLabel("无控制者")
        
        system_layout.addRow("软件版本:", self.sw_version_label)
        system_layout.addRow("时间戳:", self.timestamp_label)
        system_layout.addRow("当前控制者:", self.current_owner_label)
        
        # 状态刷新控制
        refresh_layout = QHBoxLayout()
        refresh_button = QPushButton("刷新状态")
        refresh_button.clicked.connect(self.get_bed_system_status)
        
        self.auto_refresh_bed = QCheckBox("自动刷新(1秒)")
        self.auto_refresh_bed.stateChanged.connect(self.toggle_bed_auto_refresh)
        
        refresh_layout.addWidget(refresh_button)
        refresh_layout.addWidget(self.auto_refresh_bed)
        
        system_group.setLayout(system_layout)
        status_layout.addWidget(system_group)
        status_layout.addLayout(refresh_layout)
        
        # 一级床状态
        primary_group = QGroupBox("一级床状态")
        primary_layout = QFormLayout()
        
        self.primary_position_label = QLabel("0")
        self.primary_velocity_label = QLabel("0")
        self.primary_status_label = QLabel("未知")
        
        # 运动能力
        self.primary_position_range_label = QLabel("")
        self.primary_velocity_range_label = QLabel("")
        self.primary_acceleration_label = QLabel("")
        
        primary_layout.addRow("当前位置(mm):", self.primary_position_label)
        primary_layout.addRow("当前速度(mm/s):", self.primary_velocity_label)
        primary_layout.addRow("运动状态:", self.primary_status_label)
        # primary_layout.addRow("位置范围:", self.primary_position_range_label)
        # primary_layout.addRow("速度范围:", self.primary_velocity_range_label)
        # primary_layout.addRow("加速度上限:", self.primary_acceleration_label)
        
        primary_group.setLayout(primary_layout)
        status_layout.addWidget(primary_group)
        
        # 二级床状态
        secondary_group = QGroupBox("二级床状态")
        secondary_layout = QFormLayout()
        
        self.secondary_position_label = QLabel("0")
        self.secondary_velocity_label = QLabel("0")
        self.secondary_status_label = QLabel("")
        
        # 运动能力
        self.secondary_position_range_label = QLabel("")
        self.secondary_velocity_range_label = QLabel("")
        self.secondary_acceleration_label = QLabel("")
        
        secondary_layout.addRow("当前位置(mm):", self.secondary_position_label)
        secondary_layout.addRow("当前速度(mm):", self.secondary_velocity_label)
        secondary_layout.addRow("运动状态:", self.secondary_status_label)
        # secondary_layout.addRow("位置范围:", self.secondary_position_range_label)
        # secondary_layout.addRow("速度范围:", self.secondary_velocity_range_label)
        # secondary_layout.addRow("加速度上限:", self.secondary_acceleration_label)
        
        secondary_group.setLayout(secondary_layout)
        status_layout.addWidget(secondary_group)
        
        parent.addWidget(status_widget)

    def get_bed_system_status(self):
        """获取系统状态"""
        if not self.check_connection_ready() or not self.bed_stub:
            return
        
        try:
            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=context_uid)
            response = self.bed_stub.GetSystemStatusInfo(request, timeout=3.0, metadata=metadata)
            
            # 更新系统状态
            self.sw_version_label.setText(response.softwareVersion)

            # 转换时间戳为可读格式
            try:
                timestamp_ms = int(response.timeStamp)
                timestamp_s = timestamp_ms / 1000
                formatted_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp_s))
                self.timestamp_label.setText(formatted_time)
            except (ValueError, TypeError):
                self.timestamp_label.setText(response.timeStamp)
            
            # 更新控制者
            owner_map = {
                motor_control_pb2.CT: "CT",
                motor_control_pb2.PET: "PET",
                motor_control_pb2.SPECT: "SPECT",
                motor_control_pb2.NONE: "无控制者"
            }
            self.current_owner_label.setText(owner_map.get(response.ownership, "未知"))
            
            # 更新一级床状态
            if response.firstBedStatus:
                self.primary_position_label.setText(f"{response.firstBedStatus.motionInfo.postion:.2f}")
                self.primary_velocity_label.setText(f"{(response.firstBedStatus.motionInfo.velocity / 10):.2f}")
                
                status_map = {
                    motor_control_pb2.Ready: "就绪",
                    motor_control_pb2.Moving: "移动中",
                    motor_control_pb2.Estop: "紧急停止",
                    motor_control_pb2.Error: "错误"
                }
                self.primary_status_label.setText(status_map.get(response.firstBedStatus.motionStatus, "未知"))
                
                # 设置状态标签颜色
                if response.firstBedStatus.motionStatus == motor_control_pb2.Error:
                    self.primary_status_label.setStyleSheet("color: red; font-weight: bold;")
                elif response.firstBedStatus.motionStatus == motor_control_pb2.Moving:
                    self.primary_status_label.setStyleSheet("color: green; font-weight: bold;")
                elif response.firstBedStatus.motionStatus == motor_control_pb2.Estop:
                    self.primary_status_label.setStyleSheet("color: orange; font-weight: bold;")
                else:
                    self.primary_status_label.setStyleSheet("color: blue; font-weight: bold;")
                
                # 运动能力
                cap = response.firstBedStatus.motionCapability
                self.primary_position_range_label.setText(f"{cap.positionMin} ~ {cap.positionMax}")
                self.primary_velocity_range_label.setText(f"{cap.velocityMin} ~ {cap.velocityMax}")
                self.primary_acceleration_label.setText(f"{cap.accelerationMax}")
            
            # 更新二级床状态
            if response.secondaryBedStatus:
                self.secondary_position_label.setText(f"{response.secondaryBedStatus.motionInfo.postion:.2f}")
                self.secondary_velocity_label.setText(f"{response.secondaryBedStatus.motionInfo.velocity:.2f}")
                
                status_map = {
                    motor_control_pb2.Ready: "就绪",
                    motor_control_pb2.Moving: "移动中",
                    motor_control_pb2.Estop: "紧急停止",
                    motor_control_pb2.Error: "错误"
                }
                self.secondary_status_label.setText(status_map.get(response.secondaryBedStatus.motionStatus, "未知"))
                
                # 设置状态标签颜色
                if response.secondaryBedStatus.motionStatus == motor_control_pb2.Error:
                    self.secondary_status_label.setStyleSheet("color: red; font-weight: bold;")
                elif response.secondaryBedStatus.motionStatus == motor_control_pb2.Moving:
                    self.secondary_status_label.setStyleSheet("color: green; font-weight: bold;")
                elif response.secondaryBedStatus.motionStatus == motor_control_pb2.Estop:
                    self.secondary_status_label.setStyleSheet("color: orange; font-weight: bold;")
                else:
                    self.secondary_status_label.setStyleSheet("color: blue; font-weight: bold;")
                
                # 运动能力
                cap = response.secondaryBedStatus.motionCapability
                self.secondary_position_range_label.setText(f"{cap.positionMin} ~ {cap.positionMax}")
                self.secondary_velocity_range_label.setText(f"{cap.velocityMin} ~ {cap.velocityMax}")
                self.secondary_acceleration_label.setText(f"{cap.accelerationMax}")
            
            self.log_message("成功获取动物床系统状态")
            
        except Exception as e:
            self.log_message(f"获取动物床系统状态失败: {str(e)}")

    def update_bed_status(self):
        """定时更新床状态"""
        if self.connected:
            self.get_bed_system_status()

    def toggle_bed_auto_refresh(self, state):
        """切换自动刷新床状态"""
        if state == Qt.Checked:
            self.bed_status_timer.start(1000)  # 每秒刷新一次
            self.log_message("已启用自动刷新床状态")
        else:
            self.bed_status_timer.stop()
            self.log_message("已禁用自动刷新床状态")

    def get_bed_post_id(self):
        """获取床PostId"""
        if not self.check_connection_ready() or not self.bed_stub:
            return
        
        try:
            # 获取选择的床类型
            bed_type_idx = self.postid_bed_selector.currentIndex()
            bed_type = motor_control_pb2.Primary if bed_type_idx == 0 else motor_control_pb2.Secondary

            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()

            request = motor_control_pb2.GetPostIdDescription(
                contextUID=context_uid,
                bedType=bed_type
            )

            response = self.bed_stub.GetPostId(request, metadata=metadata)

            self.log_message(f"发送获取PostId请求 - 客户端类型: {client_type}, Context UID: {context_uid}")

            if response.errorCode == 0:
                postid = response.postId
                postid_text = f"VID: {postid.VID}\nDID: {postid.DID}\nHWID: {postid.HWID}\nRID: {postid.RID}"
                self.postid_info.setText(postid_text)
                self.log_message(f"成功获取床({bed_type_idx})的PostId")
            else:
                self.postid_info.setText(f"获取失败: 错误码 {response.errorCode}")
                self.log_message(f"获取床PostId失败: 错误码 {response.errorCode}")
                
        except Exception as e:
            self.postid_info.setText(f"获取失败: {str(e)}")
            self.log_message(f"获取床PostId异常: {str(e)}")

    def gain_bed_control(self):
        """获取床控制权"""
        if not self.check_connection_ready() or not self.bed_stub:
            return
        
        try:
            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=context_uid)
            response = self.bed_stub.GainControl(request, metadata=metadata)

            self.log_message(f"发送获取控制权请求 - 客户端类型: {client_type}, Context UID: {context_uid}")

            if response.errorCode == 0:
                self.log_message(f"成功获取床控制权: {client_type}")
                QMessageBox.information(self, "成功", f"{client_type}成功获取床控制权")
            else:
                self.log_message(f"获取床控制权失败: 错误码 {response.errorCode}")
                QMessageBox.warning(self, "操作失败", f"获取床控制权失败: 错误码 {response.errorCode}")
                
            # 刷新状态
            self.get_bed_system_status()
                
        except Exception as e:
            self.log_message(f"获取床控制权异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"获取床控制权失败: {str(e)}")

    def release_bed_control(self):
        """释放床控制权"""
        if not self.check_connection_ready() or not self.bed_stub:
            return
        
        try:
            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=context_uid)
            response = self.bed_stub.ReleaseControl(request, metadata=metadata)

            self.log_message(f"发送释放控制权请求 - 客户端类型: {client_type}, Context UID: {context_uid}")

            if response.errorCode == 0:
                self.log_message("成功释放床控制权")
                QMessageBox.information(self, "成功", "成功释放床控制权")
            else:
                self.log_message(f"释放床控制权失败: 错误码 {response.errorCode}")
                QMessageBox.warning(self, "操作失败", f"释放床控制权失败: 错误码 {response.errorCode}")
                
            # 刷新状态
            self.get_bed_system_status()
                
        except Exception as e:
            self.log_message(f"释放床控制权异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"释放床控制权失败: {str(e)}")

    def send_heartbeat(self):
        """发送心跳检测"""
        if not self.check_connection_ready() or not self.bed_stub:
            return

        try:
            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=context_uid)
            response = self.bed_stub.HeartBeatCheck(request, metadata=metadata)

            timestamp = time.strftime("%H:%M:%S")
            self.log_message(f"发送心跳检测 - 客户端类型: {client_type}, Context UID: {context_uid}")

            if response.errorCode == 0:
                self.heartbeat_status.setText(f"成功 ({timestamp})")
                self.heartbeat_status.setStyleSheet("color: green;")
                self.log_message("心跳检测成功")
            else:
                self.heartbeat_status.setText(f"失败: 错误码 {response.errorCode}")
                self.heartbeat_status.setStyleSheet("color: red;")
                self.log_message(f"心跳检测失败: 错误码 {response.errorCode}")

        except Exception as e:
            self.heartbeat_status.setText(f"失败: {str(e)}")
            self.heartbeat_status.setStyleSheet("color: red;")
            self.log_message(f"心跳检测失败: {str(e)}")

    def start_bed_move(self):
        """开始移动床"""
        if not self.check_connection_ready() or not self.bed_stub:
            return

        # 立即更新UI状态 - 禁用开始按钮，启用停止按钮
        self.start_move_button.setEnabled(False)
        self.stop_move_button.setEnabled(True)

        # 获取参数
        bed_type_idx = self.bed_type_selector.currentIndex()
        if bed_type_idx == 0:
            bed_type = motor_control_pb2.Primary
        elif bed_type_idx == 1:
            bed_type = motor_control_pb2.Secondary
        else:
            bed_type = motor_control_pb2.Both

        motion_mode_idx = self.motion_mode_selector.currentIndex()
        if motion_mode_idx == 0:
            motion_mode = motor_control_pb2.PositionMode
        elif motion_mode_idx == 1:
            motion_mode = motor_control_pb2.VelocityMode
        else:
            motion_mode = motor_control_pb2.HomingMode

        position = self.target_position.value()
        velocity = self.target_velocity.value()

        # 创建运动信息
        motion_info = motor_control_pb2.MotionInfo(postion=position, velocity=velocity)

        # 获取上下文信息
        client_type, context_uid = self.get_current_context_info()

        # 创建请求
        request = motor_control_pb2.StartMoveDescription(
            contextUID=context_uid,
            mode=motion_mode,
            bedType=bed_type,
            targetMotionInfo=motion_info
        )

        # 在后台线程中执行移动操作
        def move_operation():
            try:
                # 创建gRPC metadata并发送请求
                metadata = self.create_grpc_metadata()
                response = self.bed_stub.StartMove(request, metadata=metadata)

                self.log_message(f"发送床移动请求 - 客户端类型: {client_type}, Context UID: {context_uid}")

                if response.errorCode == 0:
                    mode_text = ["位置", "速度", "回零"][motion_mode_idx]
                    bed_text = ["一级床", "二级床", "两级床"][bed_type_idx]
                    message = f"成功启动{bed_text}移动: {mode_text}模式\n当前位置: {response.currentMotionInfo.postion}"
                    self.move_operation_finished.emit(True, message, response)
                else:
                    message = f"启动床移动失败: 错误码 {response.errorCode}"
                    self.move_operation_finished.emit(False, message, response)

            except Exception as e:
                message = f"启动床移动异常: {str(e)}"
                self.move_operation_finished.emit(False, message, None)

        # 启动后台线程
        thread = threading.Thread(target=move_operation)
        thread.daemon = True
        thread.start()

        self.log_message("正在启动床移动操作...")

    @pyqtSlot(bool, str, object)
    def on_move_operation_finished(self, success, message, response):
        """处理移动操作完成的信号"""
        self.log_message(message)

        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.warning(self, "操作失败", message)

        # 恢复按钮状态
        self.start_move_button.setEnabled(True)
        self.stop_move_button.setEnabled(True)

        # 刷新状态
        # self.get_bed_system_status()

    def stop_bed_move(self):
        """停止床移动"""
        if not self.check_connection_ready() or not self.bed_stub:
            return

        try:
            client_type, context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()
            request = motor_control_pb2.CommonDescription(contextUID=context_uid)
            response = self.bed_stub.StopMove(request, timeout=3.0, metadata=metadata)

            self.log_message(f"发送停止移动请求 - 客户端类型: {client_type}, Context UID: {context_uid}")

            if response.errorCode == 0:
                self.log_message("成功停止床移动")
                QMessageBox.information(self, "成功", "成功停止床移动")
            else:
                self.log_message(f"停止床移动失败: 错误码 {response.errorCode}")
                QMessageBox.warning(self, "操作失败", f"停止床移动失败: 错误码 {response.errorCode}")

            # 无论成功还是失败，都恢复按钮状态
            self.start_move_button.setEnabled(True)
            self.stop_move_button.setEnabled(True)
            self.start_move_button.setText("开始移动")

            # 刷新状态
            # self.get_bed_system_status()

        except Exception as e:
            self.log_message(f"停止床移动异常: {str(e)}")
            QMessageBox.critical(self, "错误", f"停止床移动失败: {str(e)}")
            # 异常时也恢复按钮状态
            self.start_move_button.setEnabled(True)
            self.stop_move_button.setEnabled(True)
            self.start_move_button.setText("开始移动")

    def start_trigger_monitoring(self):
        """开始Trigger信息监控"""
        if not self.check_connection_ready() or not self.bed_stub:
            return

        if self.trigger_monitoring:
            return

        self.trigger_monitoring = True
        self.start_trigger_button.setEnabled(False)
        self.stop_trigger_button.setEnabled(True)
        self.trigger_status.setText("监控中...")
        self.trigger_status.setStyleSheet("color: green;")

        # 启动监控线程
        self.trigger_thread = threading.Thread(target=self._trigger_monitoring_worker)
        self.trigger_thread.daemon = True
        self.trigger_thread.start()

        self.log_message("开始Trigger信息监控")

    def stop_trigger_monitoring(self):
        """停止Trigger信息监控"""
        self.trigger_monitoring = False
        self.start_trigger_button.setEnabled(True)
        self.stop_trigger_button.setEnabled(False)
        self.trigger_status.setText("已停止")
        self.trigger_status.setStyleSheet("color: red;")

        self.log_message("停止Trigger信息监控")

    def _trigger_monitoring_worker(self):
        """Trigger监控工作线程"""
        try:
            # 获取上下文信息
            client_type, base_context_uid = self.get_current_context_info()
            metadata = self.create_grpc_metadata()

            self.log_message(f"开始Trigger监控 - 客户端类型: {client_type}")

            def request_generator():
                counter = 0
                while self.trigger_monitoring:
                    yield motor_control_pb2.GetTriggerInfoDescription(
                        contextUID=f"{base_context_uid}-trigger-{counter}"
                    )
                    counter += 1
                    time.sleep(0.1)  # 100ms间隔

            # 建立流式连接
            response_stream = self.bed_stub.GetTriggerInfo(request_generator(), metadata=metadata)

            for response in response_stream:
                if not self.trigger_monitoring:
                    break

                if response.errorCode == 0:
                    # 更新UI显示
                    trigger_info = f"位置: {response.triggerPosition:.2f}, 时间戳: {response.timestamp}"

                    # 在主线程中更新UI
                    self.trigger_info_display.append(f"[{time.strftime('%H:%M:%S')}] {trigger_info}")

                    # 限制显示行数
                    if self.trigger_info_display.document().blockCount() > 100:
                        cursor = self.trigger_info_display.textCursor()
                        cursor.movePosition(cursor.Start)
                        cursor.select(cursor.BlockUnderCursor)
                        cursor.removeSelectedText()
                        cursor.deleteChar()  # 删除换行符
                else:
                    self.log_message(f"Trigger监控错误: 错误码 {response.errorCode}")

        except Exception as e:
            self.log_message(f"Trigger监控异常: {str(e)}")
        finally:
            # 确保在异常情况下也能恢复UI状态
            if self.trigger_monitoring:
                self.trigger_monitoring = False
                self.start_trigger_button.setEnabled(True)
                self.stop_trigger_button.setEnabled(False)
                self.trigger_status.setText("监控异常")
                self.trigger_status.setStyleSheet("color: red;")

def main():
    app = QApplication(sys.argv)
    window = MotorControlGUI()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
