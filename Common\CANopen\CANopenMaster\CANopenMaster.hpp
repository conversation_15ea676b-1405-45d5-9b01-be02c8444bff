#ifndef CANOPEN_MASTER_HPP
#define CANOPEN_MASTER_HPP

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <map>
#include <mutex>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <cstdint>

#include "CANopenPara.hpp"



// CANopen相关头文件
extern "C" {
    #include "CANopen.h"
    #include "CO_error.h"
    #include "CO_epoll_interface.h"
}

// PDO回调函数类型定义
typedef std::function<void(uint8_t nodeId, const uint8_t* data, size_t length)> PDOCallback;

// PDO映射缓存结构，用于记录每个节点的PDO映射关系
struct PDOMappingCache {
    std::vector<PDOMappingEntry> entries;  // 映射条目列表
    uint8_t transmissionType;              // 传输类型
};

class CANopenMaster {
public:
    // 构造函数和析构函数
    explicit CANopenMaster(const std::string& canInterface);

    ~CANopenMaster();

    // 初始化CANopen网络
    virtual bool init();
    
    // 启动CANopen网络
    virtual bool start();
    
    // 停止CANopen网络
    virtual bool stop();

    // 发送NMT命令
    virtual bool sendNMTCommand(uint8_t command, uint8_t nodeId);
    
    // 发送SYNC同步消息
    virtual bool sendSYNCMessage();

    // 添加设备节点
    virtual bool addNode(uint8_t nodeId);
    
    // 查找设备节点
    virtual bool findNode(uint8_t nodeId);

    // 初始化节点
    virtual bool initializeNMTState(uint8_t nodeId);
    
    // DS402状态机控制
    virtual bool sendControlWord(uint8_t nodeId, uint16_t controlWord);
    virtual bool sendControlWordCommand(uint8_t nodeId, DS402Command command);
    virtual DS402State getState(uint8_t nodeId);
    uint16_t getStatusWord(uint8_t nodeId);
    
    // 操作模式设置
    virtual bool setOperationMode(uint8_t nodeId, DS402OperationMode mode);
    virtual DS402OperationMode getOperationMode(uint8_t nodeId);
    
    // 位置控制
    virtual bool setTargetPosition(uint8_t nodeId, int32_t position);
    virtual int32_t getActualPosition(uint8_t nodeId);
    
    // 速度控制
    virtual bool setTargetVelocity(uint8_t nodeId, int32_t velocity);
    virtual int32_t getActualVelocity(uint8_t nodeId);

    // 加减速控制
    virtual bool setProfileAcceleration(uint8_t nodeId, uint32_t acceleration);
    virtual bool setProfileDeceleration(uint8_t nodeId, uint32_t deceleration);
    virtual uint32_t getProfileAcceleration(uint8_t nodeId);
    virtual uint32_t getProfileDeceleration(uint8_t nodeId);
    
    // 新增接口 - 轮廓速度控制
    virtual bool setProfileVelocity(uint8_t nodeId, uint32_t velocity);
    virtual uint32_t getProfileVelocity(uint8_t nodeId);

    // 新增接口 - 快速停止减速度
    virtual bool setQuickStopDeceleration(uint8_t nodeId, uint32_t deceleration);
    virtual uint32_t getQuickStopDeceleration(uint8_t nodeId);
    
    // 新增接口 - 运动轮廓类型
    virtual bool setMotionProfileType(uint8_t nodeId, uint16_t profileType);
    virtual uint16_t getMotionProfileType(uint8_t nodeId);
    
    // 新增接口 - 位置跟踪窗口
    virtual bool setPositionTrackingWindow(uint8_t nodeId, uint32_t window);
    virtual uint32_t getPositionTrackingWindow(uint8_t nodeId);
    
    // 新增接口 - 位置跟踪窗口时间
    virtual bool setPositionTrackingWindowTime(uint8_t nodeId, uint16_t time);
    virtual uint16_t getPositionTrackingWindowTime(uint8_t nodeId);
    
    // 新增接口 - 触发探针功能
    virtual bool setTouchProbeFunction(uint8_t nodeId, uint16_t function);
    virtual uint16_t getTouchProbeFunction(uint8_t nodeId);
    
    // 新增接口 - 触发探针选择
    virtual bool setTouchProbeSelect(uint8_t nodeId, uint8_t touchProbeIndex, int16_t ioSelect);
    virtual int16_t getTouchProbeSelect(uint8_t nodeId, uint8_t touchProbeIndex);
    
    // 新增接口 - 输入引脚去抖动值
    virtual bool setInputPinDebounceValues(uint8_t nodeId, uint8_t pinIndex, uint16_t values);
    virtual uint16_t getInputPinDebounceValues(uint8_t nodeId, uint8_t pinIndex);

    // 新增接口 - 输入引脚配置
    virtual bool setInputPinConfiguration(uint8_t nodeId, uint8_t pinIndex, uint16_t configuration);
    virtual uint16_t getInputPinConfiguration(uint8_t nodeId, uint8_t pinIndex);
    
    // 新增接口 - 相位模式
    virtual bool setPhasingMode(uint8_t nodeId, uint16_t mode);
    virtual uint16_t getPhasingMode(uint8_t nodeId);
    
    // 新增接口 - 故障掩码
    virtual bool setFaultMask(uint8_t nodeId, uint32_t mask);
    virtual uint32_t getFaultMask(uint8_t nodeId);
    
    // 新增接口 - 回零相关
    virtual bool setHomingMethod(uint8_t nodeId, int8_t method);
    virtual int8_t getHomingMethod(uint8_t nodeId);
    
    virtual bool setHomingSpeeds(uint8_t nodeId, uint8_t subIndex, uint32_t speed);
    virtual uint32_t getHomingSpeeds(uint8_t nodeId, uint8_t subIndex);
    
    virtual bool setHomingAcceleration(uint8_t nodeId, uint32_t acceleration);
    virtual uint32_t getHomingAcceleration(uint8_t nodeId);
    
    virtual bool setHomingOffset(uint8_t nodeId, int32_t offset);
    virtual int32_t getHomingOffset(uint8_t nodeId);
    
    // 扭矩控制
    bool setTargetTorque(uint8_t nodeId, int16_t torque);
    int16_t getActualTorque(uint8_t nodeId);
    
    // PDO配置
    virtual bool configureTPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig& config);
    virtual bool configureRPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig& config);
    virtual bool configLocalRPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig& config);  //  根据子站配置的TPDO来配置主站的RPDO（本地）
    
    // 增强的PDO配置接口
    virtual bool configurePDO(uint8_t nodeId, PDOType pdoType, uint8_t pdoNum, const PDOConfig& config);
    
    // PDO回调函数注册
    virtual void registerPDOCallback(PDOType pdoType, uint8_t pdoNum, PDOCallback callback);
    virtual void unregisterPDOCallback(PDOType pdoType, uint8_t pdoNum);

    // 获取PDO数据
    virtual PDOData getPDOData(uint8_t nodeId);
    
    // 心跳功能
    virtual bool setHeartbeatTime(uint8_t nodeId, uint16_t heartbeatTime);
    virtual uint16_t getHeartbeatTime(uint8_t nodeId);
    virtual bool configureHeartbeatConsumer(uint8_t nodeId, uint16_t heartbeatTime);

    // 创建PDO映射
    static uint32_t createPDOMapping(uint16_t index, uint8_t subIndex, uint8_t length);
    
    // 获取SDO错误代码的文本描述
    static std::string getSDOAbortCodeDescription(CO_SDO_abortCode_t abortCode);


private:
    // CANopen对象
    CO_t* CO;
    
    // CAN接口
    std::string canInterface;
    
    // 节点数据映射
    std::map<uint8_t, PDOData> nodeDataMap; //  todo:两个驱动器的相同object在本地存储是否会发生冲突？
    std::mutex nodeDataMutex;
    
    // PDO回调函数映射
    std::map<std::pair<PDOType, uint8_t>, PDOCallback> pdoCallbacks;
    std::mutex pdoCallbackMutex;
    
    // PDO映射缓存：nodeId -> pdoNum -> 映射信息
    std::map<uint8_t, std::map<uint8_t, PDOMappingCache>> pdoMappingCache;
    std::mutex pdoMappingMutex;
    
    // 当前SDO操作的上下文
    struct SDOContext {
        uint8_t nodeId;
        uint16_t index;
        uint8_t subIndex;
    };
    
    // 处理线程
    std::thread processThread;
    std::thread rtThread;
    std::atomic<bool> running;
    std::atomic<bool> isCANopenStarted{false};

    // Object for epoll
    CO_epoll_t epMain_;
    CO_epoll_t epRt_;

    CO_CANptrSocketCan_t CANptr{};

    uint8_t masterNodeId_{0x01};

    std::string domain_{"CANopenMaster"};

protected:
    
    // 实时线程处理函数
    void rtThreadFunction();
    
    // SDO读写函数
    CO_SDO_abortCode_t readSDO(uint8_t nodeId, uint16_t index, uint8_t subIndex,
                             uint8_t* buffer, size_t bufferSize, size_t* readSize);
    CO_SDO_abortCode_t writeSDO(uint8_t nodeId, uint16_t index, uint8_t subIndex,
                              uint8_t* data, size_t dataSize);
    
    // DS402状态字解析
    DS402State parseStatusWord(uint16_t statusWord);
    
    // DS402控制字生成
    uint16_t generateControlWord(DS402Command command);
    
    // canopennode主线程
    void mainThreadFunction();
    
    // PDO数据处理
    void processPDOData(uint8_t nodeId, uint8_t rpdoNum, const uint8_t* data, size_t length);
    
    // 获取指定节点和PDO编号的映射配置
    PDOMappingCache* getPDOMappingCache(uint8_t nodeId, uint8_t pdoNum);
    
    // RPDO回调函数
    static void rpdoCallback(void* object);
    
    // TPDO回调函数
    static void tpdoCallback(void* object);
    
    // 错误处理
    void handleError(CO_SDO_abortCode_t abortCode);

    // 检查PDONum
    bool checkPDOIndex(uint8_t pdoNum);
};

#endif // CANOPEN_MASTER_HPP 