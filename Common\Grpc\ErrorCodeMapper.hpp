#ifndef ERROR_CODE_MAPPER_HPP
#define ERROR_CODE_MAPPER_HPP

#include <string>
#include <map>
#include <cstdint>

/**
 * @brief 错误码映射工具类
 * 
 * 统一管理所有错误码的定义和映射
 * 提供业务错误类型到 gRPC 错误码的转换
 */
class ErrorCodeMapper {
public:
    /**
     * @brief 业务错误类型枚举
     */
    enum class BusinessError {
        SUCCESS = 0,                    // 成功
        PERMISSION_DENIED,              // 权限被拒绝
        INVALID_PARAMETER,              // 无效参数
        HARDWARE_ERROR,                 // 硬件错误
        TIMEOUT,                        // 超时
        SERVO_MANAGER_NULL,             // 伺服管理器未初始化
        INVALID_BED_TYPE,               // 无效床类型
        GAIN_CONTROL_FAILED,            // 获取控制权失败
        RELEASE_CONTROL_FAILED,         // 释放控制权失败
        START_MOVE_FAILED,              // 开始移动失败
        STOP_OPERATION_FAILED,          // 停止操作失败
        MOTION_INTERFERENCE,            // 运动干涉
        GENERAL_ERROR                   // 通用错误
    };

    /**
     * @brief 根据业务错误类型获取错误码
     * @param error 业务错误类型
     * @return 对应的错误码
     */
    static uint64_t getErrorCode(BusinessError error);

    /**
     * @brief 根据错误类型字符串获取错误码（兼容现有代码）
     * @param errorType 错误类型字符串
     * @return 对应的错误码
     */
    static uint64_t getErrorCode(const std::string& errorType);

    /**
     * @brief 获取错误描述信息
     * @param error 业务错误类型
     * @return 错误描述字符串
     */
    static std::string getErrorDescription(BusinessError error);

    /**
     * @brief 检查操作是否成功
     * @param result 操作结果
     * @return 成功返回 SUCCESS，失败返回对应的错误类型
     */
    static BusinessError checkOperationResult(bool result, const std::string& operation);

private:
    // 错误码映射表
    static const std::map<BusinessError, uint64_t> errorCodeMap_;
    
    // 字符串到错误码的映射表（兼容现有代码）
    static const std::map<std::string, uint64_t> stringErrorCodeMap_;
    
    // 错误描述映射表
    static const std::map<BusinessError, std::string> errorDescriptionMap_;

    // 禁止实例化
    ErrorCodeMapper() = delete;
    ~ErrorCodeMapper() = delete;
    ErrorCodeMapper(const ErrorCodeMapper&) = delete;
    ErrorCodeMapper& operator=(const ErrorCodeMapper&) = delete;
};

#endif // ERROR_CODE_MAPPER_HPP
