syntax = "proto3";

package motor_control;


//  控床服务
service BedMasterAppService {

    //  开始移床
    rpc StartMove(StartMoveDescription) returns (StartMoveStatus) {}
    
    //  停止移床
    rpc StopMove(CommonDescription) returns (CommonStatus) {}
    
    //  获取PostId
    rpc GetPostId(GetPostIdDescription) returns (GetPostIdStatus) {}
    
    //  获取动物床控制权
    rpc GainControl(CommonDescription) returns (CommonStatus) {}
    
    //  释放动物床控制权
    rpc ReleaseControl(CommonDescription) returns (CommonStatus) {}
    
    //  心跳检测
    rpc HeartBeatCheck(CommonDescription) returns (CommonStatus) {}
    
    //  获取动物床状态
    rpc GetSystemStatusInfo(CommonDescription) returns (SystemStatusInfoStatus) {}

    // 获取trigger信息
    rpc GetTriggerInfo(stream GetTriggerInfoDescription) returns (stream TriggerInfoStatus) {}
    
}

//////////////////////////////////////////////////////开始移床////////////////////////////////////////////////////
message StartMoveDescription
{
  string contextUID = 1;  // 命令唯一标识符
  MotionMode mode = 2;
  BedType bedType = 3;
  MotionInfo targetMotionInfo = 4;
}

message StartMoveStatus
{
  string contextUID = 1;
  uint64 errorCode = 2;
  MotionInfo currentMotionInfo = 3;
}

////////////////////////////////////////////////////无参数服务////////////////////////////////////////////////////
message CommonDescription
{
  string contextUID = 1;
}

message CommonStatus
{
  string contextUID = 1;
  uint64 errorCode = 2;
}

////////////////////////////////////////////////////获取PostId////////////////////////////////////////////////////
message GetPostIdDescription
{
  string contextUID = 1;
  BedType bedType = 2;
}

message GetPostIdStatus
{
  string contextUID = 1;
  uint64 errorCode = 2;
  PostIdInfo postId=3;
}

////////////////////////////////////////////////////系统状态////////////////////////////////////////////////////
message SystemStatusInfoStatus
{
  string softwareVersion = 1;
  string timeStamp = 2;
  BedStatus firstBedStatus = 3;
  BedStatus secondaryBedStatus = 4;
  HostType ownership = 5;
}

////////////////////////////////////////////////////Trigger///////////////////////////////////////////////////

message GetTriggerInfoDescription
{
  string contextUID = 1;
}

message TriggerInfoStatus
{
  string contextUID = 1;
  uint64 errorCode = 2;
  float triggerPosition = 3;
  uint64 triggerTimestamp = 4;
  uint32 exposureTime = 5;
}

////////////////////////////////////////////////////枚举类型////////////////////////////////////////////////////

// 动物床类型
enum BedType
{
    Primary = 0;        // 一级床
    Secondary = 1;      // 二级床
    Both = 2;          // 该选项仅提供给Pet及Spect使用。先动二级床再动一级床。
}


//  运动模式
enum MotionMode
{
    PositionMode = 0;
    VelocityMode = 1;
    HomingMode = 2;
}

//  运动状态
enum MotionStatus
{
   Ready = 0;
   Moving = 1;
   Estop = 2;
   Error = 3;
}

// 上位机类型，用以标识当前动物床由谁来进行控制
enum HostType
{
    CT = 0;
    PET = 1;
    SPECT = 2;
    NONE = 3;
}

//  运动信息
message MotionInfo
{
    float postion = 1;
    float velocity = 2;
}

//  运动限制参数
message MotionCapability
{
    float positionMin = 1;
    float positionMax = 2;
    float velocityMin = 3;
    float velocityMax = 4;
    float accelerationMax = 5;
    float decelerationMax = 6;
}

//  动物床状态
message BedStatus
{
    MotionInfo motionInfo = 1;
    MotionStatus motionStatus = 2;
    MotionCapability motionCapability = 3;
}

//  PostId信息
message PostIdInfo
{
    uint32 VID = 1;
    uint32 DID = 2;
    uint32 HWID = 3;
    uint32 RID = 4;
}

////////////////////////////////////////////////////example////////////////////////////////////////////////////

// 操作模式枚举
enum OperationMode {
  PROFILE_POSITION = 0;
  VELOCITY = 1;
  PROFILE_VELOCITY = 2;
  PROFILE_TORQUE = 3;
  HOMING = 6;
  INTERPOLATED_POSITION = 7;
  CYCLIC_SYNC_POSITION = 8;
  CYCLIC_SYNC_VELOCITY = 9;
  CYCLIC_SYNC_TORQUE = 10;
}

// 驱动器状态枚举
enum DriveState {
  NOT_READY_TO_SWITCH_ON = 0;
  SWITCH_ON_DISABLED = 1;
  READY_TO_SWITCH_ON = 2;
  SWITCHED_ON = 3;
  OPERATION_ENABLED = 4;
  QUICK_STOP_ACTIVE = 5;
  FAULT_REACTION_ACTIVE = 6;
  FAULT = 7;
}

// 电机控制请求
message MotorControlRequest {
  uint32 node_id = 1;
  bool enable = 2;
}

// 电机控制响应
message MotorControlResponse {
  bool success = 1;
  string message = 2;
  DriveState state = 3;
}

// 电机状态请求
message MotorStatusRequest {
  uint32 node_id = 1;
}

// 电机状态响应
message MotorStatusResponse {
  bool success = 1;
  string message = 2;
  DriveState state = 3;
  int32 actual_position = 4;
  int32 actual_velocity = 5;
  int32 actual_torque = 6;
  OperationMode current_mode = 7;
  uint32 status_word = 8;
  bool is_target_reached = 9;
  bool has_fault = 10;
}

// 操作模式请求
message OperationModeRequest {
  uint32 node_id = 1;
  OperationMode mode = 2;
}

// 操作模式响应
message OperationModeResponse {
  bool success = 1;
  string message = 2;
  OperationMode current_mode = 3;
}

// 位置控制请求
message PositionControlRequest {
  uint32 node_id = 1;
  int32 target_position = 2;
  int32 profile_velocity = 3;
  int32 profile_acceleration = 4;
  int32 profile_deceleration = 5;
  bool absolute = 6;  // true=绝对位置，false=相对位置
  bool immediate = 7; // true=立即执行，false=完成当前动作后执行
}

// 位置控制响应
message PositionControlResponse {
  bool success = 1;
  string message = 2;
  int32 actual_position = 3;
}

// 速度控制请求
message VelocityControlRequest {
  uint32 node_id = 1;
  int32 target_velocity = 2;
  int32 profile_acceleration = 3;
  int32 profile_deceleration = 4;
}

// 速度控制响应
message VelocityControlResponse {
  bool success = 1;
  string message = 2;
  int32 actual_velocity = 3;
}

// 扭矩控制请求
message TorqueControlRequest {
  uint32 node_id = 1;
  int32 target_torque = 2;
  int32 torque_slope = 3;
}

// 扭矩控制响应
message TorqueControlResponse {
  bool success = 1;
  string message = 2;
  int32 actual_torque = 3;
}

// 回零请求
message HomingRequest {
  uint32 node_id = 1;
  int32 homing_method = 2;
  int32 homing_speed_switch = 3;
  int32 homing_speed_zero = 4;
  int32 homing_acceleration = 5;
}

// 回零响应
message HomingResponse {
  bool success = 1;
  string message = 2;
  bool homing_completed = 3;
}

// 紧急停止请求
message EmergencyStopRequest {
  uint32 node_id = 1;
}

// 紧急停止响应
message EmergencyStopResponse {
  bool success = 1;
  string message = 2;
}

// 清除故障请求
message ClearFaultRequest {
  uint32 node_id = 1;
}

// 清除故障响应
message ClearFaultResponse {
  bool success = 1;
  string message = 2;
  bool fault_cleared = 3;
}

// 获取所有节点请求
message GetAllNodesRequest {
  // 无需参数
}

// 节点信息
message NodeInfo {
  uint32 node_id = 1;
  bool is_connected = 2;
  DriveState state = 3;
  string vendor_name = 4;
  string product_name = 5;
  string revision_number = 6;
}

// 获取所有节点响应
message GetAllNodesResponse {
  bool success = 1;
  string message = 2;
  repeated NodeInfo nodes = 3;
}

// 设备配置
message DeviceConfig {
  string device = 1;
  string name = 2;
}

// 配置消息
message ConfigMessage {
  string can_interface = 1;
  string grpc_server_address = 2;
  int32 status_socket_port = 3;
  repeated uint32 node_ids = 4;
  repeated DeviceConfig uart_devices = 5;
  repeated DeviceConfig i2c_devices = 6;
}