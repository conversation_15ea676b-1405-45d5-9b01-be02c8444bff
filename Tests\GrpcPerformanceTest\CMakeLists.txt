# GRPC通信性能测试的CMakeLists.txt
# 基于现有Tests/CMakeLists.txt简化而来

cmake_minimum_required(VERSION 3.16)

set(CMAKE_CXX_STANDARD 17)

# 设置x86包搜索路径
set(GRPC_INSTALL_PATH_X86 "/home/<USER>/custom/Library/GrpcFramework/grpcInstall_x86/")
set(absl_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/absl")
set(Protobuf_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/protobuf")
set(gRPC_DIR "${GRPC_INSTALL_PATH_X86}/lib/cmake/grpc")

set(CMAKE_C_COMPILER /usr/bin/cc)
set(CMAKE_CXX_COMPILER /usr/bin/c++)

project(gRpcCommunicationPerformanceTest)

# 查找必要的包
find_package(GTest REQUIRED)
find_package(Protobuf CONFIG REQUIRED)
find_package(gRPC CONFIG REQUIRED)
find_package(Threads REQUIRED)

# 包含目录
set(CANOPENNODE_DIR ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenNode)
set(UNITTEST_MOCK_DIR ${CMAKE_SOURCE_DIR}/../GrpcUnitTest)
include_directories(
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc
    ${CMAKE_SOURCE_DIR}/../../Business/ServoControl
    ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenMaster
    ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate
    ${CMAKE_SOURCE_DIR}/../../Common/Utils
    ${CANOPENNODE_DIR}
    ${UNITTEST_MOCK_DIR}
    ${CANOPENNODE_DIR}/301
    ${CANOPENNODE_DIR}/303
    ${CANOPENNODE_DIR}/304
    ${CANOPENNODE_DIR}/305
    ${CANOPENNODE_DIR}/309
    ${CANOPENNODE_DIR}/extra
    ${CANOPENNODE_DIR}/storage
    ${CANOPENNODE_DIR}/linux
    ${CANOPENNODE_DIR}/od
)
link_directories("/mnt/hgfs/E/BedMaster/CANopenNodePorting/cmake-build-debug")

set(PROTOBUF_GEN_SOURCES
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/motor_control.pb.cc
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/motor_control.grpc.pb.cc
        ${CMAKE_SOURCE_DIR}/../../Common/Protobuf/ProtobufGenerate/ServoConfigParameters.pb.cc
)

# 创建通信性能测试可执行文件
add_executable(grpc_communication_performance_test
    GrpcCommunicationPerformanceTest.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/GrpcServer.cpp
    ${CMAKE_SOURCE_DIR}/../../Business/ServoControl/ServoControlManager.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/CANopen/CANopenMaster/CANopenMaster.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/ErrorCodeMapper.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Grpc/ProtobufConverter.cpp
    ${CMAKE_SOURCE_DIR}/../../Common/Utils/SyslogManager.cpp
    ${PROTOBUF_GEN_SOURCES}
)

# 链接库
target_link_libraries(grpc_communication_performance_test
    GTest::gtest
    GTest::gtest_main
    gmock
    gmock_main
    protobuf::libprotobuf
    gRPC::grpc++
    Threads::Threads
    canopennode
)

# 添加测试
enable_testing()
add_test(NAME GrpcCommunicationPerformanceTest COMMAND grpc_communication_performance_test)

# 设置编译选项
target_compile_features(grpc_communication_performance_test PRIVATE cxx_std_17)
target_compile_options(grpc_communication_performance_test PRIVATE -Wall -Wextra -O2)

# 创建结果目录
file(MAKE_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}/results)

# 打印配置信息
message(STATUS "Configuring gRPC Communication Performance Test")
message(STATUS "  C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  gRPC Version: ${gRPC_VERSION}")
message(STATUS "  Protobuf Version: ${Protobuf_VERSION}")

# 添加自定义目标用于快速测试
add_custom_target(quick_perf_test
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/grpc_communication_performance_test --gtest_filter="*SingleInterfaceLatency*"
    DEPENDS grpc_communication_performance_test
    COMMENT "Running quick performance test (single interface latency only)"
)

# 添加自定义目标用于完整测试
add_custom_target(full_perf_test
    COMMAND env PERF_TEST_DURATION=300 ${CMAKE_CURRENT_BINARY_DIR}/grpc_communication_performance_test
    DEPENDS grpc_communication_performance_test
    COMMENT "Running full performance test (5 minutes duration)"
)

# 添加自定义目标用于长时间测试
add_custom_target(long_perf_test
    COMMAND env PERF_TEST_DURATION=3600 ${CMAKE_CURRENT_BINARY_DIR}/grpc_communication_performance_test
    DEPENDS grpc_communication_performance_test
    COMMENT "Running long-term performance test (1 hour duration)"
)
