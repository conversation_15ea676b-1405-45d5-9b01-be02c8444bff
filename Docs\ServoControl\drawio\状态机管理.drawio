<mxfile host="65bd71144e">
    <diagram id="7_r87-Of8-3ns55agalt" name="第 1 页">
        <mxGraphModel dx="716" dy="1817" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="7" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="Initialize Success" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="7">
                    <mxGeometry x="-0.2083" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="" style="edgeStyle=orthogonalEdgeStyle;curved=1;html=1;" edge="1" parent="1" source="2">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="560" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="Initialize Error" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="35">
                    <mxGeometry x="0.2369" y="-40" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="2" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;Initial&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="300" y="60" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;curved=1;" edge="1" parent="1" source="3" target="4">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="430" y="320"/>
                            <mxPoint x="430" y="490"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="Move" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="9">
                    <mxGeometry x="-0.163" y="2" relative="1" as="geometry">
                        <mxPoint y="27" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3" target="5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="460" y="320" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="Error" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="22">
                    <mxGeometry x="0.0571" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="3" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;Ready&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="300" y="280" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="4" target="5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="460" y="490" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="MoveError" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="14">
                    <mxGeometry x="0.1025" relative="1" as="geometry">
                        <mxPoint x="-31" y="-40" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;curved=1;" edge="1" parent="1" source="4" target="6">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="220" y="490" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="Estop" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="18">
                    <mxGeometry x="-0.0882" y="-3" relative="1" as="geometry">
                        <mxPoint x="12" y="-27" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;Moving&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="300" y="450" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="30" value="" style="edgeStyle=orthogonalEdgeStyle;curved=1;html=1;exitX=0;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="340" y="280" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="532" y="240"/>
                            <mxPoint x="500" y="240"/>
                            <mxPoint x="500" y="220"/>
                            <mxPoint x="340" y="220"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="ClearError" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="30">
                    <mxGeometry x="-0.1176" y="4" relative="1" as="geometry">
                        <mxPoint x="-20" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;Error&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="520" y="280" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="" style="edgeStyle=orthogonalEdgeStyle;curved=1;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="6">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="340" y="280" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="140" y="210"/>
                            <mxPoint x="300" y="210"/>
                            <mxPoint x="300" y="281"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="45" value="Recover" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="42">
                    <mxGeometry x="-0.0264" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="6" value="&lt;span style=&quot;font-size: 16px;&quot;&gt;Estop&lt;/span&gt;" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;" vertex="1" parent="1">
                    <mxGeometry x="100" y="280" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="" style="endArrow=classic;html=1;curved=1;edgeStyle=orthogonalEdgeStyle;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="3">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="270" y="360" as="sourcePoint"/>
                        <mxPoint x="320" y="310" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="250" y="490"/>
                            <mxPoint x="250" y="320"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="29" value="MoveComplete/Stop" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="28">
                    <mxGeometry x="0.0777" y="-2" relative="1" as="geometry">
                        <mxPoint y="15" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="32" value="" style="shape=flexArrow;endArrow=classic;html=1;curved=1;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="339.5" y="-60" as="sourcePoint"/>
                        <mxPoint x="339.5" y="60" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="Start" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="32">
                    <mxGeometry x="-0.0944" y="-1" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>