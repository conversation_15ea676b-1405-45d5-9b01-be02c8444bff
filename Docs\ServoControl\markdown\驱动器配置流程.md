# 数据配置格式： [对象索引][子索引][值/说明]

> **注意**：方括号 `[]` 中的文字如 `[method]` 是该参数的说明或实际值占位符。

# 初始化流程

1. NMT状态切换

2. 配置TPDO

3. 一级床配置探针操作及其他IO操作

4. 二级床配置IO操作


# 回零模式(Homing Mode)配置流程

1. Set homing method： 
    [0x6098][0x00][method]

2. Set homing velocity:
    [0x6099][0x01][fast-speed]
    [0x6099][0x02][low-speed]

3. Set homing acceleration:
    [0x609A][0x00][acceleration]

4. Set homing mode:
    [0x6060][0x00][homing-mode]

5. Start Move:
    [0x6040][0x00][0x1F]

# 位置模式 (Position Mode) 配置流程

1. 设置操作模式为位置模式:
    [0x6060][0x00][0x01]  # 0x01代表Profile Position Mode

2. 设置轮廓速度、加速度和减速度:
    [0x6081][0x00][profile-velocity]  # 轮廓速度
    [0x6083][0x00][profile-acceleration]  # 轮廓加速度
    [0x6084][0x00][profile-deceleration]  # 轮廓减速度

3. 设置目标位置:
    [0x607A][0x00][target-position]

4. 设置位置窗及时间窗
    [0x6067][0x00][position-window]
    [0x6068][0x00][time-window]

4. 使能操作并启动位置移动:
    [0x6040][0x00][0x0F]  # 使能操作
    [0x6040][0x00][0x3F]  # 启动位置移动 (设置bit 4)

# 速度模式 (Velocity Mode) 配置流程

1. 设置操作模式为速度模式:
    [0x6060][0x00][0x03]  # 0x03代表Profile Velocity Mode

2. 设置轮廓加速度和减速度:
    [0x6082][0x00][profile-acceleration]  # 轮廓加速度
    [0x6083][0x00][profile-deceleration]  # 轮廓减速度

3. 设置目标速度:
    [0x60FF][0x00][target-velocity]

4. 使能操作并启动速度控制:
    [0x6040][0x00][0x0F]  # 使能操作
    [0x6040][0x00][0x0F]  # 保持使能，速度模式通常不需要额外的启动位

# CANopen DS402协议常用字典对象

## 控制相关对象
- **0x6040**: 控制字 (Controlword)
- **0x6041**: 状态字 (Statusword)
- **0x6060**: 操作模式 (Modes of operation)
- **0x6061**: 当前操作模式 (Modes of operation display)

## 位置控制相关对象
- **0x607A**: 目标位置 (Target position)
- **0x6062**: 当前位置 (Position demand value)
- **0x6064**: 实际位置 (Position actual value)
- **0x607D**: 软件位置限位 (Software position limit)
- **0x607B**: 位置范围限制 (Position range limit)
- **0x6067**: 位置窗口 (Position window)
- **0x6068**: 位置窗口时间 (Position window time)

## 速度控制相关对象
- **0x6080**: 最大电机速度 (Max motor speed)
- **0x6081**: 轮廓速度 (Profile velocity)
- **0x6082**: 加速度 (Profile acceleration)
- **0x6083**: 减速度 (Profile deceleration)
- **0x6084**: 急停减速度 (Quick stop deceleration)
- **0x606C**: 实际速度 (Velocity actual value)
- **0x60FF**: 目标速度 (Target velocity)

## 回零(Homing)相关对象
- **0x6098**: 回零方法 (Homing method)
- **0x6099**: 回零速度 (Homing speed)
  - **0x6099:01**: 快速回零速度 (Fast homing speed)
  - **0x6099:02**: 慢速回零速度 (Slow homing speed)
- **0x609A**: 回零加速度 (Homing acceleration)
- **0x607C**: 回零偏移量 (Home offset)
- **0x6072**: 最大转矩 (Max torque)

## 力矩控制相关对象
- **0x6071**: 目标转矩 (Target torque)
- **0x6077**: 实际转矩 (Torque actual value)
- **0x6087**: 转矩斜率 (Torque slope)
- **0x6080**: 最大电机速度 (Max motor speed)

## 故障处理相关对象
- **0x603F**: 错误码 (Error code)
- **0x1001**: 错误寄存器 (Error register)
- **0x1003**: 错误历史 (Pre-defined error field)

## 单位因子相关对象
- **0x6091**: 齿轮比 (Gear ratio)
- **0x6092**: 进给常数 (Feed constant)
- **0x6093**: 位置因子 (Position factor)
- **0x6094**: 速度编码器因子 (Velocity encoder factor)
- **0x6097**: 加减速比率 (Acceleration factor)

