#include "ProtobufConverter.hpp"
#include "SyslogManager.hpp"
#include <chrono>

BedOwnerType ProtobufConverter::convertHostType(motor_control::HostType hostType) {
    switch (hostType) {
        case motor_control::HostType::CT:
            return BedOwnerType::CT;
        case motor_control::HostType::PET:
            return BedOwnerType::PET;
        case motor_control::HostType::SPECT:
            return BedOwnerType::SPECT;
        case motor_control::HostType::NONE:
        default:
            return BedOwnerType::NONE;
    }
}

BedType ProtobufConverter::convertBedType(motor_control::BedType bedType) {
    switch (bedType) {
        case motor_control::BedType::Primary:
            return BedType::Primary;
        case motor_control::BedType::Secondary:
            return BedType::Secondary;
        case motor_control::BedType::Both:
            return BedType::Both;
        default:
            LOG_WARNING_MSG("ProtobufConverter", "未知的床类型: %d", static_cast<int>(bedType));
            return BedType::Primary; // 默认返回一级床
    }
}

motor_control::MotionMode ProtobufConverter::convertMotionMode(motor_control::MotionMode motionMode) {
    // 这个方法主要用于验证和日志记录，直接返回输入值
    switch (motionMode) {
        case motor_control::MotionMode::PositionMode:
        case motor_control::MotionMode::VelocityMode:
        case motor_control::MotionMode::HomingMode:
            return motionMode;
        default:
            LOG_WARNING_MSG("ProtobufConverter", "未知的运动模式: %d", static_cast<int>(motionMode));
            return motor_control::MotionMode::PositionMode; // 默认返回位置模式
    }
}

motor_control::HostType ProtobufConverter::convertToHostType(BedOwnerType ownerType) {
    switch (ownerType) {
        case BedOwnerType::CT:
            return motor_control::HostType::CT;
        case BedOwnerType::PET:
            return motor_control::HostType::PET;
        case BedOwnerType::SPECT:
            return motor_control::HostType::SPECT;
        case BedOwnerType::NONE:
        default:
            return motor_control::HostType::NONE;
    }
}

motor_control::BedType ProtobufConverter::convertToBedType(BedType bedType) {
    switch (bedType) {
        case BedType::Primary:
            return motor_control::BedType::Primary;
        case BedType::Secondary:
            return motor_control::BedType::Secondary;
        case BedType::Both:
            return motor_control::BedType::Both;
        default:
            LOG_WARNING_MSG("ProtobufConverter", "未知的业务层床类型: %d", static_cast<int>(bedType));
            return motor_control::BedType::Primary;
    }
}

void ProtobufConverter::fillSystemStatusInfo(const BedStatus& primaryStatus,
                                           const BedStatus& secondaryStatus,
                                           BedOwnerType currentOwner,
                                           motor_control::SystemStatusInfoStatus* response) {
    if (!response) {
        LOG_ERROR_MSG("ProtobufConverter", "响应对象为空");
        return;
    }

    // 设置软件版本和时间戳
    response->set_softwareversion("BedMaster v1.0.0");

    // 获取当前时间作为时间戳
    auto now = std::chrono::system_clock::now();
    auto now_ms = std::chrono::time_point_cast<std::chrono::milliseconds>(now);
    auto value = now_ms.time_since_epoch();
    response->set_timestamp(std::to_string(value.count()));

    // 设置控制权信息
    response->set_ownership(convertToHostType(currentOwner));

    // 填充一级床状态
    motor_control::BedStatus* firstBedStatus = response->mutable_firstbedstatus();
    motor_control::MotionInfo* primaryMotionInfo = firstBedStatus->mutable_motioninfo();
    primaryMotionInfo->set_postion(primaryStatus.motionInfo.position);
    primaryMotionInfo->set_velocity(primaryStatus.motionInfo.velocity);

    // 设置一级床运动状态
    switch (primaryStatus.status) {
        case BedMotionStatus::Ready:
            firstBedStatus->set_motionstatus(motor_control::MotionStatus::Ready);
            break;
        case BedMotionStatus::Moving:
            firstBedStatus->set_motionstatus(motor_control::MotionStatus::Moving);
            break;
        case BedMotionStatus::Estop:
            firstBedStatus->set_motionstatus(motor_control::MotionStatus::Estop);
            break;
        case BedMotionStatus::Error:
        default:
            firstBedStatus->set_motionstatus(motor_control::MotionStatus::Error);
            break;
    }

    // 填充一级床运动能力限制
    motor_control::MotionCapability* primaryCapability = firstBedStatus->mutable_motioncapability();
    primaryCapability->set_positionmin(primaryStatus.positionMin);
    primaryCapability->set_positionmax(primaryStatus.positionMax);
    primaryCapability->set_velocitymin(primaryStatus.velocityMin);
    primaryCapability->set_velocitymax(primaryStatus.velocityMax);
    primaryCapability->set_accelerationmax(primaryStatus.accelerationMax);
    primaryCapability->set_decelerationmax(primaryStatus.decelerationMax);

    // 填充二级床状态
    motor_control::BedStatus* secondBedStatus = response->mutable_secondarybedstatus();
    motor_control::MotionInfo* secondaryMotionInfo = secondBedStatus->mutable_motioninfo();
    secondaryMotionInfo->set_postion(secondaryStatus.motionInfo.position);
    secondaryMotionInfo->set_velocity(secondaryStatus.motionInfo.velocity);

    // 设置二级床运动状态
    switch (secondaryStatus.status) {
        case BedMotionStatus::Ready:
            secondBedStatus->set_motionstatus(motor_control::MotionStatus::Ready);
            break;
        case BedMotionStatus::Moving:
            secondBedStatus->set_motionstatus(motor_control::MotionStatus::Moving);
            break;
        case BedMotionStatus::Estop:
            secondBedStatus->set_motionstatus(motor_control::MotionStatus::Estop);
            break;
        case BedMotionStatus::Error:
        default:
            secondBedStatus->set_motionstatus(motor_control::MotionStatus::Error);
            break;
    }

    // 填充二级床运动能力限制
    motor_control::MotionCapability* secondaryCapability = secondBedStatus->mutable_motioncapability();
    secondaryCapability->set_positionmin(secondaryStatus.positionMin);
    secondaryCapability->set_positionmax(secondaryStatus.positionMax);
    secondaryCapability->set_velocitymin(secondaryStatus.velocityMin);
    secondaryCapability->set_velocitymax(secondaryStatus.velocityMax);
    secondaryCapability->set_accelerationmax(secondaryStatus.accelerationMax);
    secondaryCapability->set_decelerationmax(secondaryStatus.decelerationMax);
}

void ProtobufConverter::fillPostIdInfo(const PostIdInfo& postIdInfo, 
                                     motor_control::GetPostIdStatus* response) {
    if (!response) {
        LOG_ERROR_MSG("ProtobufConverter", "响应对象为空");
        return;
    }

    // 填充PostId信息
    motor_control::PostIdInfo* postId = response->mutable_postid();
    postId->set_vid(postIdInfo.vid);
    postId->set_did(postIdInfo.did);
    postId->set_hwid(postIdInfo.hwid);
    postId->set_rid(postIdInfo.rid);
}
