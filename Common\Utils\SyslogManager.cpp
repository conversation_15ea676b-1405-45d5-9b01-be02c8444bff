#include "SyslogManager.hpp"
#include <cstdio>
#include <sys/syscall.h>
#include <unistd.h>
#include <cxxabi.h>

void setLogger(char *programName) {
    // 初始化SyslogManager，启用控制台输出
    SyslogManager::getInstance().initialize(programName, LOG_CONS | LOG_PID | LOG_NDELAY | LOG_PERROR, LOG_LOCAL6, LOG_INFO);
    LOG_INFO_MSG("main", "%s 启动", programName);
}

SyslogManager::SyslogManager() : initialized_(false), consoleOutput_(false) {
}

SyslogManager::~SyslogManager() {
    shutdown();
}

SyslogManager& SyslogManager::getInstance() {
    static SyslogManager instance;
    return instance;
}

void SyslogManager::initialize(const char* ident, int option, int facility, int level) {
    std::lock_guard<std::mutex> lock(logMutex_);

    if (!initialized_) {
        openlog(ident, option, facility);
        setlogmask(LOG_UPTO(level));
        initialized_ = true;

        // 记录初始化日志
        syslog(LOG_INFO, "[%s] [TID:%s] [SyslogManager] 系统日志管理器已初始化",
               getCurrentTimestamp().c_str(), getCurrentThreadId().c_str());
    }
}

void SyslogManager::shutdown() {
    std::lock_guard<std::mutex> lock(logMutex_);

    if (initialized_) {
        syslog(LOG_INFO, "[%s] [TID:%s] [SyslogManager] 系统日志管理器正在关闭",
               getCurrentTimestamp().c_str(), getCurrentThreadId().c_str());

        if (consoleOutput_) {
            fprintf(stdout, "[%s] [TID:%s] [SyslogManager] 系统日志管理器正在关闭\n",
                   getCurrentTimestamp().c_str(), getCurrentThreadId().c_str());
            fflush(stdout);
        }

        closelog();
        initialized_ = false;
    }
}

void SyslogManager::setConsoleOutput(bool enable) {
    std::lock_guard<std::mutex> lock(logMutex_);
    consoleOutput_ = enable;
}

void SyslogManager::logMessage(int priority, const std::string& className, const char* format, ...) {
    if (!initialized_) {
        initialize();
    }

    std::lock_guard<std::mutex> lock(logMutex_);

    va_list args;
    va_start(args, format);

    std::string formattedMessage = formatLogMessage(priority, className, format, args);

    va_end(args);

    // 写入syslog
    syslog(priority, "%s", formattedMessage.c_str());
}

std::string SyslogManager::getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto microseconds = std::chrono::duration_cast<std::chrono::microseconds>(
        now.time_since_epoch()) % 1000000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(6) << microseconds.count();
    
    return ss.str();
}

std::string SyslogManager::getCurrentThreadId() {
    std::stringstream ss;
    
    // 使用Linux系统调用获取线程ID
    pid_t tid = static_cast<pid_t>(syscall(SYS_gettid));
    ss << tid;
    
    return ss.str();
}

std::string SyslogManager::formatLogMessage(int priority, const std::string& className, 
                                          const char* format, va_list args) {
    // 格式化用户消息
    char userMessage[MAX_LOG_SIZE];
    vsnprintf(userMessage, sizeof(userMessage), format, args);
    
    // 处理类名（去除C++名称修饰）
    std::string cleanClassName = className;
    if (className.find("N") == 0 || className.find("St") == 0) {
        // 尝试解析C++修饰名
        int status;
        char* demangled = abi::__cxa_demangle(className.c_str(), nullptr, nullptr, &status);
        if (status == 0 && demangled) {
            cleanClassName = demangled;
            free(demangled);
        }
    }
    
    // 构建完整的日志消息
    std::stringstream ss;
    ss << "[" << getCurrentTimestamp() << "] ";
    ss << "[TID:" << getCurrentThreadId() << "] ";
    ss << "[" << cleanClassName << "] ";
    ss << userMessage;
    
    return ss.str();
}
