#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <thread>
#include <chrono>
#include <set>
#include <map>
#include <atomic>
#include <mutex>
#include <vector>
#include <numeric>
#include <iostream>
#include <iomanip>
#include "GrpcServer.hpp"
#include "ServoControlManager.hpp"
#include "CANopenMaster.hpp"
#include "CANopenPara.hpp"
#include "motor_control.grpc.pb.h"
#include <fstream>
#include "MockCANopenMaster.h"
#include "MockServoControlManager.h"
#include "ErrorCodeMapper.hpp"


//  定义 GrpcServerTest 类，继承自 testing::Test 类
class GrpcServerTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        // 创建模拟的CANopenMaster和ServoControlManager

        mock_canopen_master_ = std::make_unique<MockCANopenMaster>("vcan0");
        // 使用 NiceMock 包装来抑制警告
        auto nice_mock = std::make_unique<::testing::NiceMock<MockServoControlManager>>(mock_canopen_master_.get());
        mock_servo_manager_ = std::move(nice_mock);

        // 创建GRPC服务器
        server_address_ = "localhost:50051";
        grpc_server_ = std::make_unique<GrpcServer>(server_address_, mock_servo_manager_.get());

        // 启动服务器
        ASSERT_TRUE(grpc_server_->start());

        // 等待服务器启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 创建客户端
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        stub_ = motor_control::BedMasterAppService::NewStub(channel);
    }

    void TearDown() override
    {
        grpc_server_->stop();
    }

    std::unique_ptr<MockCANopenMaster> mock_canopen_master_;
    std::unique_ptr<MockServoControlManager> mock_servo_manager_;
    std::unique_ptr<GrpcServer> grpc_server_;
    std::unique_ptr<motor_control::BedMasterAppService::Stub> stub_;
    std::string server_address_;
};

// 测试获取控制权功能
TEST_F(GrpcServerTest, GainControlTest)
{
    // clienttype为NONE
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "NONE");
        request.set_contextuid("test-gain-control-000");

        grpc::Status status = stub_->GainControl(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-gain-control-000");
        EXPECT_EQ(response.errorcode(), 0); // 成功
    }

    //  只获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;

        // 设置客户端类型
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-001");

        grpc::Status status = stub_->GainControl(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-gain-control-001");
        EXPECT_EQ(response.errorcode(), 0); // 成功

        // 相同Host第二次获取控制权
        grpc::ClientContext context2;              // 创建新的 ClientContext
        context2.AddMetadata("client-type", "CT"); // 为新 context 添加元数据
        grpc::Status status2 = stub_->GainControl(&context2, request, &response);
        EXPECT_TRUE(status2.ok());
        EXPECT_EQ(response.contextuid(), "test-gain-control-001");
        EXPECT_EQ(response.errorcode(), 0); // 成功
    }

    // 获取控制权，但是没有权限
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-gain-control-002");

        grpc::Status status = stub_->GainControl(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), ErrorCodeMapper::getErrorCode("GAIN_CONTROL_FAILED")); // 没有权限
        EXPECT_EQ(response.contextuid(), "test-gain-control-002");
    }
}

// 测试释放控制权功能
TEST_F(GrpcServerTest, ReleaseControlTest)
{
    // 先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-001");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-gain-control-001");
    }

    // 释放控制权，但是没有权限
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-gain-control-002");

        grpc::Status status = stub_->ReleaseControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), ErrorCodeMapper::getErrorCode("RELEASE_CONTROL_FAILED")); // 没有权限
        EXPECT_EQ(response.contextuid(), "test-gain-control-002");
    }

    // 然后释放控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-release-control-003");

        grpc::Status status = stub_->ReleaseControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-release-control-003");
        EXPECT_EQ(response.errorcode(), 0); // 成功
    }
}

// 测试StartMove功能
TEST_F(GrpcServerTest, StartMoveTest)
{
    using ::testing::_;
    using ::testing::Invoke;
    using ::testing::Return;

    EXPECT_CALL(*mock_servo_manager_, moveToPosition(_, _, _, _, _))
        .WillRepeatedly(Invoke([this](uint8_t nodeId, int32_t position, int velocity, bool absolute, bool immediate)
                               {

            std::cout << "Custom moveToPosition called" << std::endl;

            // 自定义业务逻辑
            mock_servo_manager_->setMockBedPosition(static_cast<BedType>(nodeId - 1), (float)position);

            return true; }));

    // 先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-move");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
    }

    // 测试StartMove一级床位置模式
    {
        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");

        request.set_contextuid("test-start-move-position");
        request.set_mode(motor_control::MotionMode::PositionMode);
        request.set_bedtype(motor_control::BedType::Primary);

        // 设置目标运动信息
        motor_control::MotionInfo *targetInfo = request.mutable_targetmotioninfo();
        targetInfo->set_postion(100.0); // 目标位置
        targetInfo->set_velocity(50.0); // 目标速度

        grpc::Status status = stub_->StartMove(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-start-move-position");
        EXPECT_EQ(response.errorcode(), 0); // 使用Mock，应该成功

        // 验证当前位置和速度信息
        EXPECT_FLOAT_EQ(response.currentmotioninfo().postion(), 100.0);
    }

    // 测试StartMove二级床速度模式
    {
        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");

        request.set_contextuid("test-start-move-velocity");
        request.set_mode(motor_control::MotionMode::VelocityMode);
        request.set_bedtype(motor_control::BedType::Secondary);

        // 设置目标运动信息
        motor_control::MotionInfo *targetInfo = request.mutable_targetmotioninfo();
        targetInfo->set_velocity(30.0); // 目标速度

        grpc::Status status = stub_->StartMove(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-start-move-velocity");
        EXPECT_EQ(response.errorcode(), 0); // 使用Mock，应该成功
    }

    // 测试StartMove回零模式
    {
        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");

        request.set_contextuid("test-start-move-homing");
        request.set_mode(motor_control::MotionMode::HomingMode);
        request.set_bedtype(motor_control::BedType::Both);

        grpc::Status status = stub_->StartMove(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-start-move-homing");
        EXPECT_EQ(response.errorcode(), 0); // 使用Mock，应该成功
    }

    // 测试控制权不匹配时StartMove
    {
        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");

        request.set_contextuid("test-start-move-control-mismatch");
        request.set_mode(motor_control::MotionMode::PositionMode);
        request.set_bedtype(motor_control::BedType::Primary);

        grpc::Status status = stub_->StartMove(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), ErrorCodeMapper::getErrorCode("START_MOVE_FAILED")); // PERMISSION_DENIED 错误码为 1
        EXPECT_EQ(response.contextuid(), "test-start-move-control-mismatch");
    }
}

// 测试StopMove功能
TEST_F(GrpcServerTest, StopMoveTest)
{
    // 先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-stop");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
    }

    // StartMove过程中通过StopMove来结束控床
    {
        // 执行StartMove
        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");

        request.set_contextuid("test-start-move-stop");
        request.set_mode(motor_control::MotionMode::PositionMode);
        request.set_bedtype(motor_control::BedType::Primary);

        // 设置目标运动信息
        motor_control::MotionInfo *targetInfo = request.mutable_targetmotioninfo();
        targetInfo->set_postion(100.0); // 目标位置
        targetInfo->set_velocity(50.0); // 目标速度

        // 创建线程来执行StartMove
        std::thread startMoveThread([&]()
                                    {
            grpc::Status status = stub_->StartMove(&context, request, &response);
            EXPECT_TRUE(status.ok());
            EXPECT_EQ(response.errorcode(), 0); });

        // 控制权不匹配时执行StopMove
        motor_control::CommonDescription request2;
        motor_control::CommonStatus response2;
        grpc::ClientContext context2;
        context2.AddMetadata("client-type", "PET");
        request2.set_contextuid("test-stop-move-control-mismatch");

        grpc::Status status2 = stub_->StopMove(&context2, request2, &response2);
        EXPECT_TRUE(status2.ok());
        EXPECT_EQ(response2.contextuid(), "test-stop-move-control-mismatch");
        EXPECT_EQ(response2.errorcode(), ErrorCodeMapper::getErrorCode("STOP_OPERATION_FAILED")); // PERMISSION_DENIED 错误码为 1

        // 控制权匹配时执行StopMove
        motor_control::CommonDescription request3;
        motor_control::CommonStatus response3;
        grpc::ClientContext context3;
        context3.AddMetadata("client-type", "CT");
        request3.set_contextuid("test-stop-move-control-match");

        grpc::Status status3 = stub_->StopMove(&context3, request3, &response3);

        EXPECT_TRUE(status3.ok());
        EXPECT_EQ(response3.contextuid(), "test-stop-move-control-match");
        EXPECT_EQ(response3.errorcode(), 0);

        // 等待StartMove线程执行完成
        startMoveThread.join();
    }
}

// 测试获取系统状态功能
TEST_F(GrpcServerTest, GetSystemStatusInfoTest)
{
    motor_control::CommonDescription request;
    motor_control::SystemStatusInfoStatus response;
    grpc::ClientContext context;

    context.AddMetadata("client-type", "CT");
    request.set_contextuid("test-system-status-001");

    grpc::Status status = stub_->GetSystemStatusInfo(&context, request, &response);

    EXPECT_TRUE(status.ok());
    EXPECT_FALSE(response.softwareversion().empty());
    EXPECT_FALSE(response.timestamp().empty());
    EXPECT_EQ(response.ownership(), motor_control::HostType::NONE); // 初始状态应该是NONE
}

// 测试GetTriggerInfo双向流服务
TEST_F(GrpcServerTest, GetTriggerInfoStreamTest)
{
    using ::testing::_;
    using ::testing::Invoke;
    using ::testing::Return;
    // 设置waitForTriggerInfoChange的默认行为
    EXPECT_CALL(*mock_servo_manager_, waitForTriggerInfoChange(_, _))
        .WillRepeatedly(Invoke([this](TriggerInfo &lastKnownInfo, int timeout_ms)
                               {
            std::cout << "Custom waitForTriggerInfoChange called" << std::endl;
            static uint32_t info = 0;
            lastKnownInfo.position = ++info;
            lastKnownInfo.timestamp = info++;
            lastKnownInfo.isValid = true;   

            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            return true; }));
    // .WillRepeatedly(Return(false));

    // CT先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-trigger-CT");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-gain-control-trigger-CT");
        EXPECT_EQ(response.errorcode(), 0);
    }

    // 无控制权时获取TriggerInfo
    {
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");

        motor_control::GetTriggerInfoDescription request;
        motor_control::TriggerInfoStatus response;
        request.set_contextuid("test-trigger-without-control");

        // 建立双向流
        auto stream = stub_->GetTriggerInfo(&context);
        EXPECT_FALSE(stream->Read(&response));
        EXPECT_TRUE(stream->Finish().error_code() == grpc::StatusCode::PERMISSION_DENIED);
    }

    // 测试CT双向流控制
    {
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        auto stream = stub_->GetTriggerInfo(&context);

        // 发送开始请求
        motor_control::GetTriggerInfoDescription request;
        request.set_contextuid("test-trigger-ct");
        EXPECT_TRUE(stream->Write(request));

        // 读取响应
        motor_control::TriggerInfoStatus response;
        std::thread readThread([&]() {
            if (stream->Read(&response))
            {
                EXPECT_EQ(response.contextuid(), "test-trigger-ct");
                // EXPECT_NE(response.triggertimestamp(), 0);
                // EXPECT_NE(response.triggerposition(), 0);
                // EXPECT_NE(response.exposuretime(), 0);
            } });

        // 发送EOF结束流
        motor_control::GetTriggerInfoDescription eofRequest;
        eofRequest.set_contextuid("STOP");
        stream->Write(eofRequest);
        stream->WritesDone();

        if (readThread.joinable())
        {
            readThread.join();
        }
        EXPECT_TRUE(stream->Finish().ok());
    }

    // CT释放控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-release-control-trigger-CT");

        grpc::Status status = stub_->ReleaseControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-release-control-trigger-CT");
    }

    // PET获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-gain-control-trigger-PET");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-gain-control-trigger-PET");
    }

    // PET双向流控制
    {
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        auto stream = stub_->GetTriggerInfo(&context);

        // 发送开始请求
        motor_control::GetTriggerInfoDescription request;
        request.set_contextuid("test-trigger-PET");
        EXPECT_TRUE(stream->Write(request));

        // 读取响应
        motor_control::TriggerInfoStatus response;
        std::thread readThread([&]()
                               {
            while (stream->Read(&response))
            {
                EXPECT_EQ(response.contextuid(), "test-trigger-PET");
                // EXPECT_NE(response.triggertimestamp(), 0);
                // EXPECT_NE(response.triggerposition(), 0);
                // EXPECT_NE(response.exposuretime(), 0);
            } });

        //  结束
        stream->WritesDone();
        if (readThread.joinable())
        {
            readThread.join();
        }

        // 设置超时，避免无限阻塞
        // context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(3));
        EXPECT_TRUE(stream->Finish().ok());
    }
}

// 测试心跳检测功能
TEST_F(GrpcServerTest, HeartBeatCheckTest)
{
    using ::testing::_;
    using ::testing::Invoke;
    using ::testing::Return;

    // 测试1: 无控制权时的心跳检测（应该返回权限拒绝错误）
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;

        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-heartbeat-no-control");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

        // 无控制权时心跳检测应该返回权限拒绝错误
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 1); // PERMISSION_DENIED 错误码为 1
        EXPECT_EQ(response.contextuid(), "test-heartbeat-no-control");
    }

    // 测试2: 获取控制权后的心跳检测（应该成功）
    {
        // 先获取控制权
        motor_control::CommonDescription gainRequest;
        motor_control::CommonStatus gainResponse;
        grpc::ClientContext gainContext;
        gainContext.AddMetadata("client-type", "CT");
        gainRequest.set_contextuid("test-gain-control-heartbeat");

        grpc::Status gainStatus = stub_->GainControl(&gainContext, gainRequest, &gainResponse);
        EXPECT_TRUE(gainStatus.ok());
        EXPECT_EQ(gainResponse.errorcode(), 0);
        EXPECT_EQ(gainResponse.contextuid(), "test-gain-control-heartbeat");

        // 设置心跳更新的期望行为
        // EXPECT_CALL(*mock_servo_manager_, updateHeartbeat(BedOwnerType::CT))
        //     .WillOnce(Return(true));

        // 执行心跳检测
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-heartbeat-with-control");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-heartbeat-with-control");
        EXPECT_EQ(response.errorcode(), 0);

        // 释放控制权
        motor_control::CommonDescription releaseRequest;
        motor_control::CommonStatus releaseResponse;
        grpc::ClientContext releaseContext;
        releaseContext.AddMetadata("client-type", "CT");
        releaseRequest.set_contextuid("test-release-control-heartbeat");

        grpc::Status releaseStatus = stub_->ReleaseControl(&releaseContext, releaseRequest, &releaseResponse);
        EXPECT_TRUE(releaseStatus.ok());
        EXPECT_EQ(releaseResponse.errorcode(), 0);
        EXPECT_EQ(releaseResponse.contextuid(), "test-release-control-heartbeat");
    }

    // 测试3: 不同客户端类型的心跳检测
    {
        // PET获取控制权
        motor_control::CommonDescription gainRequest;
        motor_control::CommonStatus gainResponse;
        grpc::ClientContext gainContext;
        gainContext.AddMetadata("client-type", "PET");
        gainRequest.set_contextuid("test-gain-control-pet");

        grpc::Status gainStatus = stub_->GainControl(&gainContext, gainRequest, &gainResponse);
        EXPECT_TRUE(gainStatus.ok());
        EXPECT_EQ(gainResponse.errorcode(), 0);
        EXPECT_EQ(gainResponse.contextuid(), "test-gain-control-pet");

        // 设置PET心跳更新的期望行为
        // EXPECT_CALL(*mock_servo_manager_, updateHeartbeat(BedOwnerType::PET))
        //     .WillOnce(Return(true));

        // PET执行心跳检测
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-heartbeat-pet");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.contextuid(), "test-heartbeat-pet");
        EXPECT_EQ(response.errorcode(), 0);

        // 释放控制权
        motor_control::CommonDescription releaseRequest;
        motor_control::CommonStatus releaseResponse;
        grpc::ClientContext releaseContext;
        releaseContext.AddMetadata("client-type", "PET");
        releaseRequest.set_contextuid("test-release-control-pet");

        grpc::Status releaseStatus = stub_->ReleaseControl(&releaseContext, releaseRequest, &releaseResponse);
        EXPECT_TRUE(releaseStatus.ok());
        EXPECT_EQ(releaseResponse.errorcode(), 0);
        EXPECT_EQ(releaseResponse.contextuid(), "test-release-control-pet");
    }

    // 测试4: 错误的客户端类型
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "UNKNOWN");
        request.set_contextuid("test-heartbeat-unknown");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

        // 未知客户端类型应该返回权限拒绝错误
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 1); // PERMISSION_DENIED 错误码为 1
        EXPECT_EQ(response.contextuid(), "test-heartbeat-unknown");
    }

    // 测试5: 心跳更新失败的情况
    {
        // SPECT获取控制权
        motor_control::CommonDescription gainRequest;
        motor_control::CommonStatus gainResponse;
        grpc::ClientContext gainContext;
        gainContext.AddMetadata("client-type", "SPECT");
        gainRequest.set_contextuid("test-gain-control-spect");

        grpc::Status gainStatus = stub_->GainControl(&gainContext, gainRequest, &gainResponse);
        EXPECT_TRUE(gainStatus.ok());
        EXPECT_EQ(gainResponse.errorcode(), 0);
        EXPECT_EQ(gainResponse.contextuid(), "test-gain-control-spect");

        // 设置心跳更新失败的期望行为
        // EXPECT_CALL(*mock_servo_manager_, updateHeartbeat(BedOwnerType::SPECT))
        //     .WillOnce(Return(false));

        // 执行心跳检测
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-heartbeat-update-failed");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);

        // 心跳更新失败应该返回权限拒绝错误
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), ErrorCodeMapper::getErrorCode("PERMISSION_DENIED"));
        EXPECT_EQ(response.contextuid(), "test-heartbeat-update-failed");

        // 释放控制权
        motor_control::CommonDescription releaseRequest;
        motor_control::CommonStatus releaseResponse;
        grpc::ClientContext releaseContext;
        releaseContext.AddMetadata("client-type", "SPECT");
        releaseRequest.set_contextuid("test-release-control-spect");

        grpc::Status releaseStatus = stub_->ReleaseControl(&releaseContext, releaseRequest, &releaseResponse);
        EXPECT_TRUE(releaseStatus.ok());
        EXPECT_EQ(releaseResponse.errorcode(), 0);
        EXPECT_EQ(releaseResponse.contextuid(), "test-release-control-spect");
    }
}

// 测试心跳超时和自动回零功能
TEST_F(GrpcServerTest, HeartbeatTimeoutAndAutoHomingTest)
{
    using ::testing::_;
    using ::testing::Invoke;
    using ::testing::Return;

    // 启动ServoManager
    EXPECT_TRUE(mock_servo_manager_->startManager("/mnt/hgfs/E/BedMaster/CANopenNodePorting/Configuration/servo_config.json"));

    // CT先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-timeout");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-gain-control-timeout");
    }

    // 发送心跳信号
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-heartbeat-timeout");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-heartbeat-timeout");
    }

    //  模拟心跳超时
    std::this_thread::sleep_for(std::chrono::seconds(6));

    //  心跳超时后再次发送心跳信号
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-heartbeat-timeout-after");

        grpc::Status status = stub_->HeartBeatCheck(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), ErrorCodeMapper::getErrorCode("PERMISSION_DENIED"));
        EXPECT_EQ(response.contextuid(), "test-heartbeat-timeout-after");
    }

    // PET获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "PET");
        request.set_contextuid("test-gain-control-pet-timeout");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-gain-control-pet-timeout");
    }

}

// 测试获取PostID
TEST_F(GrpcServerTest, GetPostIdTest)
{
    motor_control::GetPostIdDescription request;
    motor_control::GetPostIdStatus response;
    grpc::ClientContext context;

    context.AddMetadata("client-type", "CT");
    request.set_contextuid("test-get-post-id-001");

    grpc::Status status = stub_->GetPostId(&context, request, &response);

    EXPECT_TRUE(status.ok());
    EXPECT_EQ(response.contextuid(), "test-get-post-id-001");
    EXPECT_NE(response.postid().did(), 0);
}

// 测试流式通信性能 - 正确的服务端推送流模式
TEST_F(GrpcServerTest, StreamPerformanceTest)
{
    using ::testing::_;
    using ::testing::Invoke;
    using ::testing::Return;

    // 测试参数
    const int concurrent_streams = 3;
    const int messages_per_stream = 50;
    const int test_duration_seconds = 5;

    // 性能统计变量（原子类型保证线程安全）
    std::atomic<int> total_messages{0};
    std::atomic<int> total_errors{0};
    std::atomic<int> streams_established{0};
    std::atomic<bool> test_running{true};

    // 时间测量容器（使用互斥锁保护）
    std::mutex metrics_mutex;
    std::vector<std::chrono::microseconds> stream_establishment_times;
    std::vector<std::chrono::microseconds> message_latencies;

    // 设置waitForTriggerInfoChange的模拟行为 - 快速推送消息
    EXPECT_CALL(*mock_servo_manager_, waitForTriggerInfoChange(_, _))
        .WillRepeatedly(Invoke([&](TriggerInfo &lastKnownInfo, int timeout_ms) -> bool {
            if (!test_running.load()) {
                return false;
            }

            // 模拟快速的触发信息更新
            static std::atomic<uint32_t> counter{0};
            uint32_t current = counter.fetch_add(1);

            lastKnownInfo.position = current * 100;  // 位置递增
            lastKnownInfo.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count();
            lastKnownInfo.triggerTimestamp = lastKnownInfo.timestamp;
            lastKnownInfo.interval = 50;  // 50ms间隔
            lastKnownInfo.isValid = true;

            // 短暂延迟模拟真实场景
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
            return true;
        }));

    // 先获取控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-gain-control-performance");

        grpc::Status status = stub_->GainControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-gain-control-performance");
    }

    auto test_start_time = std::chrono::steady_clock::now();

    // 创建并发流
    std::vector<std::thread> stream_threads;

    for (int i = 0; i < concurrent_streams; ++i) {
        stream_threads.emplace_back([&, i]() {
            try {
                // 建立流连接
                grpc::ClientContext context;
                context.AddMetadata("client-type", "CT");

                auto stream_start_time = std::chrono::steady_clock::now();
                auto stream = stub_->GetTriggerInfo(&context);

                // 发送开始请求（只发送一次）
                motor_control::GetTriggerInfoDescription request;
                request.set_contextuid("performance-test-stream-" + std::to_string(i));

                if (!stream->Write(request)) {
                    total_errors.fetch_add(1);
                    return;
                }

                // 进入消息接收循环（被动接收服务端推送）
                motor_control::TriggerInfoStatus response;
                int messages_received = 0;
                bool first_message_received = false;

                while (test_running.load() && messages_received < messages_per_stream) {
                    auto read_start_time = std::chrono::steady_clock::now();

                    if (stream->Read(&response)) {
                        auto read_end_time = std::chrono::steady_clock::now();

                        // 记录第一条消息的流建立时间
                        if (!first_message_received) {
                            auto establishment_time = std::chrono::duration_cast<std::chrono::microseconds>(
                                read_end_time - stream_start_time);

                            {
                                std::lock_guard<std::mutex> lock(metrics_mutex);
                                stream_establishment_times.push_back(establishment_time);
                            }

                            streams_established.fetch_add(1);
                            first_message_received = true;
                        }

                        // 记录消息接收延迟
                        auto latency = std::chrono::duration_cast<std::chrono::microseconds>(
                            read_end_time - read_start_time);

                        {
                            std::lock_guard<std::mutex> lock(metrics_mutex);
                            message_latencies.push_back(latency);
                        }

                        messages_received++;
                        total_messages.fetch_add(1);

                        // 验证响应内容
                        EXPECT_EQ(response.contextuid(), "performance-test-stream-" + std::to_string(i));
                        EXPECT_EQ(response.errorcode(), 0);
                        EXPECT_GE(response.triggerposition(), 0);
                        EXPECT_GT(response.triggertimestamp(), 0);

                    } else {
                        // 读取失败，可能是流结束或错误
                        break;
                    }
                }

                // 发送结束请求
                motor_control::GetTriggerInfoDescription stop_request;
                stop_request.set_contextuid("STOP");
                stream->Write(stop_request);
                stream->WritesDone();

                // 关键：继续读取服务端可能还在发送的数据
                while (stream->Read(&response)) {
                    // 读空所有剩余数据，但不处理
                    // 这样可以让服务端的Write()不再阻塞
                }

                // 现在Finish()应该不会阻塞了
                auto finish_status = stream->Finish();
                if (!finish_status.ok()) {
                    total_errors.fetch_add(1);
                }

            } catch (const std::exception& e) {
                total_errors.fetch_add(1);
                std::cerr << "Stream thread " << i << " exception: " << e.what() << std::endl;
            }
        });
    }

    // 运行指定时间后停止测试
    std::this_thread::sleep_for(std::chrono::seconds(test_duration_seconds));
    test_running.store(false);

    // 等待所有线程完成
    for (auto& t : stream_threads) {
        if (t.joinable()) {
            t.join();
        }
    }

    auto test_end_time = std::chrono::steady_clock::now();
    auto total_test_time = std::chrono::duration_cast<std::chrono::milliseconds>(
        test_end_time - test_start_time);

    // 计算性能指标
    {
        std::lock_guard<std::mutex> lock(metrics_mutex);

        // 流建立时间统计
        if (!stream_establishment_times.empty()) {
            auto avg_establishment_time = std::accumulate(
                stream_establishment_times.begin(),
                stream_establishment_times.end(),
                std::chrono::microseconds{0}) / stream_establishment_times.size();

            std::cout << "=== 流式通信性能测试结果 ===" << std::endl;
            std::cout << "并发流数量: " << concurrent_streams << std::endl;
            std::cout << "每流期望消息数: " << messages_per_stream << std::endl;
            std::cout << "测试持续时间: " << total_test_time.count() << " ms" << std::endl;
            std::cout << "成功建立的流: " << streams_established.load() << "/" << concurrent_streams << std::endl;
            std::cout << "平均流建立时间: " << avg_establishment_time.count() << " μs" << std::endl;
        }

        // 消息延迟统计
        if (!message_latencies.empty()) {
            auto avg_latency = std::accumulate(
                message_latencies.begin(),
                message_latencies.end(),
                std::chrono::microseconds{0}) / message_latencies.size();

            std::cout << "总接收消息数: " << total_messages.load() << std::endl;
            std::cout << "平均消息接收延迟: " << avg_latency.count() << " μs" << std::endl;

            // 计算QPS
            double qps = static_cast<double>(total_messages.load()) /
                        (static_cast<double>(total_test_time.count()) / 1000.0);
            std::cout << "消息接收QPS: " << std::fixed << std::setprecision(2) << qps << std::endl;
        }

        std::cout << "错误数量: " << total_errors.load() << std::endl;

        // 错误率计算
        if (total_messages.load() > 0) {
            double error_rate = static_cast<double>(total_errors.load()) /
                               static_cast<double>(total_messages.load() + total_errors.load()) * 100.0;
            std::cout << "错误率: " << std::fixed << std::setprecision(2) << error_rate << "%" << std::endl;
        }
        std::cout << "=========================" << std::endl;
    }

    // 性能断言
    EXPECT_GT(streams_established.load(), 0) << "至少应该建立一个流连接";
    EXPECT_GT(total_messages.load(), 0) << "应该接收到消息";
    EXPECT_LT(total_errors.load(), total_messages.load()) << "错误数量应该少于成功消息数量";

    // 释放控制权
    {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        grpc::ClientContext context;
        context.AddMetadata("client-type", "CT");
        request.set_contextuid("test-release-control-performance");

        grpc::Status status = stub_->ReleaseControl(&context, request, &response);
        EXPECT_TRUE(status.ok());
        EXPECT_EQ(response.errorcode(), 0);
        EXPECT_EQ(response.contextuid(), "test-release-control-performance");
    }
}

int main(int argc, char **argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}

