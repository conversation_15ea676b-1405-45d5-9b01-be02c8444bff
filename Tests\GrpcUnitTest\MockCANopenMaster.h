#ifndef MOCK_CANOPEN_MASTER_H
#define MOCK_CANOPEN_MASTER_H

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <set>
#include <map>
#include "CANopenMaster.hpp"

// Mock class for CANopenMaster to simulate CANopen behavior
class MockCANopenMaster : public CANopenMaster
{
public:
    explicit MockCANopenMaster(const std::string &canInterface)
        : CANopenMaster(canInterface)
    {
        // Initialize mock state
        resetMockState();

        // Setup default behavior
        setupDefaultBehavior();
    }

    // Use gmock to define all methods that need to be mocked
    MOCK_METHOD(bool, init, (), (override));
    MOCK_METHOD(bool, start, (), (override));
    MOCK_METHOD(bool, stop, (), (override));
    MOCK_METHOD(bool, addNode, (uint8_t nodeId), (override));
    MOCK_METHOD(bool, findNode, (uint8_t nodeId), (override));
    MOCK_METHOD(bool, initializeNMTState, (uint8_t nodeId), (override));

    // DS402 state machine related methods
    MOCK_METHOD(bool, sendControlWord, (uint8_t nodeId, uint16_t controlWord), (override));
    MOCK_METHOD(bool, sendControlWordCommand, (uint8_t nodeId, DS402Command command), (override));
    MOCK_METHOD(DS402State, getState, (uint8_t nodeId), (override));

    // Operation mode related methods
    MOCK_METHOD(bool, setOperationMode, (uint8_t nodeId, DS402OperationMode mode), (override));
    MOCK_METHOD(DS402OperationMode, getOperationMode, (uint8_t nodeId), (override));

    // Position control related methods
    MOCK_METHOD(bool, setTargetPosition, (uint8_t nodeId, int32_t position), (override));
    MOCK_METHOD(int32_t, getActualPosition, (uint8_t nodeId), (override));

    // Velocity control related methods
    MOCK_METHOD(bool, setTargetVelocity, (uint8_t nodeId, int32_t velocity), (override));
    MOCK_METHOD(int32_t, getActualVelocity, (uint8_t nodeId), (override));

    // Homing operation related methods
    MOCK_METHOD(bool, setHomingMethod, (uint8_t nodeId, int8_t method), (override));

    // Kinematic parameter setting methods
    MOCK_METHOD(bool, setProfileVelocity, (uint8_t nodeId, uint32_t velocity), (override));
    MOCK_METHOD(bool, setProfileAcceleration, (uint8_t nodeId, uint32_t acceleration), (override));
    MOCK_METHOD(bool, setProfileDeceleration, (uint8_t nodeId, uint32_t deceleration), (override));

    // Setup default behavior
    void setupDefaultBehavior()
    {
        using ::testing::_;
        using ::testing::Invoke;
        using ::testing::Return;

        // Basic operation default behavior
        ON_CALL(*this, init()).WillByDefault(Return(true));
        ON_CALL(*this, start()).WillByDefault(Return(true));
        ON_CALL(*this, stop()).WillByDefault(Return(true));
        ON_CALL(*this, initializeNMTState(_)).WillByDefault(Return(true));

        // Node management default behavior
        ON_CALL(*this, addNode(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                        {
            mock_nodes.insert(nodeId);
            return true; }));

        ON_CALL(*this, findNode(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                         { return mock_nodes.find(nodeId) != mock_nodes.end(); }));

        // DS402 state machine default behavior
        ON_CALL(*this, sendControlWord(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, uint16_t controlWord)
                                                                   {
            mock_control_words[nodeId] = controlWord;
            return true; }));

        ON_CALL(*this, sendControlWordCommand(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, DS402Command command)
                                                                          {
            mock_commands[nodeId] = command;
            updateStateFromCommand(nodeId, command);
            return true; }));

        ON_CALL(*this, getState(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                         { return mock_states[nodeId]; }));

        // Operation mode default behavior
        ON_CALL(*this, setOperationMode(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, DS402OperationMode mode)
                                                                    {
            mock_op_modes[nodeId] = mode;
            return true; }));

        ON_CALL(*this, getOperationMode(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                                 { return mock_op_modes[nodeId]; }));

        // Position control default behavior
        ON_CALL(*this, setTargetPosition(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, int32_t position)
                                                                     {
            mock_target_positions[nodeId] = position;
            mock_actual_positions[nodeId] = position; // Automatically update current position
            return true; }));

        ON_CALL(*this, getActualPosition(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                                  { return mock_actual_positions[nodeId]; }));

        // Velocity control default behavior
        ON_CALL(*this, setTargetVelocity(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, int32_t velocity)
                                                                     {
            mock_target_velocities[nodeId] = velocity;
            mock_actual_velocities[nodeId] = velocity; // Automatically update current velocity
            return true; }));

        ON_CALL(*this, getActualVelocity(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                                  { return mock_actual_velocities[nodeId]; }));

        // Other parameter setting default behavior
        ON_CALL(*this, setHomingMethod(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, int8_t method)
                                                                   {
            mock_homing_methods[nodeId] = method;
            return true; }));

        ON_CALL(*this, setProfileVelocity(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, uint32_t velocity)
                                                                      {
            mock_profile_velocities[nodeId] = velocity;
            return true; }));

        ON_CALL(*this, setProfileAcceleration(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, uint32_t acceleration)
                                                                          {
            mock_profile_accelerations[nodeId] = acceleration;
            return true; }));

        ON_CALL(*this, setProfileDeceleration(_, _)).WillByDefault(Invoke([this](uint8_t nodeId, uint32_t deceleration)
                                                                          {
            mock_profile_decelerations[nodeId] = deceleration;
            return true; }));
    }

    // Reset mock state
    void resetMockState()
    {
        mock_nodes.clear();
        mock_control_words.clear();
        mock_commands.clear();
        mock_states.clear();
        mock_op_modes.clear();
        mock_target_positions.clear();
        mock_actual_positions.clear();
        mock_target_velocities.clear();
        mock_actual_velocities.clear();
        mock_homing_methods.clear();
        mock_profile_velocities.clear();
        mock_profile_accelerations.clear();
        mock_profile_decelerations.clear();

        // Default value settings
        for (int i = 1; i <= 127; i++)
        {
            mock_states[i] = DS402State::NOT_READY_TO_SWITCH_ON;
        }
    }

    // Set mock state machine state
    void setMockState(uint8_t nodeId, DS402State state)
    {
        mock_states[nodeId] = state;
    }

    // Set mock actual position
    void setMockPosition(uint8_t nodeId, int32_t position)
    {
        mock_actual_positions[nodeId] = position;
    }

    // Set mock actual velocity
    void setMockVelocity(uint8_t nodeId, int32_t velocity)
    {
        mock_actual_velocities[nodeId] = velocity;
    }

private:
    // Internal mock state storage
    std::set<uint8_t> mock_nodes;
    std::map<uint8_t, uint16_t> mock_control_words;
    std::map<uint8_t, DS402Command> mock_commands;
    std::map<uint8_t, DS402State> mock_states;
    std::map<uint8_t, DS402OperationMode> mock_op_modes;
    std::map<uint8_t, int32_t> mock_target_positions;
    std::map<uint8_t, int32_t> mock_actual_positions;
    std::map<uint8_t, int32_t> mock_target_velocities;
    std::map<uint8_t, int32_t> mock_actual_velocities;
    std::map<uint8_t, int8_t> mock_homing_methods;
    std::map<uint8_t, uint32_t> mock_profile_velocities;
    std::map<uint8_t, uint32_t> mock_profile_accelerations;
    std::map<uint8_t, uint32_t> mock_profile_decelerations;

    // Update state machine state based on command
    void updateStateFromCommand(uint8_t nodeId, DS402Command command)
    {
        switch (command)
        {
        case DS402Command::SHUTDOWN:
            mock_states[nodeId] = DS402State::READY_TO_SWITCH_ON;
            break;
        case DS402Command::SWITCH_ON:
            mock_states[nodeId] = DS402State::SWITCHED_ON;
            break;
        case DS402Command::ENABLE_OPERATION:
            mock_states[nodeId] = DS402State::OPERATION_ENABLED;
            break;
        case DS402Command::DISABLE_OPERATION:
            mock_states[nodeId] = DS402State::SWITCHED_ON;
            break;
        case DS402Command::DISABLE_VOLTAGE:
            mock_states[nodeId] = DS402State::SWITCH_ON_DISABLED;
            break;
        case DS402Command::QUICK_STOP:
            mock_states[nodeId] = DS402State::QUICK_STOP_ACTIVE;
            break;
        case DS402Command::FAULT_RESET:
            mock_states[nodeId] = DS402State::SWITCH_ON_DISABLED;
            break;
        default:
            // Keep current state
            break;
        }
    }
};

#endif // MOCK_CANOPEN_MASTER_H
