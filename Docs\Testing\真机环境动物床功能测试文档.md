# 真机环境动物床功能测试文档

## 1. 测试环境准备

### 1.1 硬件环境
- 一级床驱动器 (节点ID: 0x06)
- 二级床驱动器 (节点ID: 0x0A)
- CAN总线适配器
- 限位开关、编码器、紧急停止按钮

### 1.2 软件环境
```bash
# 启动主程序
sudo ./BedMasterApp --config=/etc/bedmaster/servo_config.json --can-interface=can0

# 启动GUI测试工具
cd Tools/Gui
python motor_control_gui.py
```

### 1.3 测试工具
- GUI测试工具 (`motor_control_gui.py`)
- CAN监控工具 (`candump can0`)

## 2. 测试用例设计

### 2.1 运动控制测试

#### TC-001: 一级床位置模式测试
**输入参数:**
- 床类型: Primary (一级床)
- 运动模式: Position (位置)
- 目标位置: 100000 (100mm)
- 目标速度: 50000

**测试步骤:**
1. GUI连接服务器
2. 获取控制权
3. 设置输入参数
4. 点击"开始移动"
5. 等待运动完成

**预期输出:**
- 运动状态: 就绪 → 移动中 → 就绪
- 最终位置: 100000 ± 100 (误差≤0.1mm)
- 运动时间: < 30秒
- 无异常报警

#### TC-002: 一级床速度模式测试
**输入参数:**
- 床类型: Primary (一级床)
- 运动模式: Velocity (速度)
- 目标速度: 30000
- 目标位置: 200000

**预期输出:**
- 运动状态: 移动中
- 实际速度: 30000 ± 1500 (误差≤5%)
- 停止响应时间: < 100ms

#### TC-003: 一级床回零模式测试
**输入参数:**
- 床类型: Primary (一级床)
- 运动模式: Homing (回零)

**预期输出:**
- 回零完成位置: 0 ± 50
- 回零时间: < 60秒
- 运动状态: 就绪

#### TC-004: 二级床位置模式测试
**输入参数:**
- 床类型: Secondary (二级床)
- 运动模式: Position (位置)
- 目标位置: 80000 (80mm)
- 目标速度: 40000

**预期输出:**
- 最终位置: 80000 ± 100
- 运动状态: 就绪
- 无异常报警

#### TC-005: 二级床速度模式测试
**输入参数:**
- 床类型: Secondary (二级床)
- 运动模式: Velocity (速度)
- 目标速度: 25000
- 目标位置: 150000

**预期输出:**
- 实际速度: 25000 ± 1250
- 运动状态: 移动中

#### TC-006: 二级床回零模式测试
**输入参数:**
- 床类型: Secondary (二级床)
- 运动模式: Homing (回零)

**预期输出:**
- 回零完成位置: 0 ± 50
- 回零时间: < 60秒

#### TC-007: 双床顺序控制测试
**输入参数:**
- 床类型: Both (两级床)
- 运动模式: Position (位置)
- 目标位置: 50000
- 目标速度: 30000

**预期输出:**
- 执行顺序: 先二级床，后一级床
- 两床最终位置: 50000 ± 100
- 运动状态: 就绪

### 2.2 权限管理测试

#### TC-010: 控制权获取释放测试
**输入参数:**
- 客户端类型: CT
- 操作: 获取控制权 → 释放控制权

**预期输出:**
- 获取后当前控制者: CT
- 释放后当前控制者: 无控制者
- 操作响应时间: < 150ms

#### TC-011: 权限冲突测试
**输入参数:**
- 客户端A: CT (已获取控制权)
- 客户端B: PET (尝试获取控制权)

**预期输出:**
- 客户端B获取控制权: 失败
- 客户端B移动操作: 被拒绝
- 当前控制者: CT

### 2.3 状态监控测试

#### TC-020: 实时状态监控测试
**输入参数:**
- 自动刷新: 启用 (1秒间隔)
- 执行移动操作

**预期输出:**
- 位置信息: 实时更新
- 状态变化: 准确反映
- 刷新频率: 约1秒

#### TC-021: ESTOP状态监控测试
**输入参数:**
- 运动过程中按下硬件急停按钮

**预期输出:**
- 运动状态: 紧急停止
- 状态颜色: 橙色
- 响应时间: < 50ms

#### TC-022: Trigger信号监控测试
**输入参数:**
- 启动Trigger监控
- 一级床移动过程中触发信号

**预期输出:**
- Trigger位置: 准确记录
- 时间戳: 正确
- 信号有效性: true

### 2.4 异常处理测试

#### TC-030: 软件限位保护测试
**输入参数:**
- 目标位置: 超出配置限位范围
- 一级床限位: ±1000000
- 二级床限位: ±800000

**预期输出:**
- 操作结果: 被拒绝
- 错误信息: 超出限位范围
- 床体状态: 无移动

#### TC-031: 硬件限位保护测试
**输入参数:**
- 移动到接近硬件限位位置

**预期输出:**
- 运动状态: 立即停止
- 错误状态: 限位触发
- 保护动作: 有效

#### TC-032: 通信中断恢复测试
**输入参数:**
- 人为中断网络连接
- 恢复网络连接

**预期输出:**
- 中断时连接状态: 连接中断
- 恢复后状态: 自动重连
- 功能恢复: 正常

## 3. 测试结果记录模板

### 3.1 测试用例执行记录
```
测试用例: TC-XXX
执行日期: ____/____/____
执行人员: __________

输入参数记录:
- 床类型: __________
- 运动模式: __________
- 目标位置: __________
- 目标速度: __________

输出结果记录:
- 实际位置: __________
- 位置误差: __________
- 运动时间: __________
- 运动状态: __________
- 异常信息: __________

测试结果: □ 通过 □ 失败
备注: ____________________
```

### 3.2 性能数据记录
```json
{
  "testCase": "TC-XXX",
  "input": {
    "bedType": "Primary",
    "motionMode": "Position", 
    "targetPosition": 100000,
    "targetVelocity": 50000
  },
  "output": {
    "actualPosition": 99998,
    "positionError": -2,
    "responseTime": 45.2,
    "movementTime": 12.5,
    "motionStatus": "Ready"
  },
  "result": "PASS"
}
```

## 4. 测试通过标准

### 4.1 功能测试标准
- 位置控制精度: ±0.1mm
- 速度控制精度: ±5%
- 响应时间: < 200ms
- 回零精度: ±0.05mm

### 4.2 安全测试标准
- 急停响应时间: < 50ms
- 限位保护: 100%有效
- 权限控制: 无越权操作

### 4.3 性能测试标准
- 系统可用性: > 99%
- 连续运行时间: > 24小时
- 内存泄漏: 无

## 5. 测试报告格式

```
测试总结:
- 测试用例总数: _____ 个
- 通过用例: _____ 个
- 失败用例: _____ 个
- 通过率: _____ %

主要测试结果:
- 一级床功能: □ 正常 □ 异常
- 二级床功能: □ 正常 □ 异常  
- 双床协同: □ 正常 □ 异常
- 权限管理: □ 正常 □ 异常
- 状态监控: □ 正常 □ 异常
- 异常处理: □ 正常 □ 异常

存在问题:
1. ____________________
2. ____________________

改进建议:
1. ____________________
2. ____________________
```
