// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ServoConfigParameters.proto

#include "ServoConfigParameters.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace ServoConfigProto {
constexpr PositionModeConfig::PositionModeConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profile_velocity_(0)
  , profile_acceleration_(0)
  , profile_deceleration_(0)
  , use_limit_switches_(false)
  , positive_limit_(0)
  , negative_limit_(0)
  , position_tracking_window_(0)
  , position_tracking_window_time_(0){}
struct PositionModeConfigDefaultTypeInternal {
  constexpr PositionModeConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PositionModeConfigDefaultTypeInternal() {}
  union {
    PositionModeConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PositionModeConfigDefaultTypeInternal _PositionModeConfig_default_instance_;
constexpr VelocityModeConfig::VelocityModeConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : profile_acceleration_(0)
  , profile_deceleration_(0)
  , max_velocity_(0)
  , min_velocity_(0){}
struct VelocityModeConfigDefaultTypeInternal {
  constexpr VelocityModeConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VelocityModeConfigDefaultTypeInternal() {}
  union {
    VelocityModeConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VelocityModeConfigDefaultTypeInternal _VelocityModeConfig_default_instance_;
constexpr HomingConfig::HomingConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : method_(0)
  , speed_switch_(0)
  , speed_zero_(0)
  , acceleration_(0)
  , offset_(0){}
struct HomingConfigDefaultTypeInternal {
  constexpr HomingConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HomingConfigDefaultTypeInternal() {}
  union {
    HomingConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HomingConfigDefaultTypeInternal _HomingConfig_default_instance_;
constexpr InputPinConfig::InputPinConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : pin_index_(0)
  , configuration_(0)
  , debounce_values_(0u){}
struct InputPinConfigDefaultTypeInternal {
  constexpr InputPinConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~InputPinConfigDefaultTypeInternal() {}
  union {
    InputPinConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT InputPinConfigDefaultTypeInternal _InputPinConfig_default_instance_;
constexpr TouchProbeConfig::TouchProbeConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : probe_index_(0)
  , function_(0)
  , io_select_(0)
  , debounce_values_(0u){}
struct TouchProbeConfigDefaultTypeInternal {
  constexpr TouchProbeConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TouchProbeConfigDefaultTypeInternal() {}
  union {
    TouchProbeConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TouchProbeConfigDefaultTypeInternal _TouchProbeConfig_default_instance_;
constexpr PDOMappingEntry::PDOMappingEntry(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : object_index_(0u)
  , sub_index_(0u)
  , data_length_(0u){}
struct PDOMappingEntryDefaultTypeInternal {
  constexpr PDOMappingEntryDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PDOMappingEntryDefaultTypeInternal() {}
  union {
    PDOMappingEntry _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PDOMappingEntryDefaultTypeInternal _PDOMappingEntry_default_instance_;
constexpr TPDOConfig::TPDOConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : mapping_entries_()
  , pdo_number_(0u)
  , transmission_type_(0u){}
struct TPDOConfigDefaultTypeInternal {
  constexpr TPDOConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TPDOConfigDefaultTypeInternal() {}
  union {
    TPDOConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TPDOConfigDefaultTypeInternal _TPDOConfig_default_instance_;
constexpr ServoMotorConfig::ServoMotorConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : input_pins_()
  , touch_probes_()
  , tpdo_config_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , position_mode_(nullptr)
  , velocity_mode_(nullptr)
  , homing_(nullptr)
  , node_id_(0u)
  , encoder_resolution_(0){}
struct ServoMotorConfigDefaultTypeInternal {
  constexpr ServoMotorConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ServoMotorConfigDefaultTypeInternal() {}
  union {
    ServoMotorConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ServoMotorConfigDefaultTypeInternal _ServoMotorConfig_default_instance_;
constexpr HeartbeatConfig::HeartbeatConfig(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timeout_ms_(0u)
  , check_interval_ms_(0u)
  , enable_auto_homing_(false)
  , enable_heartbeat_monitor_(false)
  , max_missed_heartbeats_(0u){}
struct HeartbeatConfigDefaultTypeInternal {
  constexpr HeartbeatConfigDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HeartbeatConfigDefaultTypeInternal() {}
  union {
    HeartbeatConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HeartbeatConfigDefaultTypeInternal _HeartbeatConfig_default_instance_;
constexpr ServoConfigMessage::ServoConfigMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : servo_motors_()
  , heartbeat_config_(nullptr){}
struct ServoConfigMessageDefaultTypeInternal {
  constexpr ServoConfigMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ServoConfigMessageDefaultTypeInternal() {}
  union {
    ServoConfigMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ServoConfigMessageDefaultTypeInternal _ServoConfigMessage_default_instance_;
}  // namespace ServoConfigProto
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_ServoConfigParameters_2eproto[10];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_ServoConfigParameters_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_ServoConfigParameters_2eproto = nullptr;

const uint32_t TableStruct_ServoConfigParameters_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, profile_velocity_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, profile_acceleration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, profile_deceleration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, use_limit_switches_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, positive_limit_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, negative_limit_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, position_tracking_window_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PositionModeConfig, position_tracking_window_time_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::VelocityModeConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::VelocityModeConfig, profile_acceleration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::VelocityModeConfig, profile_deceleration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::VelocityModeConfig, max_velocity_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::VelocityModeConfig, min_velocity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, method_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, speed_switch_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, speed_zero_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, acceleration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HomingConfig, offset_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::InputPinConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::InputPinConfig, pin_index_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::InputPinConfig, configuration_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::InputPinConfig, debounce_values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TouchProbeConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TouchProbeConfig, probe_index_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TouchProbeConfig, function_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TouchProbeConfig, io_select_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TouchProbeConfig, debounce_values_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PDOMappingEntry, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PDOMappingEntry, object_index_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PDOMappingEntry, sub_index_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::PDOMappingEntry, data_length_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TPDOConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TPDOConfig, pdo_number_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TPDOConfig, transmission_type_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::TPDOConfig, mapping_entries_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, node_id_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, name_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, type_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, encoder_resolution_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, position_mode_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, velocity_mode_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, homing_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, input_pins_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, touch_probes_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoMotorConfig, tpdo_config_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, timeout_ms_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, check_interval_ms_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, enable_auto_homing_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, enable_heartbeat_monitor_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::HeartbeatConfig, max_missed_heartbeats_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoConfigMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoConfigMessage, servo_motors_),
  PROTOBUF_FIELD_OFFSET(::ServoConfigProto::ServoConfigMessage, heartbeat_config_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::ServoConfigProto::PositionModeConfig)},
  { 14, -1, -1, sizeof(::ServoConfigProto::VelocityModeConfig)},
  { 24, -1, -1, sizeof(::ServoConfigProto::HomingConfig)},
  { 35, -1, -1, sizeof(::ServoConfigProto::InputPinConfig)},
  { 44, -1, -1, sizeof(::ServoConfigProto::TouchProbeConfig)},
  { 54, -1, -1, sizeof(::ServoConfigProto::PDOMappingEntry)},
  { 63, -1, -1, sizeof(::ServoConfigProto::TPDOConfig)},
  { 72, -1, -1, sizeof(::ServoConfigProto::ServoMotorConfig)},
  { 88, -1, -1, sizeof(::ServoConfigProto::HeartbeatConfig)},
  { 99, -1, -1, sizeof(::ServoConfigProto::ServoConfigMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_PositionModeConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_VelocityModeConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_HomingConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_InputPinConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_TouchProbeConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_PDOMappingEntry_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_TPDOConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_ServoMotorConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_HeartbeatConfig_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::ServoConfigProto::_ServoConfigMessage_default_instance_),
};

const char descriptor_table_protodef_ServoConfigParameters_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033ServoConfigParameters.proto\022\020ServoConf"
  "igProto\"\377\001\n\022PositionModeConfig\022\030\n\020profil"
  "e_velocity\030\001 \001(\005\022\034\n\024profile_acceleration"
  "\030\002 \001(\005\022\034\n\024profile_deceleration\030\003 \001(\005\022\032\n\022"
  "use_limit_switches\030\004 \001(\010\022\026\n\016positive_lim"
  "it\030\005 \001(\005\022\026\n\016negative_limit\030\006 \001(\005\022 \n\030posi"
  "tion_tracking_window\030\007 \001(\005\022%\n\035position_t"
  "racking_window_time\030\010 \001(\005\"|\n\022VelocityMod"
  "eConfig\022\034\n\024profile_acceleration\030\001 \001(\005\022\034\n"
  "\024profile_deceleration\030\002 \001(\005\022\024\n\014max_veloc"
  "ity\030\003 \001(\005\022\024\n\014min_velocity\030\004 \001(\005\"n\n\014Homin"
  "gConfig\022\016\n\006method\030\001 \001(\005\022\024\n\014speed_switch\030"
  "\002 \001(\005\022\022\n\nspeed_zero\030\003 \001(\005\022\024\n\014acceleratio"
  "n\030\004 \001(\005\022\016\n\006offset\030\005 \001(\005\"S\n\016InputPinConfi"
  "g\022\021\n\tpin_index\030\001 \001(\005\022\025\n\rconfiguration\030\002 "
  "\001(\005\022\027\n\017debounce_values\030\003 \001(\r\"e\n\020TouchPro"
  "beConfig\022\023\n\013probe_index\030\001 \001(\005\022\020\n\010functio"
  "n\030\002 \001(\005\022\021\n\tio_select\030\003 \001(\005\022\027\n\017debounce_v"
  "alues\030\004 \001(\r\"O\n\017PDOMappingEntry\022\024\n\014object"
  "_index\030\001 \001(\r\022\021\n\tsub_index\030\002 \001(\r\022\023\n\013data_"
  "length\030\003 \001(\r\"w\n\nTPDOConfig\022\022\n\npdo_number"
  "\030\001 \001(\r\022\031\n\021transmission_type\030\002 \001(\r\022:\n\017map"
  "ping_entries\030\003 \003(\0132!.ServoConfigProto.PD"
  "OMappingEntry\"\250\003\n\020ServoMotorConfig\022\017\n\007no"
  "de_id\030\001 \001(\r\022\014\n\004name\030\002 \001(\t\022\014\n\004type\030\003 \001(\t\022"
  "\032\n\022encoder_resolution\030\004 \001(\005\022;\n\rposition_"
  "mode\030\005 \001(\0132$.ServoConfigProto.PositionMo"
  "deConfig\022;\n\rvelocity_mode\030\006 \001(\0132$.ServoC"
  "onfigProto.VelocityModeConfig\022.\n\006homing\030"
  "\007 \001(\0132\036.ServoConfigProto.HomingConfig\0224\n"
  "\ninput_pins\030\010 \003(\0132 .ServoConfigProto.Inp"
  "utPinConfig\0228\n\014touch_probes\030\t \003(\0132\".Serv"
  "oConfigProto.TouchProbeConfig\0221\n\013tpdo_co"
  "nfig\030\n \003(\0132\034.ServoConfigProto.TPDOConfig"
  "\"\235\001\n\017HeartbeatConfig\022\022\n\ntimeout_ms\030\001 \001(\r"
  "\022\031\n\021check_interval_ms\030\002 \001(\r\022\032\n\022enable_au"
  "to_homing\030\003 \001(\010\022 \n\030enable_heartbeat_moni"
  "tor\030\004 \001(\010\022\035\n\025max_missed_heartbeats\030\005 \001(\r"
  "\"\213\001\n\022ServoConfigMessage\0228\n\014servo_motors\030"
  "\001 \003(\0132\".ServoConfigProto.ServoMotorConfi"
  "g\022;\n\020heartbeat_config\030\002 \001(\0132!.ServoConfi"
  "gProto.HeartbeatConfig*B\n\016ServoMotorType"
  "\022\013\n\007GENERIC\020\000\022\020\n\014COPLEY_FIRST\020\001\022\021\n\rCOPLE"
  "Y_SECOND\020\002b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_ServoConfigParameters_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_ServoConfigParameters_2eproto = {
  false, false, 1738, descriptor_table_protodef_ServoConfigParameters_2eproto, "ServoConfigParameters.proto", 
  &descriptor_table_ServoConfigParameters_2eproto_once, nullptr, 0, 10,
  schemas, file_default_instances, TableStruct_ServoConfigParameters_2eproto::offsets,
  file_level_metadata_ServoConfigParameters_2eproto, file_level_enum_descriptors_ServoConfigParameters_2eproto, file_level_service_descriptors_ServoConfigParameters_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_ServoConfigParameters_2eproto_getter() {
  return &descriptor_table_ServoConfigParameters_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_ServoConfigParameters_2eproto(&descriptor_table_ServoConfigParameters_2eproto);
namespace ServoConfigProto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ServoMotorType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_ServoConfigParameters_2eproto);
  return file_level_enum_descriptors_ServoConfigParameters_2eproto[0];
}
bool ServoMotorType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class PositionModeConfig::_Internal {
 public:
};

PositionModeConfig::PositionModeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.PositionModeConfig)
}
PositionModeConfig::PositionModeConfig(const PositionModeConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&profile_velocity_, &from.profile_velocity_,
    static_cast<size_t>(reinterpret_cast<char*>(&position_tracking_window_time_) -
    reinterpret_cast<char*>(&profile_velocity_)) + sizeof(position_tracking_window_time_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.PositionModeConfig)
}

inline void PositionModeConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&profile_velocity_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&position_tracking_window_time_) -
    reinterpret_cast<char*>(&profile_velocity_)) + sizeof(position_tracking_window_time_));
}

PositionModeConfig::~PositionModeConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.PositionModeConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PositionModeConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PositionModeConfig::ArenaDtor(void* object) {
  PositionModeConfig* _this = reinterpret_cast< PositionModeConfig* >(object);
  (void)_this;
}
void PositionModeConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PositionModeConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PositionModeConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.PositionModeConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&profile_velocity_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&position_tracking_window_time_) -
      reinterpret_cast<char*>(&profile_velocity_)) + sizeof(position_tracking_window_time_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PositionModeConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 profile_velocity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          profile_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_acceleration = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          profile_acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_deceleration = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          profile_deceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool use_limit_switches = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          use_limit_switches_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 positive_limit = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          positive_limit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 negative_limit = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          negative_limit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 position_tracking_window = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          position_tracking_window_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 position_tracking_window_time = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          position_tracking_window_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PositionModeConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.PositionModeConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 profile_velocity = 1;
  if (this->_internal_profile_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_profile_velocity(), target);
  }

  // int32 profile_acceleration = 2;
  if (this->_internal_profile_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_profile_acceleration(), target);
  }

  // int32 profile_deceleration = 3;
  if (this->_internal_profile_deceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_profile_deceleration(), target);
  }

  // bool use_limit_switches = 4;
  if (this->_internal_use_limit_switches() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_use_limit_switches(), target);
  }

  // int32 positive_limit = 5;
  if (this->_internal_positive_limit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_positive_limit(), target);
  }

  // int32 negative_limit = 6;
  if (this->_internal_negative_limit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_negative_limit(), target);
  }

  // int32 position_tracking_window = 7;
  if (this->_internal_position_tracking_window() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(7, this->_internal_position_tracking_window(), target);
  }

  // int32 position_tracking_window_time = 8;
  if (this->_internal_position_tracking_window_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(8, this->_internal_position_tracking_window_time(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.PositionModeConfig)
  return target;
}

size_t PositionModeConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.PositionModeConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 profile_velocity = 1;
  if (this->_internal_profile_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_velocity());
  }

  // int32 profile_acceleration = 2;
  if (this->_internal_profile_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_acceleration());
  }

  // int32 profile_deceleration = 3;
  if (this->_internal_profile_deceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_deceleration());
  }

  // bool use_limit_switches = 4;
  if (this->_internal_use_limit_switches() != 0) {
    total_size += 1 + 1;
  }

  // int32 positive_limit = 5;
  if (this->_internal_positive_limit() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_positive_limit());
  }

  // int32 negative_limit = 6;
  if (this->_internal_negative_limit() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_negative_limit());
  }

  // int32 position_tracking_window = 7;
  if (this->_internal_position_tracking_window() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_position_tracking_window());
  }

  // int32 position_tracking_window_time = 8;
  if (this->_internal_position_tracking_window_time() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_position_tracking_window_time());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PositionModeConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PositionModeConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PositionModeConfig::GetClassData() const { return &_class_data_; }

void PositionModeConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PositionModeConfig *>(to)->MergeFrom(
      static_cast<const PositionModeConfig &>(from));
}


void PositionModeConfig::MergeFrom(const PositionModeConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.PositionModeConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_profile_velocity() != 0) {
    _internal_set_profile_velocity(from._internal_profile_velocity());
  }
  if (from._internal_profile_acceleration() != 0) {
    _internal_set_profile_acceleration(from._internal_profile_acceleration());
  }
  if (from._internal_profile_deceleration() != 0) {
    _internal_set_profile_deceleration(from._internal_profile_deceleration());
  }
  if (from._internal_use_limit_switches() != 0) {
    _internal_set_use_limit_switches(from._internal_use_limit_switches());
  }
  if (from._internal_positive_limit() != 0) {
    _internal_set_positive_limit(from._internal_positive_limit());
  }
  if (from._internal_negative_limit() != 0) {
    _internal_set_negative_limit(from._internal_negative_limit());
  }
  if (from._internal_position_tracking_window() != 0) {
    _internal_set_position_tracking_window(from._internal_position_tracking_window());
  }
  if (from._internal_position_tracking_window_time() != 0) {
    _internal_set_position_tracking_window_time(from._internal_position_tracking_window_time());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PositionModeConfig::CopyFrom(const PositionModeConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.PositionModeConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PositionModeConfig::IsInitialized() const {
  return true;
}

void PositionModeConfig::InternalSwap(PositionModeConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PositionModeConfig, position_tracking_window_time_)
      + sizeof(PositionModeConfig::position_tracking_window_time_)
      - PROTOBUF_FIELD_OFFSET(PositionModeConfig, profile_velocity_)>(
          reinterpret_cast<char*>(&profile_velocity_),
          reinterpret_cast<char*>(&other->profile_velocity_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PositionModeConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[0]);
}

// ===================================================================

class VelocityModeConfig::_Internal {
 public:
};

VelocityModeConfig::VelocityModeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.VelocityModeConfig)
}
VelocityModeConfig::VelocityModeConfig(const VelocityModeConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&profile_acceleration_, &from.profile_acceleration_,
    static_cast<size_t>(reinterpret_cast<char*>(&min_velocity_) -
    reinterpret_cast<char*>(&profile_acceleration_)) + sizeof(min_velocity_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.VelocityModeConfig)
}

inline void VelocityModeConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&profile_acceleration_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&min_velocity_) -
    reinterpret_cast<char*>(&profile_acceleration_)) + sizeof(min_velocity_));
}

VelocityModeConfig::~VelocityModeConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.VelocityModeConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VelocityModeConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VelocityModeConfig::ArenaDtor(void* object) {
  VelocityModeConfig* _this = reinterpret_cast< VelocityModeConfig* >(object);
  (void)_this;
}
void VelocityModeConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VelocityModeConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VelocityModeConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.VelocityModeConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&profile_acceleration_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&min_velocity_) -
      reinterpret_cast<char*>(&profile_acceleration_)) + sizeof(min_velocity_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VelocityModeConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 profile_acceleration = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          profile_acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 profile_deceleration = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          profile_deceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 max_velocity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          max_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 min_velocity = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          min_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VelocityModeConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.VelocityModeConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 profile_acceleration = 1;
  if (this->_internal_profile_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_profile_acceleration(), target);
  }

  // int32 profile_deceleration = 2;
  if (this->_internal_profile_deceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_profile_deceleration(), target);
  }

  // int32 max_velocity = 3;
  if (this->_internal_max_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_max_velocity(), target);
  }

  // int32 min_velocity = 4;
  if (this->_internal_min_velocity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_min_velocity(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.VelocityModeConfig)
  return target;
}

size_t VelocityModeConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.VelocityModeConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 profile_acceleration = 1;
  if (this->_internal_profile_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_acceleration());
  }

  // int32 profile_deceleration = 2;
  if (this->_internal_profile_deceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_profile_deceleration());
  }

  // int32 max_velocity = 3;
  if (this->_internal_max_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_max_velocity());
  }

  // int32 min_velocity = 4;
  if (this->_internal_min_velocity() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_min_velocity());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VelocityModeConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VelocityModeConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VelocityModeConfig::GetClassData() const { return &_class_data_; }

void VelocityModeConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VelocityModeConfig *>(to)->MergeFrom(
      static_cast<const VelocityModeConfig &>(from));
}


void VelocityModeConfig::MergeFrom(const VelocityModeConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.VelocityModeConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_profile_acceleration() != 0) {
    _internal_set_profile_acceleration(from._internal_profile_acceleration());
  }
  if (from._internal_profile_deceleration() != 0) {
    _internal_set_profile_deceleration(from._internal_profile_deceleration());
  }
  if (from._internal_max_velocity() != 0) {
    _internal_set_max_velocity(from._internal_max_velocity());
  }
  if (from._internal_min_velocity() != 0) {
    _internal_set_min_velocity(from._internal_min_velocity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VelocityModeConfig::CopyFrom(const VelocityModeConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.VelocityModeConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VelocityModeConfig::IsInitialized() const {
  return true;
}

void VelocityModeConfig::InternalSwap(VelocityModeConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VelocityModeConfig, min_velocity_)
      + sizeof(VelocityModeConfig::min_velocity_)
      - PROTOBUF_FIELD_OFFSET(VelocityModeConfig, profile_acceleration_)>(
          reinterpret_cast<char*>(&profile_acceleration_),
          reinterpret_cast<char*>(&other->profile_acceleration_));
}

::PROTOBUF_NAMESPACE_ID::Metadata VelocityModeConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[1]);
}

// ===================================================================

class HomingConfig::_Internal {
 public:
};

HomingConfig::HomingConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.HomingConfig)
}
HomingConfig::HomingConfig(const HomingConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&method_, &from.method_,
    static_cast<size_t>(reinterpret_cast<char*>(&offset_) -
    reinterpret_cast<char*>(&method_)) + sizeof(offset_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.HomingConfig)
}

inline void HomingConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&method_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&offset_) -
    reinterpret_cast<char*>(&method_)) + sizeof(offset_));
}

HomingConfig::~HomingConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.HomingConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HomingConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HomingConfig::ArenaDtor(void* object) {
  HomingConfig* _this = reinterpret_cast< HomingConfig* >(object);
  (void)_this;
}
void HomingConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HomingConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HomingConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.HomingConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&method_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&offset_) -
      reinterpret_cast<char*>(&method_)) + sizeof(offset_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HomingConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 method = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          method_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 speed_switch = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          speed_switch_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 speed_zero = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          speed_zero_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 acceleration = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 offset = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          offset_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HomingConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.HomingConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 method = 1;
  if (this->_internal_method() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_method(), target);
  }

  // int32 speed_switch = 2;
  if (this->_internal_speed_switch() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_speed_switch(), target);
  }

  // int32 speed_zero = 3;
  if (this->_internal_speed_zero() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_speed_zero(), target);
  }

  // int32 acceleration = 4;
  if (this->_internal_acceleration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_acceleration(), target);
  }

  // int32 offset = 5;
  if (this->_internal_offset() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_offset(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.HomingConfig)
  return target;
}

size_t HomingConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.HomingConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 method = 1;
  if (this->_internal_method() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_method());
  }

  // int32 speed_switch = 2;
  if (this->_internal_speed_switch() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_speed_switch());
  }

  // int32 speed_zero = 3;
  if (this->_internal_speed_zero() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_speed_zero());
  }

  // int32 acceleration = 4;
  if (this->_internal_acceleration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_acceleration());
  }

  // int32 offset = 5;
  if (this->_internal_offset() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_offset());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HomingConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HomingConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HomingConfig::GetClassData() const { return &_class_data_; }

void HomingConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HomingConfig *>(to)->MergeFrom(
      static_cast<const HomingConfig &>(from));
}


void HomingConfig::MergeFrom(const HomingConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.HomingConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_method() != 0) {
    _internal_set_method(from._internal_method());
  }
  if (from._internal_speed_switch() != 0) {
    _internal_set_speed_switch(from._internal_speed_switch());
  }
  if (from._internal_speed_zero() != 0) {
    _internal_set_speed_zero(from._internal_speed_zero());
  }
  if (from._internal_acceleration() != 0) {
    _internal_set_acceleration(from._internal_acceleration());
  }
  if (from._internal_offset() != 0) {
    _internal_set_offset(from._internal_offset());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HomingConfig::CopyFrom(const HomingConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.HomingConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HomingConfig::IsInitialized() const {
  return true;
}

void HomingConfig::InternalSwap(HomingConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HomingConfig, offset_)
      + sizeof(HomingConfig::offset_)
      - PROTOBUF_FIELD_OFFSET(HomingConfig, method_)>(
          reinterpret_cast<char*>(&method_),
          reinterpret_cast<char*>(&other->method_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HomingConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[2]);
}

// ===================================================================

class InputPinConfig::_Internal {
 public:
};

InputPinConfig::InputPinConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.InputPinConfig)
}
InputPinConfig::InputPinConfig(const InputPinConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&pin_index_, &from.pin_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&debounce_values_) -
    reinterpret_cast<char*>(&pin_index_)) + sizeof(debounce_values_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.InputPinConfig)
}

inline void InputPinConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&pin_index_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&debounce_values_) -
    reinterpret_cast<char*>(&pin_index_)) + sizeof(debounce_values_));
}

InputPinConfig::~InputPinConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.InputPinConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void InputPinConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void InputPinConfig::ArenaDtor(void* object) {
  InputPinConfig* _this = reinterpret_cast< InputPinConfig* >(object);
  (void)_this;
}
void InputPinConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void InputPinConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void InputPinConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.InputPinConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&pin_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&debounce_values_) -
      reinterpret_cast<char*>(&pin_index_)) + sizeof(debounce_values_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* InputPinConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 pin_index = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          pin_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 configuration = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          configuration_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 debounce_values = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          debounce_values_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* InputPinConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.InputPinConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 pin_index = 1;
  if (this->_internal_pin_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_pin_index(), target);
  }

  // int32 configuration = 2;
  if (this->_internal_configuration() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_configuration(), target);
  }

  // uint32 debounce_values = 3;
  if (this->_internal_debounce_values() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_debounce_values(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.InputPinConfig)
  return target;
}

size_t InputPinConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.InputPinConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 pin_index = 1;
  if (this->_internal_pin_index() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_pin_index());
  }

  // int32 configuration = 2;
  if (this->_internal_configuration() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_configuration());
  }

  // uint32 debounce_values = 3;
  if (this->_internal_debounce_values() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_debounce_values());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData InputPinConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    InputPinConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*InputPinConfig::GetClassData() const { return &_class_data_; }

void InputPinConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<InputPinConfig *>(to)->MergeFrom(
      static_cast<const InputPinConfig &>(from));
}


void InputPinConfig::MergeFrom(const InputPinConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.InputPinConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_pin_index() != 0) {
    _internal_set_pin_index(from._internal_pin_index());
  }
  if (from._internal_configuration() != 0) {
    _internal_set_configuration(from._internal_configuration());
  }
  if (from._internal_debounce_values() != 0) {
    _internal_set_debounce_values(from._internal_debounce_values());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void InputPinConfig::CopyFrom(const InputPinConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.InputPinConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InputPinConfig::IsInitialized() const {
  return true;
}

void InputPinConfig::InternalSwap(InputPinConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(InputPinConfig, debounce_values_)
      + sizeof(InputPinConfig::debounce_values_)
      - PROTOBUF_FIELD_OFFSET(InputPinConfig, pin_index_)>(
          reinterpret_cast<char*>(&pin_index_),
          reinterpret_cast<char*>(&other->pin_index_));
}

::PROTOBUF_NAMESPACE_ID::Metadata InputPinConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[3]);
}

// ===================================================================

class TouchProbeConfig::_Internal {
 public:
};

TouchProbeConfig::TouchProbeConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.TouchProbeConfig)
}
TouchProbeConfig::TouchProbeConfig(const TouchProbeConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&probe_index_, &from.probe_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&debounce_values_) -
    reinterpret_cast<char*>(&probe_index_)) + sizeof(debounce_values_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.TouchProbeConfig)
}

inline void TouchProbeConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&probe_index_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&debounce_values_) -
    reinterpret_cast<char*>(&probe_index_)) + sizeof(debounce_values_));
}

TouchProbeConfig::~TouchProbeConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.TouchProbeConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TouchProbeConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TouchProbeConfig::ArenaDtor(void* object) {
  TouchProbeConfig* _this = reinterpret_cast< TouchProbeConfig* >(object);
  (void)_this;
}
void TouchProbeConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TouchProbeConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TouchProbeConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.TouchProbeConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&probe_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&debounce_values_) -
      reinterpret_cast<char*>(&probe_index_)) + sizeof(debounce_values_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TouchProbeConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 probe_index = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          probe_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 function = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          function_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 io_select = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          io_select_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 debounce_values = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          debounce_values_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TouchProbeConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.TouchProbeConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 probe_index = 1;
  if (this->_internal_probe_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_probe_index(), target);
  }

  // int32 function = 2;
  if (this->_internal_function() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_function(), target);
  }

  // int32 io_select = 3;
  if (this->_internal_io_select() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_io_select(), target);
  }

  // uint32 debounce_values = 4;
  if (this->_internal_debounce_values() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_debounce_values(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.TouchProbeConfig)
  return target;
}

size_t TouchProbeConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.TouchProbeConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 probe_index = 1;
  if (this->_internal_probe_index() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_probe_index());
  }

  // int32 function = 2;
  if (this->_internal_function() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_function());
  }

  // int32 io_select = 3;
  if (this->_internal_io_select() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_io_select());
  }

  // uint32 debounce_values = 4;
  if (this->_internal_debounce_values() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_debounce_values());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TouchProbeConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TouchProbeConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TouchProbeConfig::GetClassData() const { return &_class_data_; }

void TouchProbeConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TouchProbeConfig *>(to)->MergeFrom(
      static_cast<const TouchProbeConfig &>(from));
}


void TouchProbeConfig::MergeFrom(const TouchProbeConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.TouchProbeConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_probe_index() != 0) {
    _internal_set_probe_index(from._internal_probe_index());
  }
  if (from._internal_function() != 0) {
    _internal_set_function(from._internal_function());
  }
  if (from._internal_io_select() != 0) {
    _internal_set_io_select(from._internal_io_select());
  }
  if (from._internal_debounce_values() != 0) {
    _internal_set_debounce_values(from._internal_debounce_values());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TouchProbeConfig::CopyFrom(const TouchProbeConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.TouchProbeConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TouchProbeConfig::IsInitialized() const {
  return true;
}

void TouchProbeConfig::InternalSwap(TouchProbeConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TouchProbeConfig, debounce_values_)
      + sizeof(TouchProbeConfig::debounce_values_)
      - PROTOBUF_FIELD_OFFSET(TouchProbeConfig, probe_index_)>(
          reinterpret_cast<char*>(&probe_index_),
          reinterpret_cast<char*>(&other->probe_index_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TouchProbeConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[4]);
}

// ===================================================================

class PDOMappingEntry::_Internal {
 public:
};

PDOMappingEntry::PDOMappingEntry(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.PDOMappingEntry)
}
PDOMappingEntry::PDOMappingEntry(const PDOMappingEntry& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&object_index_, &from.object_index_,
    static_cast<size_t>(reinterpret_cast<char*>(&data_length_) -
    reinterpret_cast<char*>(&object_index_)) + sizeof(data_length_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.PDOMappingEntry)
}

inline void PDOMappingEntry::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&object_index_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&data_length_) -
    reinterpret_cast<char*>(&object_index_)) + sizeof(data_length_));
}

PDOMappingEntry::~PDOMappingEntry() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.PDOMappingEntry)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PDOMappingEntry::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PDOMappingEntry::ArenaDtor(void* object) {
  PDOMappingEntry* _this = reinterpret_cast< PDOMappingEntry* >(object);
  (void)_this;
}
void PDOMappingEntry::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PDOMappingEntry::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PDOMappingEntry::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.PDOMappingEntry)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&object_index_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&data_length_) -
      reinterpret_cast<char*>(&object_index_)) + sizeof(data_length_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PDOMappingEntry::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 object_index = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          object_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 sub_index = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          sub_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 data_length = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          data_length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PDOMappingEntry::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.PDOMappingEntry)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 object_index = 1;
  if (this->_internal_object_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_object_index(), target);
  }

  // uint32 sub_index = 2;
  if (this->_internal_sub_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_sub_index(), target);
  }

  // uint32 data_length = 3;
  if (this->_internal_data_length() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_data_length(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.PDOMappingEntry)
  return target;
}

size_t PDOMappingEntry::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.PDOMappingEntry)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 object_index = 1;
  if (this->_internal_object_index() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_object_index());
  }

  // uint32 sub_index = 2;
  if (this->_internal_sub_index() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_sub_index());
  }

  // uint32 data_length = 3;
  if (this->_internal_data_length() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_data_length());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PDOMappingEntry::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PDOMappingEntry::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PDOMappingEntry::GetClassData() const { return &_class_data_; }

void PDOMappingEntry::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PDOMappingEntry *>(to)->MergeFrom(
      static_cast<const PDOMappingEntry &>(from));
}


void PDOMappingEntry::MergeFrom(const PDOMappingEntry& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.PDOMappingEntry)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_object_index() != 0) {
    _internal_set_object_index(from._internal_object_index());
  }
  if (from._internal_sub_index() != 0) {
    _internal_set_sub_index(from._internal_sub_index());
  }
  if (from._internal_data_length() != 0) {
    _internal_set_data_length(from._internal_data_length());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PDOMappingEntry::CopyFrom(const PDOMappingEntry& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.PDOMappingEntry)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PDOMappingEntry::IsInitialized() const {
  return true;
}

void PDOMappingEntry::InternalSwap(PDOMappingEntry* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PDOMappingEntry, data_length_)
      + sizeof(PDOMappingEntry::data_length_)
      - PROTOBUF_FIELD_OFFSET(PDOMappingEntry, object_index_)>(
          reinterpret_cast<char*>(&object_index_),
          reinterpret_cast<char*>(&other->object_index_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PDOMappingEntry::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[5]);
}

// ===================================================================

class TPDOConfig::_Internal {
 public:
};

TPDOConfig::TPDOConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  mapping_entries_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.TPDOConfig)
}
TPDOConfig::TPDOConfig(const TPDOConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      mapping_entries_(from.mapping_entries_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&pdo_number_, &from.pdo_number_,
    static_cast<size_t>(reinterpret_cast<char*>(&transmission_type_) -
    reinterpret_cast<char*>(&pdo_number_)) + sizeof(transmission_type_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.TPDOConfig)
}

inline void TPDOConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&pdo_number_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&transmission_type_) -
    reinterpret_cast<char*>(&pdo_number_)) + sizeof(transmission_type_));
}

TPDOConfig::~TPDOConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.TPDOConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TPDOConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TPDOConfig::ArenaDtor(void* object) {
  TPDOConfig* _this = reinterpret_cast< TPDOConfig* >(object);
  (void)_this;
}
void TPDOConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TPDOConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TPDOConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.TPDOConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  mapping_entries_.Clear();
  ::memset(&pdo_number_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&transmission_type_) -
      reinterpret_cast<char*>(&pdo_number_)) + sizeof(transmission_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TPDOConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 pdo_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          pdo_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 transmission_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          transmission_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .ServoConfigProto.PDOMappingEntry mapping_entries = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_mapping_entries(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TPDOConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.TPDOConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 pdo_number = 1;
  if (this->_internal_pdo_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_pdo_number(), target);
  }

  // uint32 transmission_type = 2;
  if (this->_internal_transmission_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_transmission_type(), target);
  }

  // repeated .ServoConfigProto.PDOMappingEntry mapping_entries = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_mapping_entries_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_mapping_entries(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.TPDOConfig)
  return target;
}

size_t TPDOConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.TPDOConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ServoConfigProto.PDOMappingEntry mapping_entries = 3;
  total_size += 1UL * this->_internal_mapping_entries_size();
  for (const auto& msg : this->mapping_entries_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 pdo_number = 1;
  if (this->_internal_pdo_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_pdo_number());
  }

  // uint32 transmission_type = 2;
  if (this->_internal_transmission_type() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_transmission_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TPDOConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TPDOConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TPDOConfig::GetClassData() const { return &_class_data_; }

void TPDOConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TPDOConfig *>(to)->MergeFrom(
      static_cast<const TPDOConfig &>(from));
}


void TPDOConfig::MergeFrom(const TPDOConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.TPDOConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  mapping_entries_.MergeFrom(from.mapping_entries_);
  if (from._internal_pdo_number() != 0) {
    _internal_set_pdo_number(from._internal_pdo_number());
  }
  if (from._internal_transmission_type() != 0) {
    _internal_set_transmission_type(from._internal_transmission_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TPDOConfig::CopyFrom(const TPDOConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.TPDOConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TPDOConfig::IsInitialized() const {
  return true;
}

void TPDOConfig::InternalSwap(TPDOConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  mapping_entries_.InternalSwap(&other->mapping_entries_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TPDOConfig, transmission_type_)
      + sizeof(TPDOConfig::transmission_type_)
      - PROTOBUF_FIELD_OFFSET(TPDOConfig, pdo_number_)>(
          reinterpret_cast<char*>(&pdo_number_),
          reinterpret_cast<char*>(&other->pdo_number_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TPDOConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[6]);
}

// ===================================================================

class ServoMotorConfig::_Internal {
 public:
  static const ::ServoConfigProto::PositionModeConfig& position_mode(const ServoMotorConfig* msg);
  static const ::ServoConfigProto::VelocityModeConfig& velocity_mode(const ServoMotorConfig* msg);
  static const ::ServoConfigProto::HomingConfig& homing(const ServoMotorConfig* msg);
};

const ::ServoConfigProto::PositionModeConfig&
ServoMotorConfig::_Internal::position_mode(const ServoMotorConfig* msg) {
  return *msg->position_mode_;
}
const ::ServoConfigProto::VelocityModeConfig&
ServoMotorConfig::_Internal::velocity_mode(const ServoMotorConfig* msg) {
  return *msg->velocity_mode_;
}
const ::ServoConfigProto::HomingConfig&
ServoMotorConfig::_Internal::homing(const ServoMotorConfig* msg) {
  return *msg->homing_;
}
ServoMotorConfig::ServoMotorConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  input_pins_(arena),
  touch_probes_(arena),
  tpdo_config_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.ServoMotorConfig)
}
ServoMotorConfig::ServoMotorConfig(const ServoMotorConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      input_pins_(from.input_pins_),
      touch_probes_(from.touch_probes_),
      tpdo_config_(from.tpdo_config_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_position_mode()) {
    position_mode_ = new ::ServoConfigProto::PositionModeConfig(*from.position_mode_);
  } else {
    position_mode_ = nullptr;
  }
  if (from._internal_has_velocity_mode()) {
    velocity_mode_ = new ::ServoConfigProto::VelocityModeConfig(*from.velocity_mode_);
  } else {
    velocity_mode_ = nullptr;
  }
  if (from._internal_has_homing()) {
    homing_ = new ::ServoConfigProto::HomingConfig(*from.homing_);
  } else {
    homing_ = nullptr;
  }
  ::memcpy(&node_id_, &from.node_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&encoder_resolution_) -
    reinterpret_cast<char*>(&node_id_)) + sizeof(encoder_resolution_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.ServoMotorConfig)
}

inline void ServoMotorConfig::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&position_mode_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&encoder_resolution_) -
    reinterpret_cast<char*>(&position_mode_)) + sizeof(encoder_resolution_));
}

ServoMotorConfig::~ServoMotorConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.ServoMotorConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ServoMotorConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete position_mode_;
  if (this != internal_default_instance()) delete velocity_mode_;
  if (this != internal_default_instance()) delete homing_;
}

void ServoMotorConfig::ArenaDtor(void* object) {
  ServoMotorConfig* _this = reinterpret_cast< ServoMotorConfig* >(object);
  (void)_this;
}
void ServoMotorConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ServoMotorConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ServoMotorConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.ServoMotorConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_pins_.Clear();
  touch_probes_.Clear();
  tpdo_config_.Clear();
  name_.ClearToEmpty();
  type_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && position_mode_ != nullptr) {
    delete position_mode_;
  }
  position_mode_ = nullptr;
  if (GetArenaForAllocation() == nullptr && velocity_mode_ != nullptr) {
    delete velocity_mode_;
  }
  velocity_mode_ = nullptr;
  if (GetArenaForAllocation() == nullptr && homing_ != nullptr) {
    delete homing_;
  }
  homing_ = nullptr;
  ::memset(&node_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&encoder_resolution_) -
      reinterpret_cast<char*>(&node_id_)) + sizeof(encoder_resolution_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ServoMotorConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 node_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          node_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ServoConfigProto.ServoMotorConfig.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ServoConfigProto.ServoMotorConfig.type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 encoder_resolution = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          encoder_resolution_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .ServoConfigProto.PositionModeConfig position_mode = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_position_mode(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .ServoConfigProto.VelocityModeConfig velocity_mode = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_velocity_mode(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .ServoConfigProto.HomingConfig homing = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_homing(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .ServoConfigProto.InputPinConfig input_pins = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_input_pins(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .ServoConfigProto.TouchProbeConfig touch_probes = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_touch_probes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .ServoConfigProto.TPDOConfig tpdo_config = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_tpdo_config(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ServoMotorConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.ServoMotorConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_node_id(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ServoConfigProto.ServoMotorConfig.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string type = 3;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ServoConfigProto.ServoMotorConfig.type");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_type(), target);
  }

  // int32 encoder_resolution = 4;
  if (this->_internal_encoder_resolution() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_encoder_resolution(), target);
  }

  // .ServoConfigProto.PositionModeConfig position_mode = 5;
  if (this->_internal_has_position_mode()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::position_mode(this), target, stream);
  }

  // .ServoConfigProto.VelocityModeConfig velocity_mode = 6;
  if (this->_internal_has_velocity_mode()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::velocity_mode(this), target, stream);
  }

  // .ServoConfigProto.HomingConfig homing = 7;
  if (this->_internal_has_homing()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::homing(this), target, stream);
  }

  // repeated .ServoConfigProto.InputPinConfig input_pins = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_input_pins_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_input_pins(i), target, stream);
  }

  // repeated .ServoConfigProto.TouchProbeConfig touch_probes = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_touch_probes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, this->_internal_touch_probes(i), target, stream);
  }

  // repeated .ServoConfigProto.TPDOConfig tpdo_config = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_tpdo_config_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, this->_internal_tpdo_config(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.ServoMotorConfig)
  return target;
}

size_t ServoMotorConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.ServoMotorConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ServoConfigProto.InputPinConfig input_pins = 8;
  total_size += 1UL * this->_internal_input_pins_size();
  for (const auto& msg : this->input_pins_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .ServoConfigProto.TouchProbeConfig touch_probes = 9;
  total_size += 1UL * this->_internal_touch_probes_size();
  for (const auto& msg : this->touch_probes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .ServoConfigProto.TPDOConfig tpdo_config = 10;
  total_size += 1UL * this->_internal_tpdo_config_size();
  for (const auto& msg : this->tpdo_config_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string type = 3;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  // .ServoConfigProto.PositionModeConfig position_mode = 5;
  if (this->_internal_has_position_mode()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *position_mode_);
  }

  // .ServoConfigProto.VelocityModeConfig velocity_mode = 6;
  if (this->_internal_has_velocity_mode()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *velocity_mode_);
  }

  // .ServoConfigProto.HomingConfig homing = 7;
  if (this->_internal_has_homing()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *homing_);
  }

  // uint32 node_id = 1;
  if (this->_internal_node_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_node_id());
  }

  // int32 encoder_resolution = 4;
  if (this->_internal_encoder_resolution() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_encoder_resolution());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ServoMotorConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ServoMotorConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ServoMotorConfig::GetClassData() const { return &_class_data_; }

void ServoMotorConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ServoMotorConfig *>(to)->MergeFrom(
      static_cast<const ServoMotorConfig &>(from));
}


void ServoMotorConfig::MergeFrom(const ServoMotorConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.ServoMotorConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  input_pins_.MergeFrom(from.input_pins_);
  touch_probes_.MergeFrom(from.touch_probes_);
  tpdo_config_.MergeFrom(from.tpdo_config_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_has_position_mode()) {
    _internal_mutable_position_mode()->::ServoConfigProto::PositionModeConfig::MergeFrom(from._internal_position_mode());
  }
  if (from._internal_has_velocity_mode()) {
    _internal_mutable_velocity_mode()->::ServoConfigProto::VelocityModeConfig::MergeFrom(from._internal_velocity_mode());
  }
  if (from._internal_has_homing()) {
    _internal_mutable_homing()->::ServoConfigProto::HomingConfig::MergeFrom(from._internal_homing());
  }
  if (from._internal_node_id() != 0) {
    _internal_set_node_id(from._internal_node_id());
  }
  if (from._internal_encoder_resolution() != 0) {
    _internal_set_encoder_resolution(from._internal_encoder_resolution());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ServoMotorConfig::CopyFrom(const ServoMotorConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.ServoMotorConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServoMotorConfig::IsInitialized() const {
  return true;
}

void ServoMotorConfig::InternalSwap(ServoMotorConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  input_pins_.InternalSwap(&other->input_pins_);
  touch_probes_.InternalSwap(&other->touch_probes_);
  tpdo_config_.InternalSwap(&other->tpdo_config_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ServoMotorConfig, encoder_resolution_)
      + sizeof(ServoMotorConfig::encoder_resolution_)
      - PROTOBUF_FIELD_OFFSET(ServoMotorConfig, position_mode_)>(
          reinterpret_cast<char*>(&position_mode_),
          reinterpret_cast<char*>(&other->position_mode_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ServoMotorConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[7]);
}

// ===================================================================

class HeartbeatConfig::_Internal {
 public:
};

HeartbeatConfig::HeartbeatConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.HeartbeatConfig)
}
HeartbeatConfig::HeartbeatConfig(const HeartbeatConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timeout_ms_, &from.timeout_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&max_missed_heartbeats_) -
    reinterpret_cast<char*>(&timeout_ms_)) + sizeof(max_missed_heartbeats_));
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.HeartbeatConfig)
}

inline void HeartbeatConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timeout_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&max_missed_heartbeats_) -
    reinterpret_cast<char*>(&timeout_ms_)) + sizeof(max_missed_heartbeats_));
}

HeartbeatConfig::~HeartbeatConfig() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.HeartbeatConfig)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HeartbeatConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void HeartbeatConfig::ArenaDtor(void* object) {
  HeartbeatConfig* _this = reinterpret_cast< HeartbeatConfig* >(object);
  (void)_this;
}
void HeartbeatConfig::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HeartbeatConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HeartbeatConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.HeartbeatConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timeout_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_missed_heartbeats_) -
      reinterpret_cast<char*>(&timeout_ms_)) + sizeof(max_missed_heartbeats_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HeartbeatConfig::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 timeout_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timeout_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 check_interval_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          check_interval_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool enable_auto_homing = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          enable_auto_homing_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool enable_heartbeat_monitor = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          enable_heartbeat_monitor_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 max_missed_heartbeats = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          max_missed_heartbeats_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HeartbeatConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.HeartbeatConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 timeout_ms = 1;
  if (this->_internal_timeout_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_timeout_ms(), target);
  }

  // uint32 check_interval_ms = 2;
  if (this->_internal_check_interval_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_check_interval_ms(), target);
  }

  // bool enable_auto_homing = 3;
  if (this->_internal_enable_auto_homing() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_enable_auto_homing(), target);
  }

  // bool enable_heartbeat_monitor = 4;
  if (this->_internal_enable_heartbeat_monitor() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_enable_heartbeat_monitor(), target);
  }

  // uint32 max_missed_heartbeats = 5;
  if (this->_internal_max_missed_heartbeats() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_max_missed_heartbeats(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.HeartbeatConfig)
  return target;
}

size_t HeartbeatConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.HeartbeatConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 timeout_ms = 1;
  if (this->_internal_timeout_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_timeout_ms());
  }

  // uint32 check_interval_ms = 2;
  if (this->_internal_check_interval_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_check_interval_ms());
  }

  // bool enable_auto_homing = 3;
  if (this->_internal_enable_auto_homing() != 0) {
    total_size += 1 + 1;
  }

  // bool enable_heartbeat_monitor = 4;
  if (this->_internal_enable_heartbeat_monitor() != 0) {
    total_size += 1 + 1;
  }

  // uint32 max_missed_heartbeats = 5;
  if (this->_internal_max_missed_heartbeats() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_max_missed_heartbeats());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HeartbeatConfig::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HeartbeatConfig::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HeartbeatConfig::GetClassData() const { return &_class_data_; }

void HeartbeatConfig::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HeartbeatConfig *>(to)->MergeFrom(
      static_cast<const HeartbeatConfig &>(from));
}


void HeartbeatConfig::MergeFrom(const HeartbeatConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.HeartbeatConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timeout_ms() != 0) {
    _internal_set_timeout_ms(from._internal_timeout_ms());
  }
  if (from._internal_check_interval_ms() != 0) {
    _internal_set_check_interval_ms(from._internal_check_interval_ms());
  }
  if (from._internal_enable_auto_homing() != 0) {
    _internal_set_enable_auto_homing(from._internal_enable_auto_homing());
  }
  if (from._internal_enable_heartbeat_monitor() != 0) {
    _internal_set_enable_heartbeat_monitor(from._internal_enable_heartbeat_monitor());
  }
  if (from._internal_max_missed_heartbeats() != 0) {
    _internal_set_max_missed_heartbeats(from._internal_max_missed_heartbeats());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HeartbeatConfig::CopyFrom(const HeartbeatConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.HeartbeatConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HeartbeatConfig::IsInitialized() const {
  return true;
}

void HeartbeatConfig::InternalSwap(HeartbeatConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HeartbeatConfig, max_missed_heartbeats_)
      + sizeof(HeartbeatConfig::max_missed_heartbeats_)
      - PROTOBUF_FIELD_OFFSET(HeartbeatConfig, timeout_ms_)>(
          reinterpret_cast<char*>(&timeout_ms_),
          reinterpret_cast<char*>(&other->timeout_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HeartbeatConfig::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[8]);
}

// ===================================================================

class ServoConfigMessage::_Internal {
 public:
  static const ::ServoConfigProto::HeartbeatConfig& heartbeat_config(const ServoConfigMessage* msg);
};

const ::ServoConfigProto::HeartbeatConfig&
ServoConfigMessage::_Internal::heartbeat_config(const ServoConfigMessage* msg) {
  return *msg->heartbeat_config_;
}
ServoConfigMessage::ServoConfigMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  servo_motors_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ServoConfigProto.ServoConfigMessage)
}
ServoConfigMessage::ServoConfigMessage(const ServoConfigMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      servo_motors_(from.servo_motors_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_heartbeat_config()) {
    heartbeat_config_ = new ::ServoConfigProto::HeartbeatConfig(*from.heartbeat_config_);
  } else {
    heartbeat_config_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:ServoConfigProto.ServoConfigMessage)
}

inline void ServoConfigMessage::SharedCtor() {
heartbeat_config_ = nullptr;
}

ServoConfigMessage::~ServoConfigMessage() {
  // @@protoc_insertion_point(destructor:ServoConfigProto.ServoConfigMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ServoConfigMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete heartbeat_config_;
}

void ServoConfigMessage::ArenaDtor(void* object) {
  ServoConfigMessage* _this = reinterpret_cast< ServoConfigMessage* >(object);
  (void)_this;
}
void ServoConfigMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ServoConfigMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ServoConfigMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:ServoConfigProto.ServoConfigMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  servo_motors_.Clear();
  if (GetArenaForAllocation() == nullptr && heartbeat_config_ != nullptr) {
    delete heartbeat_config_;
  }
  heartbeat_config_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ServoConfigMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .ServoConfigProto.ServoMotorConfig servo_motors = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_servo_motors(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .ServoConfigProto.HeartbeatConfig heartbeat_config = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_heartbeat_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ServoConfigMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ServoConfigProto.ServoConfigMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .ServoConfigProto.ServoMotorConfig servo_motors = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_servo_motors_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_servo_motors(i), target, stream);
  }

  // .ServoConfigProto.HeartbeatConfig heartbeat_config = 2;
  if (this->_internal_has_heartbeat_config()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::heartbeat_config(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ServoConfigProto.ServoConfigMessage)
  return target;
}

size_t ServoConfigMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ServoConfigProto.ServoConfigMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .ServoConfigProto.ServoMotorConfig servo_motors = 1;
  total_size += 1UL * this->_internal_servo_motors_size();
  for (const auto& msg : this->servo_motors_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .ServoConfigProto.HeartbeatConfig heartbeat_config = 2;
  if (this->_internal_has_heartbeat_config()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *heartbeat_config_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ServoConfigMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ServoConfigMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ServoConfigMessage::GetClassData() const { return &_class_data_; }

void ServoConfigMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ServoConfigMessage *>(to)->MergeFrom(
      static_cast<const ServoConfigMessage &>(from));
}


void ServoConfigMessage::MergeFrom(const ServoConfigMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ServoConfigProto.ServoConfigMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  servo_motors_.MergeFrom(from.servo_motors_);
  if (from._internal_has_heartbeat_config()) {
    _internal_mutable_heartbeat_config()->::ServoConfigProto::HeartbeatConfig::MergeFrom(from._internal_heartbeat_config());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ServoConfigMessage::CopyFrom(const ServoConfigMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ServoConfigProto.ServoConfigMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServoConfigMessage::IsInitialized() const {
  return true;
}

void ServoConfigMessage::InternalSwap(ServoConfigMessage* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  servo_motors_.InternalSwap(&other->servo_motors_);
  swap(heartbeat_config_, other->heartbeat_config_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ServoConfigMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_ServoConfigParameters_2eproto_getter, &descriptor_table_ServoConfigParameters_2eproto_once,
      file_level_metadata_ServoConfigParameters_2eproto[9]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace ServoConfigProto
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::ServoConfigProto::PositionModeConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::PositionModeConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::PositionModeConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::VelocityModeConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::VelocityModeConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::VelocityModeConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::HomingConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::HomingConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::HomingConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::InputPinConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::InputPinConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::InputPinConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::TouchProbeConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::TouchProbeConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::TouchProbeConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::PDOMappingEntry* Arena::CreateMaybeMessage< ::ServoConfigProto::PDOMappingEntry >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::PDOMappingEntry >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::TPDOConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::TPDOConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::TPDOConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::ServoMotorConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::ServoMotorConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::ServoMotorConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::HeartbeatConfig* Arena::CreateMaybeMessage< ::ServoConfigProto::HeartbeatConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::HeartbeatConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::ServoConfigProto::ServoConfigMessage* Arena::CreateMaybeMessage< ::ServoConfigProto::ServoConfigMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ServoConfigProto::ServoConfigMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
