#ifndef MOCK_SERVO_CONTROL_MANAGER_H
#define MOCK_SERVO_CONTROL_MANAGER_H

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <thread>
#include <chrono>
#include <map>
#include <iostream>
#include <fstream>
#include <sstream>
#include "ServoControlManager.hpp"
#include "MockCANopenMaster.h"
#include "ServoConfigParameters.pb.h"
#include <google/protobuf/util/json_util.h>

// Mock class for ServoControlManager to better control test scenarios
class MockServoControlManager : public ServoControlManager
{
public:
    explicit MockServoControlManager(MockCANopenMaster *mock_master)
        : ServoControlManager(mock_master), mock_master_(mock_master)
    {
        // Initialize mock state
        resetMockState();

        // Setup default behavior
        setupDefaultBehavior();
    }

    // Use gmock to define methods that need to be mocked
    MOCK_METHOD(bool, moveToPosition, (uint8_t nodeId, int32_t position, int32_t velocity, bool absolute, bool immediate), (override));
    MOCK_METHOD(bool, moveWithVelocity, (uint8_t nodeId, int32_t velocity, int32_t position), (override));
    MOCK_METHOD(bool, moveWithHoming, (uint8_t nodeId), (override));
    MOCK_METHOD(bool, emergencyStop, (uint8_t nodeId), (override));
    MOCK_METHOD(bool, homingBed, (BedType bedType), (override));
    MOCK_METHOD(BedStatus, getBedStatus, (BedType bedType), (override));
    MOCK_METHOD(PostIdInfo, getPostId, (BedType bedType), (override));
    MOCK_METHOD(bool, waitForTriggerInfoChange, (TriggerInfo & lastKnownInfo, int timeout_ms), (override));

    // Heartbeat detection related mock methods - 只保留基本的心跳功能
    // MOCK_METHOD(bool, startHeartbeatMonitor, (), (override));
    // MOCK_METHOD(void, stopHeartbeatMonitor, (), (override));

    MOCK_METHOD(bool, startManager, (const std::string &config_file), (override));

    // Setup default behavior
    void setupDefaultBehavior()
    {
        using ::testing::_;
        using ::testing::Invoke;
        using ::testing::Return;

        // moveToPosition default behavior - maintain original infinite loop logic
        ON_CALL(*this, moveToPosition(_, _, _, _, _)).WillByDefault(Invoke([this](uint8_t nodeId, int32_t position, int32_t velocity, bool absolute, bool immediate)
                                                                        {
            last_move_node_id = nodeId;
            last_target_position = (float)position;
            last_target_velocity = 0;

            // Simulate successful move operation
            mock_bed_motion_status[nodeId] = BedMotionStatus::Moving;

            while (true)
            {
                if (getStopFlag()) {
                    std::cout << "StartMove received stop signal!" << std::endl;
                    return true;
                }
                std::cout << "StartMove等待停止标志" << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
            }

            // Update mock bed position
            updateMockBedPosition(static_cast<BedType>(nodeId), (float)position);

            return true; }));

        // moveWithVelocity default behavior
        ON_CALL(*this, moveWithVelocity(_, _, _)).WillByDefault(Invoke([this](uint8_t nodeId, int32_t velocity, int32_t position)
                                                                    {
            last_move_node_id = nodeId;
            last_velocity_value = velocity;

            // Simulate successful velocity control
            BedType bedType = nodeId == 1 ? BedType::Primary : BedType::Secondary;
            mock_bed_motion_status[static_cast<int>(bedType)] =
                velocity == 0 ? BedMotionStatus::Ready : BedMotionStatus::Moving;

            return true; }));

        // moveWithHoming default behavior
        ON_CALL(*this, moveWithHoming(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                                    {
                                                                        BedType bedType = nodeId == 1 ? BedType::Primary : BedType::Secondary;
                                                                        updateMockBedPosition(bedType, 0.0f);
                                                                        return true;
                                                                         }));

        ON_CALL(*this, emergencyStop(_)).WillByDefault(Invoke([this](uint8_t nodeId)
                                                                    {
                                                                        return true;
                                                                         }));

        // homingBed default behavior
        ON_CALL(*this, homingBed(_)).WillByDefault(Invoke([this](BedType bedType)
                                                          {
            last_homing_bed_type = bedType;

            // Simulate homing operation
            mock_bed_motion_status[static_cast<int>(bedType)] = BedMotionStatus::Moving;

            // After homing completion, set position to 0
            updateMockBedPosition(bedType, 0.0f);

            // Status becomes Ready after completion
            mock_bed_motion_status[static_cast<int>(bedType)] = BedMotionStatus::Ready;
            return true; }));

        // getBedStatus default behavior
        ON_CALL(*this, getBedStatus(_)).WillByDefault(Invoke([this](BedType bedType)
                                                             {
            BedStatus status{};
            int bedTypeIndex = static_cast<int>(bedType);

            status.status = mock_bed_motion_status[bedTypeIndex];
            status.motionInfo.position = mock_bed_positions[bedTypeIndex] / 1000;
            status.motionInfo.velocity = mock_bed_velocities[bedTypeIndex];

            // Set position and velocity limits
            if (bedType == BedType::Primary)
            {
                status.positionMin = -1000.0f;
                status.positionMax = 1000.0f;
                status.velocityMin = -500.0f;
                status.velocityMax = 500.0f;
            }
            else
            {
                status.positionMin = -800.0f;
                status.positionMax = 800.0f;
                status.velocityMin = -400.0f;
                status.velocityMax = 400.0f;
            }

            status.accelerationMax = 100.0f;
            status.decelerationMax = 100.0f;

            return status; }));

        // getPostId default behavior
        ON_CALL(*this, getPostId(_)).WillByDefault(Invoke([this](BedType bedType)
                                                          {
            PostIdInfo postId{};
            postId.vid = 0x12345678;
            postId.did = 0x87654321;
            postId.hwid = 0xABCDEF00;
            postId.rid = static_cast<uint32_t>(bedType);
            return postId; }));

        ON_CALL(*this, startManager(_)).WillByDefault(Invoke([this](const std::string &configFile) {
            // 1. Read configuration file
            std::ifstream file(configFile);
            if (!file.is_open()) {
                std::cerr << "无法打开配置文件: " << configFile << std::endl;
                return false;
            }

            // Read file content to string
            std::stringstream buffer;
            buffer << file.rdbuf();
            std::string json_str = buffer.str();
            file.close();

            // 2. Parse configuration file using protobuf JSON tools
            ServoConfigProto::ServoConfigMessage servoConfig;
            google::protobuf::util::JsonParseOptions options;
            options.ignore_unknown_fields = true;

            auto status = google::protobuf::util::JsonStringToMessage(json_str, &servoConfig, options);
            if (!status.ok()) {
                std::cerr << "解析配置文件失败: " << status.ToString() << std::endl;
                return false;
            }
            if (!loadHeartbeatConfig(servoConfig)) {
                std::cerr << "加载心跳配置失败" << std::endl;
                return false;
            }

            // 6. Start heartbeat monitoring
            if (!startHeartbeatMonitor()) {
                std::cerr << "启动心跳监控失败" << std::endl;
                return false;
            }
            return true;
        }));
    }

    // Reset mock state
    void resetMockState()
    {
        mock_bed_positions[0] = 0.0f; // Primary
        mock_bed_positions[1] = 0.0f; // Secondary
        mock_bed_velocities[0] = 0.0f;
        mock_bed_velocities[1] = 0.0f;
        mock_bed_motion_status[0] = BedMotionStatus::Ready;
        mock_bed_motion_status[1] = BedMotionStatus::Ready;

        // Reset last operation parameters
        last_move_bed_type = BedType::Primary;
        last_target_position = 0.0f;
        last_target_velocity = 0.0f;
        last_move_node_id = 0;
        last_velocity_value = 0;
        last_homing_bed_type = BedType::Primary;
        last_stop_bed_type = BedType::Primary;
        // Reset heartbeat related state
        mock_heartbeat_timestamps_.clear();
    }

    // Accessor methods for test verification
    void setMockBedPosition(BedType bedType, float position)
    {
        mock_bed_positions[(int)bedType] = position;
    }

private:
    MockCANopenMaster *mock_master_;

    // Mock state
    float mock_bed_positions[2]{}; // 0: Primary, 1: Secondary
    float mock_bed_velocities[2]{};
    BedMotionStatus mock_bed_motion_status[2]{};

    // Record last operation parameters for test verification
    BedType last_move_bed_type;
    float last_target_position{};
    float last_target_velocity{};
    uint8_t last_move_node_id{};
    int32_t last_velocity_value{};
    BedType last_homing_bed_type;
    BedType last_stop_bed_type;
    // Heartbeat related mock state
    mutable std::map<BedOwnerType, std::chrono::steady_clock::time_point> mock_heartbeat_timestamps_;

    // Update mock bed position
    void updateMockBedPosition(BedType bedType, float position)
    {
        if (bedType == BedType::Both)
        {
            mock_bed_positions[0] = position;
            mock_bed_positions[1] = position;
        }
        else
        {
            mock_bed_positions[static_cast<int>(bedType)] = position;
        }
    }
};

#endif // MOCK_SERVO_CONTROL_MANAGER_H
