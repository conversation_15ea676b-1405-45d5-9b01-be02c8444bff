#include "CANopenMaster.hpp"
#include <iostream>
#include <chrono>
#include <cstring>
#include <unistd.h>
#include <net/if.h>
#include <linux/reboot.h>
#include <sys/reboot.h>
#include <syslog.h>
#include <cstdarg>
#include <iomanip>
#include <utility>

#include "CANopen.h"
#include "OD.h"
#include "CO_error.h"
#include "CO_epoll_interface.h"
#include "CO_storageLinux.h"
#include "SyslogManager.hpp"

/* 主线程和实时线程的间隔(微秒) */
#ifndef MAIN_THREAD_INTERVAL_US
#define MAIN_THREAD_INTERVAL_US 100000
#endif
#ifndef TMR_THREAD_INTERVAL_US
#define TMR_THREAD_INTERVAL_US 1000
#endif

// 主线程存储数据结构，用于LSS配置
typedef struct {
    uint16_t pendingBitRate;  // 待定的CAN比特率
    uint8_t pendingNodeId;    // 待定的CANopen节点ID
} mainlineStorage_t;

// 全局存储对象

static mainlineStorage_t mlStorage = {0};

/* CO_CANopenInit 的默认值 */
#ifndef NMT_CONTROL
#define NMT_CONTROL \
    CO_NMT_STARTUP_TO_OPERATIONAL | CO_NMT_ERR_ON_ERR_REG | \
    CO_ERR_REG_GENERIC_ERR | CO_ERR_REG_COMMUNICATION
#endif
#ifndef FIRST_HB_TIME
#define FIRST_HB_TIME 500
#endif
#ifndef SDO_SRV_TIMEOUT_TIME
#define SDO_SRV_TIMEOUT_TIME 1000
#endif
#ifndef SDO_CLI_TIMEOUT_TIME
#define SDO_CLI_TIMEOUT_TIME 500
#endif
#ifndef SDO_CLI_BLOCK
#define SDO_CLI_BLOCK false
#endif
#ifndef OD_STATUS_BITS
#define OD_STATUS_BITS NULL
#endif
/* CANopen网关功能 */
#ifndef GATEWAY_ENABLE
#define GATEWAY_ENABLE false
#endif
/* 时间戳消息间隔(毫秒) */
#ifndef TIME_STAMP_INTERVAL_MS
#define TIME_STAMP_INTERVAL_MS 10000
#endif

/* callback for emergency messages */
static void
EmergencyRxCallback(const uint16_t ident, const uint16_t errorCode, const uint8_t errorRegister, const uint8_t errorBit,
                    const uint32_t infoCode) {
    // int16_t nodeIdRx = ident ? (ident & 0x7F) : CO_activeNodeId;

    // log_printf(LOG_NOTICE, DBG_EMERGENCY_RX, nodeIdRx, errorCode, errorRegister, errorBit, infoCode);
}

/* return string description of NMT state. */
static char *
NmtState2Str(CO_NMT_internalState_t state) {
    switch (state) {
        case CO_NMT_INITIALIZING:
            return "initializing";
        case CO_NMT_PRE_OPERATIONAL:
            return "pre-operational";
        case CO_NMT_OPERATIONAL:
            return "operational";
        case CO_NMT_STOPPED:
            return "stopped";
        default:
            return "unknown";
    }
}

/* callback for NMT change messages */
static void
NmtChangedCallback(CO_NMT_internalState_t state) {
    // log_printf(LOG_NOTICE, DBG_NMT_CHANGE, NmtState2Str(state), state);
}

/* callback for monitoring Heartbeat remote NMT state change */
static void
HeartbeatNmtChangedCallback(uint8_t nodeId, uint8_t idx, CO_NMT_internalState_t state, void *object) {
    // (void) object;
    // log_printf(LOG_NOTICE, DBG_HB_CONS_NMT_CHANGE, nodeId, idx, NmtState2Str(state), state);
}

// LSS配置回调函数（需要作为静态成员函数）
static bool_t LSScfgStoreCallback(void *object, uint8_t id, uint16_t bitRate) {
    if (object == nullptr) return false;

    mainlineStorage_t *mainlineStorage = (mainlineStorage_t *) object;
    mainlineStorage->pendingNodeId = id;
    mainlineStorage->pendingBitRate = bitRate;

    std::cout << "LSS配置: NodeID=" << static_cast<int>(id) << ", BitRate=" << bitRate << std::endl;
    return true;
}

void
log_printf(int priority, const char *format, ...) {
    va_list ap;

    va_start(ap, format);
    vsyslog(priority, format, ap);
    va_end(ap);
}


/**********************************************************Class CANopenMaster*****************************************************************************/

// CANopen主站对象
static CANopenMaster *masterInstance = nullptr;

// 错误处理宏
#define CHECK_SDO_RESULT(result, message) \
    if ((result) != CO_SDO_AB_NONE) { \
        std::cerr << (message) << " - Error code: 0x" << std::hex << (result) << std::dec << std::endl; \
        handleError(result); \
        return false; \
    }

CANopenMaster::CANopenMaster(const std::string &canInterface)
        : canInterface(canInterface), CO(nullptr), running(false) {
    masterInstance = this;
}

CANopenMaster::~CANopenMaster() {
    stop();
}

bool CANopenMaster::init() {
    // 配置CAN接口
    if (canInterface.empty()) {
        std::cerr << "错误: CAN接口未指定" << std::endl;
        return false;
    }

    CANptr.can_ifindex = (int) if_nametoindex(canInterface.c_str());
    if (CANptr.can_ifindex == 0) {
        std::cerr << "错误: 无法找到指定的CAN设备: " << canInterface << std::endl;
        return false;
    }

    // 分配CANopen对象内存
    uint32_t heapMemoryUsed = 0;
    CO_config_t *config_ptr = nullptr;
    CO = CO_new(config_ptr, &heapMemoryUsed);
    if (CO == nullptr) {
        std::cerr << "错误: 创建CO对象失败, 已使用内存: " << heapMemoryUsed << std::endl;
        return false;
    }

    // Create epoll functions
    CO_ReturnError_t err;
    err = CO_epoll_create(&epMain_, MAIN_THREAD_INTERVAL_US);
    if (err != CO_ERROR_NO) {
        std::cerr << "CO_epoll_create(main), err=" << err << std::endl;
        return false;
    }

    err = CO_epoll_create(&epRt_, TMR_THREAD_INTERVAL_US);
    if (err != CO_ERROR_NO) {
        std::cerr << "CO_epoll_create(RT), err=" << err << std::endl;
        return false;
    }
    CANptr.epoll_fd = epRt_.epoll_fd;

    return true;
}

bool CANopenMaster::start() {
    if (CO == nullptr) {
        std::cerr << "错误: CANopen未初始化" << std::endl;
        return false;
    }

    // 启动主线程
    running = true;
    processThread = std::thread(&CANopenMaster::mainThreadFunction, this);

    sleep(1);   // 等待canopennode启动
    if (!isCANopenStarted)
        return false;
    return true;
}

bool CANopenMaster::stop() {
    if (CO == nullptr)
        return true;

    running = false;
    if (processThread.joinable()) {
        processThread.join();
    }

    if (rtThread.joinable()) {
        rtThread.join();
    }

    CO_epoll_close(&epRt_);
    CO_epoll_close(&epMain_);
    CO_CANsetConfigurationMode((void *) &CANptr);
    CO_delete(CO);
    CO = nullptr;
    log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, masterNodeId_, "finished");

    return true;
}

bool CANopenMaster::sendNMTCommand(uint8_t command, uint8_t nodeId) {
    if (CO == nullptr || CO->NMT == nullptr) {
        return false;
    }

    return CO_NMT_sendCommand(CO->NMT, static_cast<CO_NMT_command_t>(command), nodeId) == CO_ERROR_NO;;
}

bool CANopenMaster::sendSYNCMessage() {
    if (CO == nullptr || CO->SYNC == nullptr) {
        return false;
    }

    return CO_SYNCsend(CO->SYNC) == CO_ERROR_NO;
}

bool CANopenMaster::addNode(uint8_t nodeId) {
    if (nodeId < 1 || nodeId > 127) {
        std::cerr << "Error: Node ID out of range (1-127)" << std::endl;
        return false;
    }

    // 初始化节点数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    if (nodeDataMap.find(nodeId) == nodeDataMap.end()) {
        PDOData data = {};
        nodeDataMap[nodeId] = data;
    }

    return true;
}

bool CANopenMaster::findNode(uint8_t nodeId) {
    if (nodeId < 1 || nodeId > 127) {
        std::cerr << "Error: Node ID out of range (1-127)" << std::endl;
        return false;
    }

    // 读取设备类型以确认节点存在
    uint32_t deviceType = 0;
    size_t readSize = 0;
    CO_SDO_abortCode_t result = readSDO(nodeId, OD_DEVICE_TYPE, 0,
                                        reinterpret_cast<uint8_t *>(&deviceType),
                                        sizeof(deviceType), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(deviceType)) {
        return false;
    }

    return true;
}

DS402State CANopenMaster::parseStatusWord(uint16_t statusWord) {
    // 解析状态字
    if ((statusWord & 0x4F) == DS402_SW_OPERATION_ENABLED) {
        return DS402State::OPERATION_ENABLED;
    } else if ((statusWord & 0x4F) == DS402_SW_SWITCHED_ON) {
        return DS402State::SWITCHED_ON;
    } else if ((statusWord & 0x4F) == DS402_SW_READY_TO_SWITCH_ON) {
        return DS402State::READY_TO_SWITCH_ON;
    } else if ((statusWord & 0x4F) == DS402_SW_SWITCH_ON_DISABLED) {
        return DS402State::SWITCH_ON_DISABLED;
    } else if (statusWord & DS402_SW_QUICK_STOP_ACTIVE) {
        return DS402State::QUICK_STOP_ACTIVE;
    } else if ((statusWord & 0x4F) == DS402_SW_FAULT_REACTION_ACTIVE) {
        return DS402State::FAULT_REACTION_ACTIVE;
    } else if (statusWord & DS402_SW_FAULT) {
        return DS402State::FAULT;
    }

    return DS402State::NOT_READY_TO_SWITCH_ON;
}

uint16_t CANopenMaster::generateControlWord(DS402Command command) {
    // 生成控制字
    uint16_t controlWord = 0;

    switch (command) {
        case DS402Command::SHUTDOWN:
            controlWord = DS402_CW_SHUTDOWN;
            break;
        case DS402Command::SWITCH_ON:
            controlWord = DS402_CW_SWITCH_ON;
            break;
        case DS402Command::SWITCH_ON_AND_ENABLE_OPERATION:
            controlWord = DS402_CW_ENABLE_OPERATION;
            break;
        case DS402Command::DISABLE_VOLTAGE:
            controlWord = DS402_CW_DISABLE_VOLTAGE;
            break;
        case DS402Command::QUICK_STOP:
            controlWord = DS402_CW_QUICK_STOP;
            break;
        case DS402Command::DISABLE_OPERATION:
            controlWord = DS402_CW_DISABLE_OPERATION;
            break;
        case DS402Command::ENABLE_OPERATION:
            controlWord = DS402_CW_ENABLE_OPERATION;
            break;
        case DS402Command::FAULT_RESET:
            controlWord = DS402_CW_FAULT_RESET;
            break;
    }

    return controlWord;
}

bool CANopenMaster::sendControlWord(uint8_t nodeId, uint16_t controlWord) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_CONTROL_WORD, 0,
                                         reinterpret_cast<uint8_t *>(&controlWord),
                                         sizeof(controlWord));

    CHECK_SDO_RESULT(result, "Failed to send control word");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.controlWord = controlWord;
    }

    return true;
}

bool CANopenMaster::sendControlWordCommand(uint8_t nodeId, DS402Command command) {
    uint16_t controlWord = generateControlWord(command);
    return sendControlWord(nodeId, controlWord);
}

DS402State CANopenMaster::getState(uint8_t nodeId) {
    uint16_t statusWord = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_STATUS_WORD, 0,
                                        reinterpret_cast<uint8_t *>(&statusWord),
                                        sizeof(statusWord), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(statusWord)) {
        return DS402State::NOT_READY_TO_SWITCH_ON;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.statusWord = statusWord;
        }
    }

    return parseStatusWord(statusWord);
}

uint16_t CANopenMaster::getStatusWord(uint8_t nodeId) {
    uint16_t statusWord = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_STATUS_WORD, 0,
                                        reinterpret_cast<uint8_t *>(&statusWord),
                                        sizeof(statusWord), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(statusWord)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.statusWord = statusWord;
        }
    }

    return statusWord;
}

bool CANopenMaster::setOperationMode(uint8_t nodeId, DS402OperationMode mode) {
    int8_t modeValue = static_cast<int8_t>(mode);
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_OPERATION_MODE, 0,
                                         reinterpret_cast<uint8_t *>(&modeValue),
                                         sizeof(modeValue));

    CHECK_SDO_RESULT(result, "Failed to set operation mode");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.operationMode = modeValue;
    }

    return true;
}

DS402OperationMode CANopenMaster::getOperationMode(uint8_t nodeId) {
    int8_t mode = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_OPERATION_MODE_DISP, 0,
                                        reinterpret_cast<uint8_t *>(&mode),
                                        sizeof(mode), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(mode)) {
        return DS402OperationMode::PROFILE_POSITION;  // 默认值
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.operationMode = mode;
        }
    }

    return static_cast<DS402OperationMode>(mode);
}

bool CANopenMaster::setTargetPosition(uint8_t nodeId, int32_t position) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_TARGET_POSITION, 0,
                                         reinterpret_cast<uint8_t *>(&position),
                                         sizeof(position));

    CHECK_SDO_RESULT(result, "Failed to set target position");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.targetPosition = position;
    }

    return true;
}

int32_t CANopenMaster::getActualPosition(uint8_t nodeId) {
    int32_t position = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_ACTUAL_POSITION, 0,
                                        reinterpret_cast<uint8_t *>(&position),
                                        sizeof(position), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(position)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.actualPosition = position;
        }
    }

    return position;
}

bool CANopenMaster::setTargetVelocity(uint8_t nodeId, int32_t velocity) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_TARGET_VELOCITY, 0,
                                         reinterpret_cast<uint8_t *>(&velocity),
                                         sizeof(velocity));

    CHECK_SDO_RESULT(result, "Failed to set target velocity");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.targetVelocity = velocity;
    }

    return true;
}

int32_t CANopenMaster::getActualVelocity(uint8_t nodeId) {
    int32_t velocity = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_ACTUAL_VELOCITY, 0,
                                        reinterpret_cast<uint8_t *>(&velocity),
                                        sizeof(velocity), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(velocity)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.actualVelocity = velocity;
        }
    }

    return velocity;
}

bool CANopenMaster::setProfileAcceleration(uint8_t nodeId, uint32_t acceleration) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_PROFILE_ACCELERATION, 0,
                                         reinterpret_cast<uint8_t *>(&acceleration),
                                         sizeof(acceleration));

    CHECK_SDO_RESULT(result, "Failed to set profile acceleration");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.profileAcceleration = acceleration;
    }

    return true;
}

bool CANopenMaster::setProfileDeceleration(uint8_t nodeId, uint32_t deceleration) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_PROFILE_DECELERATION, 0,
                                         reinterpret_cast<uint8_t *>(&deceleration),
                                         sizeof(deceleration));

    CHECK_SDO_RESULT(result, "Failed to set profile deceleration");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.profileDeceleration = deceleration;
    }

    return true;
}

uint32_t CANopenMaster::getProfileAcceleration(uint8_t nodeId) {
    uint32_t acceleration = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_PROFILE_ACCELERATION, 0,
                                        reinterpret_cast<uint8_t *>(&acceleration),
                                        sizeof(acceleration), &readSize);
}

uint32_t CANopenMaster::getProfileDeceleration(uint8_t nodeId) {
    uint32_t deceleration = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_PROFILE_DECELERATION, 0,
                                        reinterpret_cast<uint8_t *>(&deceleration),
                                        sizeof(deceleration), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(deceleration)) {
        return 0;
    }

    return deceleration;
}

bool CANopenMaster::setTargetTorque(uint8_t nodeId, int16_t torque) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_TARGET_TORQUE, 0,
                                         reinterpret_cast<uint8_t *>(&torque),
                                         sizeof(torque));

    CHECK_SDO_RESULT(result, "Failed to set target torque");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.targetTorque = torque;
    }

    return true;
}

int16_t CANopenMaster::getActualTorque(uint8_t nodeId) {
    int16_t torque = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_ACTUAL_TORQUE, 0,
                                        reinterpret_cast<uint8_t *>(&torque),
                                        sizeof(torque), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(torque)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.actualTorque = torque;
        }
    }

    return torque;
}

bool CANopenMaster::setHeartbeatTime(uint8_t nodeId, uint16_t heartbeatTime) {
    // 设置心跳时间（单位：ms）
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_HEARTBEAT_TIME, 0x00,
                                         reinterpret_cast<uint8_t *>(&heartbeatTime),
                                         sizeof(heartbeatTime));

    if (result != CO_SDO_AB_NONE) {
        std::cerr << "设置心跳时间失败, 错误码: 0x" << std::hex << result << std::dec << std::endl;
        return false;
    }

    return true;
}

uint16_t CANopenMaster::getHeartbeatTime(uint8_t nodeId) {
    uint16_t heartbeatTime = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_HEARTBEAT_TIME, 0x00,
                                        reinterpret_cast<uint8_t *>(&heartbeatTime),
                                        sizeof(heartbeatTime), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(heartbeatTime)) {
        std::cerr << "获取心跳时间失败, 错误码: 0x" << std::hex << result << std::dec << std::endl;
        return 0;
    }

    return heartbeatTime;
}

bool CANopenMaster::configureHeartbeatConsumer(uint8_t nodeId, uint16_t heartbeatTime) {
    // 配置心跳消费者
    if (CO->HBcons == nullptr) {
        std::cerr << "心跳消费者未初始化" << std::endl;
        return false;
    }

    // 设置心跳消费者参数（对象字典条目1016h，子索引是节点ID）
    uint8_t subIndex = nodeId;
    if (subIndex > 127 || subIndex == 0) {
        std::cerr << "无效的节点ID: " << static_cast<int>(nodeId) << std::endl;
        return false;
    }

    // 心跳消费者条目格式：高16位是节点ID，低16位是心跳时间（ms）
    uint32_t consumerEntry = (static_cast<uint32_t>(nodeId) << 16) | heartbeatTime;

    // 将节点ID映射到心跳消费者数组的索引
    CO_HBconsumer_t *HBcons = &CO->HBcons[0];
    if (HBcons == nullptr) {
        std::cerr << "心跳消费者对象未初始化" << std::endl;
        return false;
    }

    // 直接通过OD设置心跳消费者配置
    ODR_t odRet = OD_set_u32(OD_find(OD, 0x1016), subIndex, consumerEntry, true);
    if (odRet != ODR_OK) {
        std::cerr << "设置心跳消费者配置失败, 返回值: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    return true;
}

//  SDO数据读写
CO_SDO_abortCode_t CANopenMaster::readSDO(uint8_t nodeId, uint16_t index, uint8_t subIndex,
                                          uint8_t *buffer, size_t bufferSize, size_t *readSize) {
    if (CO == nullptr || CO->SDOclient == nullptr || buffer == nullptr || readSize == nullptr) {
        return CO_SDO_AB_GENERAL;
    }

    CO_SDO_return_t SDO_ret;

    // 设置SDO客户端
    SDO_ret = CO_SDOclient_setup(CO->SDOclient, CO_CAN_ID_SDO_CLI + nodeId, CO_CAN_ID_SDO_SRV + nodeId, nodeId);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // 初始化上传过程
    SDO_ret = CO_SDOclientUploadInitiate(CO->SDOclient, index, subIndex, 1000, false);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // 上传数据循环
    do {
        uint32_t timeDifference_us = 10000;
        CO_SDO_abortCode_t abortCode = CO_SDO_AB_NONE;

        // 检查是否有新消息
        if (CO_FLAG_READ(CO->SDOclient->CANrxNew)) {
            LOG_DEBUG_MSG(domain_, "检测到新的CAN消息\n");
        }

        SDO_ret = CO_SDOclientUpload(CO->SDOclient, timeDifference_us, false, &abortCode, NULL, NULL, NULL);
        if (SDO_ret < 0) {
            LOG_ERROR_MSG(domain_, "SDO上传失败，错误码: 0x%08X\n", abortCode);
            return abortCode;
        }

        usleep(timeDifference_us);
    } while (SDO_ret > 0);

    // 将数据复制到用户缓冲区
    *readSize = CO_SDOclientUploadBufRead(CO->SDOclient, buffer, bufferSize);

    return CO_SDO_AB_NONE;
}

CO_SDO_abortCode_t CANopenMaster::writeSDO(uint8_t nodeId, uint16_t index, uint8_t subIndex,
                                           uint8_t *data, size_t dataSize) {
    if (CO == nullptr || CO->SDOclient == nullptr || data == nullptr) {
        return CO_SDO_AB_GENERAL;
    }

    CO_SDO_return_t SDO_ret;
    bool_t bufferPartial = false;

    // 设置SDO客户端
    SDO_ret = CO_SDOclient_setup(CO->SDOclient, CO_CAN_ID_SDO_CLI + nodeId, CO_CAN_ID_SDO_SRV + nodeId, nodeId);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // 初始化下载过程
    SDO_ret = CO_SDOclientDownloadInitiate(CO->SDOclient, index, subIndex, dataSize, 1000, false);
    if (SDO_ret != CO_SDO_RT_ok_communicationEnd) {
        return CO_SDO_AB_GENERAL;
    }

    // 填充数据
    size_t nWritten = CO_SDOclientDownloadBufWrite(CO->SDOclient, data, dataSize);
    if (nWritten < dataSize) {
        bufferPartial = true;
        // 如果SDO FIFO缓冲区太小，数据可以在循环中重新填充
    }

    // 下载数据循环
    do {
        uint32_t timeDifference_us = 10000;
        CO_SDO_abortCode_t abortCode = CO_SDO_AB_NONE;

        SDO_ret = CO_SDOclientDownload(CO->SDOclient, timeDifference_us, false, bufferPartial, &abortCode, NULL, NULL);
        if (SDO_ret < 0) {
            return abortCode;
        }

        usleep(timeDifference_us);
    } while (SDO_ret > 0);

    return CO_SDO_AB_NONE;
}

std::string CANopenMaster::getSDOAbortCodeDescription(CO_SDO_abortCode_t abortCode) {
    switch (abortCode) {
        case CO_SDO_AB_NONE:
            return "无错误 (No abort)";
            
        // 通信错误
        case CO_SDO_AB_TOGGLE_BIT:
            return "切换位未改变 (Toggle bit not altered)";
        case CO_SDO_AB_TIMEOUT:
            return "SDO协议超时 (SDO protocol timed out)";
        case CO_SDO_AB_CMD:
            return "命令指示符无效或未知 (Command specifier not valid or unknown)";
        case CO_SDO_AB_BLOCK_SIZE:
            return "块模式下的块大小无效 (Invalid block size in block mode)";
        case CO_SDO_AB_SEQ_NUM:
            return "块模式下的序列号无效 (Invalid sequence number in block mode)";
        case CO_SDO_AB_CRC:
            return "CRC错误 (CRC error, block mode only)";
        case CO_SDO_AB_OUT_OF_MEM:
            return "内存不足 (Out of memory)";
            
        // 访问权限错误
        case CO_SDO_AB_UNSUPPORTED_ACCESS:
            return "不支持对对象的访问 (Unsupported access to an object)";
        case CO_SDO_AB_WRITEONLY:
            return "尝试读取一个只写对象 (Attempt to read a write only object)";
        case CO_SDO_AB_READONLY:
            return "尝试写入一个只读对象 (Attempt to write a read only object)";
            
        // 对象字典错误
        case CO_SDO_AB_NOT_EXIST:
            return "对象不存在于对象字典中 (Object does not exist in the object dictionary)";
        case CO_SDO_AB_NO_MAP:
            return "对象不能映射到PDO (Object cannot be mapped to the PDO)";
        case CO_SDO_AB_MAP_LEN:
            return "要映射的对象数量和长度超过PDO长度 (Number and length of object to be mapped exceeds PDO length)";
        case CO_SDO_AB_PRAM_INCOMPAT:
            return "一般参数不兼容原因 (General parameter incompatibility reasons)";
        case CO_SDO_AB_DEVICE_INCOMPAT:
            return "设备内部一般不兼容 (General internal incompatibility in device)";
        case CO_SDO_AB_HW:
            return "由于硬件错误访问失败 (Access failed due to hardware error)";
            
        // 数据类型错误
        case CO_SDO_AB_TYPE_MISMATCH:
            return "数据类型不匹配，服务参数长度不匹配 (Data type does not match, length of service parameter does not match)";
        case CO_SDO_AB_DATA_LONG:
            return "数据类型不匹配，服务参数过长 (Data type does not match, length of service parameter too high)";
        case CO_SDO_AB_DATA_SHORT:
            return "数据类型不匹配，服务参数过短 (Data type does not match, length of service parameter too short)";
            
        // 子索引错误
        case CO_SDO_AB_SUB_UNKNOWN:
            return "子索引不存在 (Sub index does not exist)";
            
        // 值范围错误
        case CO_SDO_AB_INVALID_VALUE:
            return "参数值无效 (Invalid value for parameter, download only)";
        case CO_SDO_AB_VALUE_HIGH:
            return "写入的参数值超过上限 (Value range of parameter written too high)";
        case CO_SDO_AB_VALUE_LOW:
            return "写入的参数值低于下限 (Value range of parameter written too low)";
        case CO_SDO_AB_MAX_LESS_MIN:
            return "最大值小于最小值 (Maximum value is less than minimum value)";
            
        // 资源错误
        case CO_SDO_AB_NO_RESOURCE:
            return "资源不可用: SDO连接 (Resource not available: SDO connection)";
            
        // 一般错误
        case CO_SDO_AB_GENERAL:
            return "一般错误 (General error)";
        case CO_SDO_AB_DATA_TRANSF:
            return "数据无法传输或存储到应用程序 (Data cannot be transferred or stored to application)";
        case CO_SDO_AB_DATA_LOC_CTRL:
            return "由于本地控制，数据无法传输或存储到应用程序 (Data cannot be transferred or stored to application because of local control)";
        case CO_SDO_AB_DATA_DEV_STATE:
            return "由于当前设备状态，数据无法传输或存储到应用程序 (Data cannot be transferred or stored to application because of present device state)";
        case CO_SDO_AB_DATA_OD:
            return "对象字典不存在或动态生成失败 (Object dictionary not present or dynamic generation fails)";
        case CO_SDO_AB_NO_DATA:
            return "无可用数据 (No data available)";
            
        default:
            return "未知错误代码 (Unknown error code)";
    }
}

void CANopenMaster::handleError(CO_SDO_abortCode_t abortCode) {
    // 如果没有错误，直接返回
    if (abortCode == CO_SDO_AB_NONE) {
        return;
    }
    
    // 获取错误描述
    std::string errorDesc = getSDOAbortCodeDescription(abortCode);
    
    // 记录错误信息到控制台
    std::cerr << "SDO错误: " << errorDesc << " (0x" << std::hex << abortCode << std::dec << ")" << std::endl;
    
    // 记录错误信息到系统日志
    syslog(LOG_ERR, "CANopen SDO错误: %s (0x%08X)", errorDesc.c_str(), abortCode);
    
    // 根据错误类型执行不同的处理逻辑
    if (abortCode == CO_SDO_AB_TIMEOUT) {
        // 超时错误可能需要重试或重新初始化通信
        syslog(LOG_WARNING, "SDO通信超时，可能需要检查设备连接状态");
    } else if (abortCode == CO_SDO_AB_NOT_EXIST) {
        // 对象不存在错误，可能是配置问题
        syslog(LOG_WARNING, "访问的对象字典条目不存在，请检查设备配置");
    } else if (abortCode == CO_SDO_AB_READONLY || abortCode == CO_SDO_AB_WRITEONLY) {
        // 访问权限错误
        syslog(LOG_WARNING, "对象访问权限错误，请检查操作类型");
    }
    
    // 对于严重错误，可以考虑通知上层应用
    if (abortCode == CO_SDO_AB_HW || abortCode == CO_SDO_AB_DEVICE_INCOMPAT) {
        // 这里可以添加通知机制，例如设置错误标志或回调
        syslog(LOG_ERR, "检测到严重的CANopen错误，可能需要重新初始化设备");
    }
}

//  CANopen主线程
void CANopenMaster::mainThreadFunction() {
    /* get current time for CO_TIME_set(), since January 1, 1984, UTC. */
    struct timespec ts;
    if (clock_gettime(CLOCK_REALTIME, &ts) == -1) {
        log_printf(LOG_CRIT, DBG_GENERAL, "clock_gettime(main)", 0);
        exit(EXIT_FAILURE);
    }
    uint16_t time_days = (uint16_t) (ts.tv_sec / (24 * 60 * 60));
    time_days -= 5113; /* difference between Unix epoch and CANopen Epoch */
    uint32_t time_ms = (uint32_t) (ts.tv_sec % (24 * 60 * 60)) * 1000;
    time_ms += ts.tv_nsec / 1000000;

    bool_t firstRun = true;
    CO_NMT_reset_cmd_t reset = CO_RESET_NOT;
    CO_ReturnError_t err;
    while (reset != CO_RESET_APP && reset != CO_RESET_QUIT && running) {
        /* CANopen communication reset - initialize CANopen objects *******************/

        /* Wait rt_thread. */
        if (!firstRun) {
            CO_LOCK_OD(CO->CANmodule);
            CO->CANmodule->CANnormal = false;
            CO_UNLOCK_OD(CO->CANmodule);
        }

        /* Enter CAN configuration. */
        CO_CANsetConfigurationMode((void *) &CANptr);
        CO_CANmodule_disable(CO->CANmodule);

        /* Initialize CAN driver*/
        err = CO_CANinit(CO, (void *) &CANptr, 0 /* bit rate not used */);
        if (err != CO_ERROR_NO) {
            log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANinit()", err);
            continue;
        }

        mlStorage.pendingNodeId = masterNodeId_;
        CO_LSS_address_t lssAddress = {.identity = {.vendorID = OD_PERSIST_COMM.x1018_identity.vendor_ID,
                .productCode = OD_PERSIST_COMM.x1018_identity.productCode,
                .revisionNumber = OD_PERSIST_COMM.x1018_identity.revisionNumber,
                .serialNumber = OD_PERSIST_COMM.x1018_identity.serialNumber}};
        err = CO_LSSinit(CO, &lssAddress, &mlStorage.pendingNodeId, &mlStorage.pendingBitRate);
        if (err != CO_ERROR_NO) {
            log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_LSSinit()", err);
            continue;
        }

        uint32_t errInfo = 0;
        //  Initialize CANopenNode except PDO objects.
        err = CO_CANopenInit(CO,                   /* CANopen object */
                             NULL,                 /* alternate NMT */
                             NULL,                 /* alternate em */
                             OD,                   /* Object dictionary */
                             OD_STATUS_BITS,       /* Optional OD_statusBits */
                             NMT_CONTROL,          /* CO_NMT_control_t */
                             FIRST_HB_TIME,        /* firstHBTime_ms */
                             SDO_SRV_TIMEOUT_TIME, /* SDOserverTimeoutTime_ms */
                             SDO_CLI_TIMEOUT_TIME, /* SDOclientTimeoutTime_ms */
                             SDO_CLI_BLOCK,        /* SDOclientBlockTransfer */
                             masterNodeId_, &errInfo);
        if (err != CO_ERROR_NO && err != CO_ERROR_NODE_ID_UNCONFIGURED_LSS) {
            if (err == CO_ERROR_OD_PARAMETERS) {
                log_printf(LOG_CRIT, DBG_OD_ENTRY, errInfo);
            } else {
                log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANopenInit()", err);
            }
            continue;
        }

        /* initialize part of threadMain and callbacks */
        CO_epoll_initCANopenMain(&epMain_, CO);

        CO_LSSslave_initCfgStoreCall(CO->LSSslave, &mlStorage, LSScfgStoreCallback);
        if (!CO->nodeIdUnconfigured) {
            if (errInfo != 0) {
                CO_errorReport(CO->em, CO_EM_INCONSISTENT_OBJECT_DICT, CO_EMC_DATA_SET, errInfo);
            }

            //  Register callback functions
            CO_EM_initCallbackRx(CO->em, EmergencyRxCallback);

            CO_NMT_initCallbackChanged(CO->NMT, NmtChangedCallback);

            CO_HBconsumer_initCallbackNmtChanged(CO->HBcons, 0, NULL, HeartbeatNmtChangedCallback);


#if (CO_CONFIG_TRACE) & CO_CONFIG_TRACE_ENABLE
            /* Initialize time */
            CO_time_init(&CO_time, CO->SDO[0], &OD_time.epochTimeBaseMs, &OD_time.epochTimeOffsetMs, 0x2130);
#endif
            log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, masterNodeId_, "communication reset");
        } else {
            log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, masterNodeId_, "node-id not initialized");
        }

        /* First time only initialization. */
        if (firstRun) {
            firstRun = false;
            CO_TIME_set(CO->TIME, time_ms, time_days, TIME_STAMP_INTERVAL_MS);

            /* Create rt_thread and set priority */
            rtThread = std::thread(&CANopenMaster::rtThreadFunction, this);
        } /* if(firstRun) */


        //  Initialize PDO objects.
        errInfo = 0;
        err = CO_CANopenInitPDO(CO,     /* CANopen object */
                                CO->em, /* emergency object */
                                OD,     /* Object dictionary */
                                masterNodeId_, &errInfo);
        if (err != CO_ERROR_NO && err != CO_ERROR_NODE_ID_UNCONFIGURED_LSS) {
            if (err == CO_ERROR_OD_PARAMETERS) {
                log_printf(LOG_CRIT, DBG_OD_ENTRY, errInfo);
            } else {
                log_printf(LOG_CRIT, DBG_CAN_OPEN, "CO_CANopenInitPDO()", err);
            }
            continue;
        }

        /* start CAN */
        CO_CANsetNormalMode(CO->CANmodule);

        //  canopen启动标志
        isCANopenStarted = true;

        log_printf(LOG_INFO, DBG_CAN_OPEN_INFO, masterNodeId_, "running ...");
        reset = CO_RESET_NOT;
        while (reset == CO_RESET_NOT && running) {
            /* loop for normal program execution ******************************************/
            CO_epoll_wait(&epMain_);

            CO_epoll_processMain(&epMain_, CO, GATEWAY_ENABLE, &reset);

            CO_epoll_processLast(&epMain_);
        }
    } /* while(reset != CO_RESET_APP */
}

//  CANopen实时线程
void CANopenMaster::rtThreadFunction() {
    // 实时线程主循环
    while (running) {
        // 等待epoll事件
        CO_epoll_wait(&epRt_);

        // 处理CAN接收和实时功能
        CO_epoll_processRT(&epRt_, CO, true);

        // 完成epoll处理
        CO_epoll_processLast(&epRt_);
    }

    // 清理
    CO_epoll_close(&epRt_);
}

//  CANopen子节点初始化
bool CANopenMaster::initializeNMTState(uint8_t nodeId) {
    bool result = sendNMTCommand(NMT_RESET_NODE, nodeId);
    sleep(10);   // 保证初始化完成
    
    result &= sendNMTCommand(NMT_ENTER_PRE_OPERATIONAL, nodeId);
    usleep(5000);
    return result;
}

// 获取指定节点和PDO编号的映射配置
PDOMappingCache* CANopenMaster::getPDOMappingCache(uint8_t nodeId, uint8_t pdoNum) {
    std::lock_guard<std::mutex> lock(pdoMappingMutex);
    auto nodeIt = pdoMappingCache.find(nodeId);
    if (nodeIt != pdoMappingCache.end()) {
        auto pdoIt = nodeIt->second.find(pdoNum);
        if (pdoIt != nodeIt->second.end()) {
            return &pdoIt->second;
        }
    }
    return nullptr;
}

// PDO数据内部处理函数
void CANopenMaster::processPDOData(uint8_t nodeId, uint8_t rpdoNum, const uint8_t *data, size_t length) {
    if (data == nullptr || length == 0) {
        return;
    }

    // 获取该节点PDO的映射配置
    // 注意这里需要保证主站RPDO和子站TPDO的映射配置是完全相同的，否则会导致数据解析错误
    PDOMappingCache* mappingCache = getPDOMappingCache(nodeId, rpdoNum);
    if (mappingCache == nullptr) {
        std::cerr << "mappingCache is nullptr" << std::endl;
        return;
    }

    // 根据映射配置解析数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        PDOData &pdoData = it->second;
        
        size_t dataPos = 0; // 数据位置指针
        
        // 遍历所有映射条目
        for (const auto& entry : mappingCache->entries) {
            if (dataPos + (entry.length / 8) > length) {
                break; // 超出数据长度，停止处理
            }
            
            // 根据映射条目处理数据
            switch (entry.index) {
                case OD_STATUS_WORD:
                    if (entry.length == 16) { // 16位
                        pdoData.statusWord = data[dataPos] | (data[dataPos + 1] << 8);
                        dataPos += 2;
                    }
                    break;
                    
                case OD_ACTUAL_POSITION:
                    if (entry.length == 32) { // 32位
                        pdoData.actualPosition = data[dataPos] | (data[dataPos + 1] << 8) | 
                                              (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;
                    
                case OD_ACTUAL_VELOCITY:
                    if (entry.length == 32) { // 32位
                        pdoData.actualVelocity = data[dataPos] | (data[dataPos + 1] << 8) | 
                                               (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;
                    
                case OD_ACTUAL_TORQUE:
                    if (entry.length == 16) { // 16位
                        pdoData.actualTorque = data[dataPos] | (data[dataPos + 1] << 8);
                        dataPos += 2;
                    }
                    break;
                    
                case OD_OPERATION_MODE_DISP:
                    if (entry.length == 8) { // 8位
                        pdoData.operationMode = static_cast<int8_t>(data[dataPos]);
                        dataPos += 1;
                    }
                    break;

                case OD_CONTROL_WORD:
                    if (entry.length == 16) { // 16位
                        pdoData.controlWord = data[dataPos] | (data[dataPos + 1] << 8);
                        dataPos += 2;
                    }
                    break;

                case OD_TARGET_POSITION:
                    if (entry.length == 32) { // 32位
                        pdoData.targetPosition = data[dataPos] | (data[dataPos + 1] << 8) | 
                                               (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;

                case OD_TARGET_VELOCITY:
                    if (entry.length == 32) { // 32位
                        pdoData.targetVelocity = data[dataPos] | (data[dataPos + 1] << 8) | 
                                               (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;

                case OD_TARGET_TORQUE:
                    if (entry.length == 16) { // 16位
                        pdoData.targetTorque = data[dataPos] | (data[dataPos + 1] << 8);
                        dataPos += 2;
                    }
                    break;

                case OD_INPUT_PIN_STATUS:
                    if (entry.length == 16) { // 16位
                        pdoData.inputPinStatus = data[dataPos] | (data[dataPos + 1] << 8);
                        dataPos += 2;
                    }
                    break;

                case OD_TOUCH_PROBE_POSITION:
                    if (entry.length == 32) { // 32位
                        pdoData.touchProbePosition = data[dataPos] | (data[dataPos + 1] << 8) | 
                                                   (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;

                case OD_TOUCH_PROBE_TIME:
                    if (entry.length == 32) { // 32位
                        pdoData.touchProbeTime = data[dataPos] | (data[dataPos + 1] << 8) | 
                                               (data[dataPos + 2] << 16) | (data[dataPos + 3] << 24);
                        dataPos += 4;
                    }
                    break;
                    
                default:
                    // 其他映射对象，跳过相应字节数
                    dataPos += entry.length / 8;
                    break;
            }
        }
        
        /*std::cout << "处理节点 " << static_cast<int>(nodeId)
                  << " 的RPDO" << static_cast<int>(rpdoNum) 
                  << " 数据，状态字: 0x" << std::hex << pdoData.statusWord << std::dec;

        // 根据DS402状态字解析设备状态
        DS402State state = parseStatusWord(pdoData.statusWord);
        std::cout << " 状态: ";
        switch (state) {
            case DS402State::NOT_READY_TO_SWITCH_ON: std::cout << "未就绪"; break;
            case DS402State::SWITCH_ON_DISABLED: std::cout << "使能禁止"; break;
            case DS402State::READY_TO_SWITCH_ON: std::cout << "就绪"; break;
            case DS402State::SWITCHED_ON: std::cout << "已通电"; break;
            case DS402State::OPERATION_ENABLED: std::cout << "运行中"; break;
            case DS402State::QUICK_STOP_ACTIVE: std::cout << "急停激活"; break;
            case DS402State::FAULT_REACTION_ACTIVE: std::cout << "故障反应"; break;
            case DS402State::FAULT: std::cout << "故障"; break;
        }

        // 显示位置、速度和扭矩信息
        std::cout << ", 位置: " << pdoData.actualPosition
                  << ", 速度: " << pdoData.actualVelocity;
        
        if (pdoData.actualTorque != 0) {
            std::cout << ", 扭矩: " << pdoData.actualTorque;
        }
        
        // 显示操作模式
        if (pdoData.operationMode != 0) {
            std::cout << ", 模式: ";
            switch (pdoData.operationMode) {
                case 1: std::cout << "Profile Position"; break;
                case 3: std::cout << "Profile Velocity"; break;
                case 6: std::cout << "Homing"; break;
                case 8: std::cout << "Cyclic Sync Position"; break;
                default: std::cout << static_cast<int>(pdoData.operationMode); break;
            }
        }
        
        // 显示输入引脚状态
        if (pdoData.inputPinStatus != 0) {
            std::cout << ", 输入引脚: 0x" << std::hex << pdoData.inputPinStatus << std::dec;
        }
        
        // 显示探针位置和时间
        if (pdoData.touchProbePosition != 0 || pdoData.touchProbeTime != 0) {
            std::cout << ", 探针位置: " << pdoData.touchProbePosition
                     << ", 探针时间: " << pdoData.touchProbeTime;
        }
        
        std::cout << std::endl;*/
    }
}

// RPDO回调函数
void CANopenMaster::rpdoCallback(void *object) {
    if (object != nullptr && masterInstance != nullptr) {
        // 从RPDO接收到数据并更新
        auto *rpdo = static_cast<CO_RPDO_t *>(object);

        if (rpdo == nullptr) return;

        // 确定RPDOIndex (0-3)
        uint8_t rpdoIdx = 0;
        if (masterInstance->CO != nullptr && masterInstance->CO->RPDO != nullptr) {
            for (uint8_t i = 0; i < OD_CNT_RPDO; i++) {
                if (rpdo == &(masterInstance->CO->RPDO[i])) {
                    rpdoIdx = i;
                    break;
                }
            }
        }
        
        uint8_t rpdoNum = rpdoIdx + 1; // 转换为1-4的PDO编号

        // 处理接收到的数据
        if (rpdo->CANrxNew[0] != nullptr) {
            uint8_t *data = rpdo->CANrxData[0];
            size_t length = rpdo->PDO_common.dataLength;

            // 获取节点ID (从RPDO的COB-ID)
            uint8_t nodeId = (rpdo->PDO_common.configuredCanId & 0x7F);
            uint16_t cobId = rpdo->PDO_common.configuredCanId;

            /*// 打印详细的接收信息
            std::cout << "收到RPDO" << static_cast<int>(rpdoNum) << "，来自节点:" << static_cast<int>(nodeId) 
                    << "，COB-ID: 0x" << std::hex << cobId << std::dec
                    << "，数据长度:" << length << std::endl;

            // 打印数据内容（16进制格式）
            if (length > 0) {
                std::cout << "数据内容: ";
                for (size_t i = 0; i < length; i++) {
                    std::cout << "0x" << std::hex << std::setw(2) << std::setfill('0') 
                              << static_cast<int>(data[i]) << " ";
                }
                std::cout << std::dec << std::endl;
            }*/
                      
            // 处理数据
            masterInstance->processPDOData(nodeId, rpdoNum, data, length);

            // 调用用户注册的回调
            {
                std::lock_guard<std::mutex> lock(masterInstance->pdoCallbackMutex);
                auto key = std::make_pair(PDOType::RPDO, rpdoNum);
                auto it = masterInstance->pdoCallbacks.find(key);
                if (it != masterInstance->pdoCallbacks.end() && it->second) {
                    it->second(nodeId, data, length);
                }
            }
        }
    }
}

// TPDO回调函数
void CANopenMaster::tpdoCallback(void *object) {
}

// 配置RPDO
bool CANopenMaster::configureRPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig& config) {
    // 使用新接口配置RPDO
    return configurePDO(nodeId, PDOType::RPDO, pdoIndex, config);
}

// 配置TPDO
bool CANopenMaster::configureTPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig& config) {
    // 使用新接口配置TPDO
    bool result = configurePDO(nodeId, PDOType::TPDO, pdoIndex, config);

    if (result) {
        // 保存PDO映射关系到缓存
        std::lock_guard<std::mutex> lock(pdoMappingMutex);
        PDOMappingCache& mappingCache = pdoMappingCache[nodeId][pdoIndex];
        mappingCache.entries = config.mappingEntries;
        mappingCache.transmissionType = config.transmissionType;

        // 自动配置主站对应的RPDO
        // todo: 子站TPDO编号与主站RPDO编号存在一定的转换关系
        /*bool rpdoResult = configLocalRPDO(nodeId, pdoIndex, config);
        if (!rpdoResult) {
            std::cerr << "警告: 主站RPDO" << static_cast<int>(pdoIndex)
                      << "配置失败，节点ID: " << static_cast<int>(nodeId) << std::endl;
            // 注意：这里不返回false，因为子站TPDO配置成功了
        } else {
            std::cout << "已自动配置主站RPDO" << static_cast<int>(pdoIndex)
                      << "以接收节点" << static_cast<int>(nodeId) << "的TPDO数据" << std::endl;
        }*/
    }
    return result;
}

// 实现配置任意PDO的函数
bool CANopenMaster::configurePDO(uint8_t nodeId, PDOType pdoType, uint8_t pdoNum, const PDOConfig &config) {
    if (!checkPDOIndex(pdoNum)) {
        LOG_ERROR_MSG(domain_, "PDO编号必须在1到%d之间!", OD_CNT_RPDO);
        return false;
    }

    // 确定PDO参数和映射的对象字典索引
    uint16_t pdoParamIndex, pdoMappingIndex;
    if (pdoType == PDOType::RPDO) {
        pdoParamIndex = OD_RPDO1_PARAM + (pdoNum - 1);
        pdoMappingIndex = OD_RPDO1_MAPPING + (pdoNum - 1);
    } else {
        pdoParamIndex = OD_TPDO1_PARAM + (pdoNum - 1);
        pdoMappingIndex = OD_TPDO1_MAPPING + (pdoNum - 1);
    }

    // 禁用PDO映射
    uint8_t mapCount = 0;
    CO_SDO_abortCode_t result = writeSDO(nodeId, pdoMappingIndex, OD_PDO_SUBIDX_COUNT, &mapCount, sizeof(mapCount));
    CHECK_SDO_RESULT(result, "Failed to disable PDO mapping");

    // 设置COB-ID
    uint32_t cobId;
    if (pdoType == PDOType::RPDO) {
        cobId = 0x200 + (pdoNum - 1) * 0x100 + nodeId;  // RPDO COB-ID
    } else {
        cobId = 0x180 + (pdoNum - 1) * 0x100 + nodeId;  // TPDO COB-ID

        //  5-8号TPDO的COB-ID以0x780为基准值，避免与SDO冲突
        if (pdoNum > 4)
        {
            cobId = 0x780 + (pdoNum - 1 - 4) * 0x100 + nodeId;
        }
    }


    result = writeSDO(nodeId, pdoParamIndex, OD_PDO_SUBIDX_COB_ID,
                      reinterpret_cast<uint8_t *>(&cobId),
                      sizeof(cobId));
    CHECK_SDO_RESULT(result, "Failed to set PDO COB-ID");

    // 设置传输类型
    uint8_t transmissionType = config.transmissionType;
    result = writeSDO(nodeId, pdoParamIndex, OD_PDO_SUBIDX_TRANSMISSION_TYPE,
                      &transmissionType, sizeof(transmissionType));
    CHECK_SDO_RESULT(result, "Failed to set PDO transmission type");

    // 配置PDO映射条目
    for (size_t i = 0; i < config.mappingEntries.size(); i++) {
        if (i >= 8) {  // CANopen限制每个PDO最多映射8个对象
            std::cerr << "警告: 超过最大映射数量限制，仅使用前8个映射" << std::endl;
            break;
        }

        const auto &entry = config.mappingEntries[i];
        uint32_t mapping = createPDOMapping(entry.index, entry.subIndex, entry.length);

        result = writeSDO(nodeId, pdoMappingIndex, static_cast<uint8_t>(i + 1),
                          reinterpret_cast<uint8_t *>(&mapping),
                          sizeof(mapping));
        CHECK_SDO_RESULT(result, "Failed to set PDO mapping entry");
    }

    // 启用PDO映射
    mapCount = static_cast<uint8_t>(std::min(config.mappingEntries.size(), size_t(8)));
    result = writeSDO(nodeId, pdoMappingIndex, OD_PDO_SUBIDX_COUNT, &mapCount, sizeof(mapCount));
    CHECK_SDO_RESULT(result, "Failed to enable PDO mapping");

    return true;
}

// 获取PDO数据
PDOData CANopenMaster::getPDOData(uint8_t nodeId) {
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        return it->second;
    }
    return PDOData();
}

// 创建PDO映射条目
uint32_t CANopenMaster::createPDOMapping(uint16_t index, uint8_t subIndex, uint8_t length) {
    return (static_cast<uint32_t>(index) << 16) |
           (static_cast<uint32_t>(subIndex) << 8) |
           static_cast<uint32_t>(length);
}

// 注册PDO回调函数
void CANopenMaster::registerPDOCallback(PDOType pdoType, uint8_t pdoNum, PDOCallback callback) {
    if (!checkPDOIndex(pdoNum)) {
        LOG_ERROR_MSG(domain_, "PDO编号必须在1到%d之间!", OD_CNT_RPDO);
        return;
    }

    std::lock_guard<std::mutex> lock(pdoCallbackMutex);
    pdoCallbacks[std::make_pair(pdoType, pdoNum)] = std::move(callback);

    // 如果是RPDO，我们需要在CANopen库中注册回调
    if (pdoType == PDOType::RPDO && CO != nullptr && CO->RPDO != nullptr) {
        CO_RPDO_initCallbackPre(CO->RPDO + pdoNum - 1, CO->RPDO + pdoNum -1, rpdoCallback);
    }
}

// 取消注册PDO回调函数
void CANopenMaster::unregisterPDOCallback(PDOType pdoType, uint8_t pdoNum) {
    if (!checkPDOIndex(pdoNum)) {
        LOG_ERROR_MSG(domain_, "PDO编号必须在1到%d之间!", OD_CNT_RPDO);
        return;
    }

    std::lock_guard<std::mutex> lock(pdoCallbackMutex);
    pdoCallbacks.erase(std::make_pair(pdoType, pdoNum));

    // 如果是RPDO，我们需要在CANopen库中取消注册回调
    if (pdoType == PDOType::RPDO && CO != nullptr && CO->RPDO != nullptr) {
        // 在CANopenNode中取消回调注册
        CO_RPDO_initCallbackPre(CO->RPDO + pdoNum - 1, nullptr, nullptr);
    }
}

// 根据子站配置的TPDO来配置主站的RPDO（本地）
bool CANopenMaster::configLocalRPDO(uint8_t nodeId, uint8_t pdoIndex, const PDOConfig &config) {
    std::cout << "开始配置主站RPDO" << static_cast<int>(pdoIndex)
              << "，对应子节点" << static_cast<int>(nodeId) << "的TPDO" << static_cast<int>(pdoIndex) << std::endl;

    // 计算对应的COB-ID (与子节点的TPDO相同)
    uint32_t cobId = 0x180 + (pdoIndex - 1) * 0x100 + nodeId;
    // 获取对象字典条目
    OD_entry_t* rpdoCommEntry = OD_ENTRY_H1400_RPDOCommunicationParameter + (pdoIndex - 1);
    OD_entry_t* rpdoMapEntry = OD_ENTRY_H1600_RPDOMappingParameter + (pdoIndex - 1);

    if (rpdoCommEntry == nullptr || rpdoMapEntry == nullptr) {
        std::cerr << "错误: 找不到RPDO对象字典条目" << std::endl;
        return false;
    }

    // 1. 禁用RPDO映射（映射条目数设为0）
    uint8_t mapCount = 0;
    ODR_t odRet = OD_set_u8(rpdoMapEntry, 0, mapCount, false);
    if (odRet != ODR_OK) {
        std::cerr << "错误: 禁用主站RPDO映射失败，错误码: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    // 2. 设置COB-ID（先禁用PDO，bit31=1）
    uint32_t cobIdDisabled = cobId | 0x80000000;
    odRet = OD_set_u32(rpdoCommEntry, 1, cobIdDisabled, false);
    if (odRet != ODR_OK) {
        std::cerr << "错误: 设置主站RPDO COB-ID(禁用)失败，错误码: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    // 3. 设置传输类型
    odRet = OD_set_u8(rpdoCommEntry, 2, config.transmissionType, false);
    if (odRet != ODR_OK) {
        std::cerr << "错误: 设置主站RPDO传输类型失败，错误码: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    // 4. 配置RPDO映射条目
    for (size_t i = 0; i < config.mappingEntries.size() && i < 8; i++) {
        const auto &entry = config.mappingEntries[i];
        uint32_t mapping = createPDOMapping(entry.index, entry.subIndex, entry.length);

        odRet = OD_set_u32(rpdoMapEntry, i + 1, mapping, false);
        if (odRet != ODR_OK) {
            std::cerr << "错误: 设置主站RPDO映射条目" << (i+1) << "失败，错误码: " << static_cast<int>(odRet) << std::endl;
            return false;
        }
    }

    // 5. 启用RPDO映射
    mapCount = static_cast<uint8_t>(std::min(config.mappingEntries.size(), size_t(8)));
    odRet = OD_set_u8(rpdoMapEntry, 0, mapCount, false);
    if (odRet != ODR_OK) {
        std::cerr << "错误: 启用主站RPDO映射失败，错误码: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    // 6. 最后启用PDO（bit31=0）
    // 这一步会触发CANopenNode的自动重配置机制
    odRet = OD_set_u32(rpdoCommEntry, 1, cobId, false);
    if (odRet != ODR_OK) {
        std::cerr << "错误: 启用主站RPDO失败，错误码: " << static_cast<int>(odRet) << std::endl;
        return false;
    }

    return true;
}

bool CANopenMaster::setProfileVelocity(uint8_t nodeId, uint32_t velocity) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_PROFILE_VELOCITY, 0,
                                         reinterpret_cast<uint8_t *>(&velocity),
                                         sizeof(velocity));

    CHECK_SDO_RESULT(result, "Failed to set profile velocity");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.profileVelocity = velocity;
    }

    return true;
}

uint32_t CANopenMaster::getProfileVelocity(uint8_t nodeId) {
    uint32_t velocity = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_PROFILE_VELOCITY, 0,
                                        reinterpret_cast<uint8_t *>(&velocity),
                                        sizeof(velocity), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(velocity)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.profileVelocity = velocity;
        }
    }

    return velocity;
}

bool CANopenMaster::setQuickStopDeceleration(uint8_t nodeId, uint32_t deceleration) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_QUICK_STOP_DECELERATION, 0,
                                         reinterpret_cast<uint8_t *>(&deceleration),
                                         sizeof(deceleration));

    CHECK_SDO_RESULT(result, "Failed to set quick stop deceleration");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.quickStopDeceleration = deceleration;
    }

    return true;
}

uint32_t CANopenMaster::getQuickStopDeceleration(uint8_t nodeId) {
    uint32_t deceleration = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_QUICK_STOP_DECELERATION, 0,
                                        reinterpret_cast<uint8_t *>(&deceleration),
                                        sizeof(deceleration), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(deceleration)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.quickStopDeceleration = deceleration;
        }
    }

    return deceleration;
}

bool CANopenMaster::setMotionProfileType(uint8_t nodeId, uint16_t profileType) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_MOTION_PROFILE_TYPE, 0,
                                         reinterpret_cast<uint8_t *>(&profileType),
                                         sizeof(profileType));

    CHECK_SDO_RESULT(result, "Failed to set motion profile type");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.motionProfileType = profileType;
    }

    return true;
}

uint16_t CANopenMaster::getMotionProfileType(uint8_t nodeId) {
    uint16_t profileType = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_MOTION_PROFILE_TYPE, 0,
                                        reinterpret_cast<uint8_t *>(&profileType),
                                        sizeof(profileType), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(profileType)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.motionProfileType = profileType;
        }
    }

    return profileType;
}

bool CANopenMaster::setPositionTrackingWindow(uint8_t nodeId, uint32_t window) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_POSITION_TRACKING_WINDOW, 0,
                                         reinterpret_cast<uint8_t *>(&window),
                                         sizeof(window));

    CHECK_SDO_RESULT(result, "Failed to set position tracking window");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.positionTrackingWindow = window;
    }

    return true;
}

uint32_t CANopenMaster::getPositionTrackingWindow(uint8_t nodeId) {
    uint32_t window = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_POSITION_TRACKING_WINDOW, 0,
                                        reinterpret_cast<uint8_t *>(&window),
                                        sizeof(window), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(window)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.positionTrackingWindow = window;
        }
    }

    return window;
}

bool CANopenMaster::setPositionTrackingWindowTime(uint8_t nodeId, uint16_t time) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_POSITION_TRACKING_WINDOW_TIME, 0,
                                         reinterpret_cast<uint8_t *>(&time),
                                         sizeof(time));

    CHECK_SDO_RESULT(result, "Failed to set position tracking window time");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.positionTrackingWindowTime = time;
    }

    return true;
}

uint16_t CANopenMaster::getPositionTrackingWindowTime(uint8_t nodeId) {
    uint16_t time = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_POSITION_TRACKING_WINDOW_TIME, 0,
                                        reinterpret_cast<uint8_t *>(&time),
                                        sizeof(time), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(time)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.positionTrackingWindowTime = time;
        }
    }

    return time;
}

bool CANopenMaster::setTouchProbeFunction(uint8_t nodeId, uint16_t function) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_TOUCH_PROBE_FUNCTION, 0,
                                         reinterpret_cast<uint8_t *>(&function),
                                         sizeof(function));

    CHECK_SDO_RESULT(result, "Failed to set touch probe function");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.touchProbeFunction = function;
    }

    return true;
}

uint16_t CANopenMaster::getTouchProbeFunction(uint8_t nodeId) {
    uint16_t function = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_TOUCH_PROBE_FUNCTION, 0,
                                        reinterpret_cast<uint8_t *>(&function),
                                        sizeof(function), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(function)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.touchProbeFunction = function;
        }
    }

    return function;
}

bool CANopenMaster::setTouchProbeSelect(uint8_t nodeId, uint8_t touchProbeIndex, int16_t ioSelect) {
    if (touchProbeIndex < 1 || touchProbeIndex > 2) {
        std::cerr << "Error: Invalid touch probe index (must be 1 or 2)" << std::endl;
        return false;
    }

    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_TOUCH_PROBE_SELECT, touchProbeIndex,
                                         reinterpret_cast<uint8_t *>(&ioSelect),
                                         sizeof(ioSelect));

    CHECK_SDO_RESULT(result, "Failed to set touch probe select");

    return true;
}

int16_t CANopenMaster::getTouchProbeSelect(uint8_t nodeId, uint8_t touchProbeIndex) {
    if (touchProbeIndex < 1 || touchProbeIndex > 2) {
        std::cerr << "Error: Invalid touch probe index (must be 1 or 2)" << std::endl;
        return 0;
    }

    int16_t ioSelect = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_TOUCH_PROBE_SELECT, 0,
                                        reinterpret_cast<uint8_t *>(&ioSelect),
                                        sizeof(ioSelect), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(ioSelect)) {
        return 0;
    }

    return ioSelect;
}

bool CANopenMaster::setInputPinDebounceValues(uint8_t nodeId, uint8_t pinIndex, uint16_t values) {
    if (pinIndex < 1 || pinIndex > 16) {
        std::cerr << "Error: Invalid pin index (must be 1-16)" << std::endl;
        return false;
    }

    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_INPUT_PIN_DEBOUNCE_VALUES, pinIndex,
                                         reinterpret_cast<uint8_t *>(&values),
                                         sizeof(values));

    CHECK_SDO_RESULT(result, "Failed to set input pin debounce values");

    return true;
}

uint16_t CANopenMaster::getInputPinDebounceValues(uint8_t nodeId, uint8_t pinIndex) {
    if (pinIndex < 1 || pinIndex > 16) {
        std::cerr << "Error: Invalid pin index (must be 1-16)" << std::endl;
        return 0;
    }

    uint32_t values = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_INPUT_PIN_DEBOUNCE_VALUES, pinIndex,
                                        reinterpret_cast<uint8_t *>(&values),
                                        sizeof(values), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(values)) {
        return 0;
    }

    return values;
}

bool CANopenMaster::setInputPinConfiguration(uint8_t nodeId, uint8_t pinIndex, uint16_t configuration) {
    if (pinIndex < 1 || pinIndex > 16) {
        std::cerr << "Error: Invalid pin index (must be 1-16)" << std::endl;
        return false;
    }

    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_PIN_CONFIGURATION, pinIndex,
                                         reinterpret_cast<uint8_t *>(&configuration),
                                         sizeof(configuration));

    CHECK_SDO_RESULT(result, "Failed to set input pin configuration");

    return true;
}

uint16_t CANopenMaster::getInputPinConfiguration(uint8_t nodeId, uint8_t pinIndex) {
    if (pinIndex < 1 || pinIndex > 16) {
        std::cerr << "Error: Invalid pin index (must be 1-16)" << std::endl;
        return 0;
    }

    uint16_t configuration = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_PIN_CONFIGURATION, pinIndex,
                                        reinterpret_cast<uint8_t *>(&configuration),
                                        sizeof(configuration), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(configuration)) {
        return 0;
    }

    return configuration;
}

bool CANopenMaster::setPhasingMode(uint8_t nodeId, uint16_t mode) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_PHASING_MODE, 0,
                                         reinterpret_cast<uint8_t *>(&mode),
                                         sizeof(mode));

    CHECK_SDO_RESULT(result, "Failed to set phasing mode");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.phasingMode = mode;
    }

    return true;
}

uint16_t CANopenMaster::getPhasingMode(uint8_t nodeId) {
    uint16_t mode = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_PHASING_MODE, 0,
                                        reinterpret_cast<uint8_t *>(&mode),
                                        sizeof(mode), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(mode)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.phasingMode = mode;
        }
    }

    return mode;
}

bool CANopenMaster::setFaultMask(uint8_t nodeId, uint32_t mask) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_FAULT_MASK, 0,
                                         reinterpret_cast<uint8_t *>(&mask),
                                         sizeof(mask));

    CHECK_SDO_RESULT(result, "Failed to set fault mask");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.faultMask = mask;
    }

    return true;
}

uint32_t CANopenMaster::getFaultMask(uint8_t nodeId) {
    uint32_t mask = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_FAULT_MASK, 0,
                                        reinterpret_cast<uint8_t *>(&mask),
                                        sizeof(mask), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(mask)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.faultMask = mask;
        }
    }

    return mask;
}

bool CANopenMaster::setHomingMethod(uint8_t nodeId, int8_t method) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_HOMING_METHOD, 0,
                                         reinterpret_cast<uint8_t *>(&method),
                                         sizeof(method));

    CHECK_SDO_RESULT(result, "Failed to set homing method");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.homingMethod = method;
    }

    return true;
}

int8_t CANopenMaster::getHomingMethod(uint8_t nodeId) {
    int8_t method = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_HOMING_METHOD, 0,
                                        reinterpret_cast<uint8_t *>(&method),
                                        sizeof(method), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(method)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.homingMethod = method;
        }
    }

    return method;
}

bool CANopenMaster::setHomingSpeeds(uint8_t nodeId, uint8_t subIndex, uint32_t speed) {
    if (subIndex < 1 || subIndex > 2) {
        std::cerr << "Error: Invalid subIndex for homing speeds (must be 1 or 2)" << std::endl;
        return false;
    }

    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_HOMING_SPEEDS, subIndex,
                                         reinterpret_cast<uint8_t *>(&speed),
                                         sizeof(speed));

    CHECK_SDO_RESULT(result, "Failed to set homing speeds");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.homingSpeeds[subIndex - 1] = speed;
    }

    return true;
}

uint32_t CANopenMaster::getHomingSpeeds(uint8_t nodeId, uint8_t subIndex) {
    if (subIndex < 1 || subIndex > 2) {
        std::cerr << "Error: Invalid subIndex for homing speeds (must be 1 or 2)" << std::endl;
        return 0;
    }

    uint32_t speed = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_HOMING_SPEEDS, subIndex,
                                        reinterpret_cast<uint8_t *>(&speed),
                                        sizeof(speed), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(speed)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.homingSpeeds[subIndex - 1] = speed;
        }
    }

    return speed;
}

bool CANopenMaster::setHomingAcceleration(uint8_t nodeId, uint32_t acceleration) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_HOMING_ACCELERATION, 0,
                                         reinterpret_cast<uint8_t *>(&acceleration),
                                         sizeof(acceleration));

    CHECK_SDO_RESULT(result, "Failed to set homing acceleration");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.homingAcceleration = acceleration;
    }

    return true;
}

uint32_t CANopenMaster::getHomingAcceleration(uint8_t nodeId) {
    uint32_t acceleration = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_HOMING_ACCELERATION, 0,
                                        reinterpret_cast<uint8_t *>(&acceleration),
                                        sizeof(acceleration), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(acceleration)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.homingAcceleration = acceleration;
        }
    }

    return acceleration;
}

bool CANopenMaster::setHomingOffset(uint8_t nodeId, int32_t offset) {
    CO_SDO_abortCode_t result = writeSDO(nodeId, OD_HOMING_OFFSET, 0,
                                         reinterpret_cast<uint8_t *>(&offset),
                                         sizeof(offset));

    CHECK_SDO_RESULT(result, "Failed to set homing offset");

    // 更新本地数据
    std::lock_guard<std::mutex> lock(nodeDataMutex);
    auto it = nodeDataMap.find(nodeId);
    if (it != nodeDataMap.end()) {
        it->second.homingOffset = offset;
    }

    return true;
}

int32_t CANopenMaster::getHomingOffset(uint8_t nodeId) {
    int32_t offset = 0;
    size_t readSize = 0;

    CO_SDO_abortCode_t result = readSDO(nodeId, OD_HOMING_OFFSET, 0,
                                        reinterpret_cast<uint8_t *>(&offset),
                                        sizeof(offset), &readSize);

    if (result != CO_SDO_AB_NONE || readSize != sizeof(offset)) {
        return 0;
    }

    // 更新本地数据
    {
        std::lock_guard<std::mutex> lock(nodeDataMutex);
        auto it = nodeDataMap.find(nodeId);
        if (it != nodeDataMap.end()) {
            it->second.homingOffset = offset;
        }
    }

    return offset;
}

bool CANopenMaster::checkPDOIndex(uint8_t pdoNum) {
    return pdoNum >= 1 && pdoNum <= OD_CNT_RPDO;
}
