#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <thread>
#include <chrono>
#include <vector>
#include <map>
#include <string>
#include <iostream>
#include <iomanip>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <atomic>
#include <future>
#include <condition_variable>
#include <mutex>

#include "GrpcServer.hpp"
#include "ServoControlManager.hpp"
#include "motor_control.grpc.pb.h"
#include "MockCANopenMaster.h"
#include "MockServoControlManager.h"
#include "SyslogManager.hpp"

// 详细时间统计结构
struct DetailedTimingStats {
    double servo_processing_time_ms = 0.0;      // 伺服处理时间
    double motion_completion_time_ms = 0.0;     // 运动完成时间
    double response_transmission_time_ms = 0.0; // 响应传输时间
    double total_end_to_end_time_ms = 0.0;      // 端到端总时间
};

// 性能统计结构
struct PerformanceStats {
    std::vector<double> latencies_ms;
    double p50 = 0.0;
    double p95 = 0.0;
    double p99 = 0.0;
    double avg = 0.0;
    double max = 0.0;
    double min = 0.0;
    double std_deviation = 0.0;
    size_t total_requests = 0;
    size_t error_count = 0;
    double error_rate = 0.0;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;

    // 新增字段用于支持文档场景测试
    int background_load_clients = 0;            // 背景负载客户端数量
    std::string motion_mode = "";               // 运动模式
    DetailedTimingStats detailed_timing;        // 详细时间分解

    double getDurationSeconds() const {
        return std::chrono::duration<double>(end_time - start_time).count();
    }
};

/**
 * @brief 背景负载模拟器
 *
 * 模拟多客户端以指定频率持续获取系统状态，用于测试在背景负载下的接口性能
 */
class BackgroundLoadSimulator {
public:
    BackgroundLoadSimulator(const std::string &server_address)
            : server_address_(server_address), running_(false) {}

    ~BackgroundLoadSimulator() {
        stopBackgroundLoad();
    }

    // 启动背景负载
    void startBackgroundLoad(int client_count, int frequency_ms) {
        if (running_.load()) {
            return; // 已经在运行
        }

        client_count_ = client_count;
        frequency_ms_ = frequency_ms;
        running_.store(true);

        // 重置统计数据
        total_requests_.store(0);
        error_count_.store(0);
        latencies_.clear();

        std::cout << "[INFO] 启动背景负载: " << client_count << " 个客户端, "
                  << frequency_ms << "ms 频率" << std::endl;

        // 创建客户端线程
        for (int i = 0; i < client_count; ++i) {
            client_threads_.emplace_back([this, i]() {
                runBackgroundClient(i);
            });
        }
    }

    // 停止背景负载
    void stopBackgroundLoad() {
        if (!running_.load()) {
            return;
        }

        running_.store(false);

        // 等待所有客户端线程结束
        for (auto &thread: client_threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        client_threads_.clear();

        std::cout << "[INFO] 背景负载已停止" << std::endl;
    }

    // 获取背景负载统计
    PerformanceStats getBackgroundLoadStats() {
        PerformanceStats stats;

        {
            std::lock_guard<std::mutex> lock(stats_mutex_);
            stats.latencies_ms = latencies_;
        }

        stats.total_requests = total_requests_.load();
        stats.error_count = error_count_.load();
        stats.error_rate = stats.total_requests > 0 ?
                           static_cast<double>(stats.error_count) / stats.total_requests : 0.0;
        stats.background_load_clients = client_count_;

        return stats;
    }

private:
    void runBackgroundClient(int client_id) {
        // 为每个客户端创建独立连接
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        auto stub = motor_control::BedMasterAppService::NewStub(channel);

        while (running_.load()) {
            auto start_time = std::chrono::high_resolution_clock::now();

            // 调用GetSystemStatusInfo接口
            grpc::ClientContext context;
            context.AddMetadata("client-type", "BACKGROUND_LOAD");
            context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(1));

            motor_control::CommonDescription request;
            motor_control::SystemStatusInfoStatus response;
            request.set_contextuid("bg-load-" + std::to_string(client_id) + "-" +
                                   std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

            grpc::Status status = stub->GetSystemStatusInfo(&context, request, &response);

            auto end_time = std::chrono::high_resolution_clock::now();
            double latency_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();

            // 更新统计数据
            {
                std::lock_guard<std::mutex> lock(stats_mutex_);
                latencies_.push_back(latency_ms);
            }

            total_requests_++;
            if (!status.ok()) {
                error_count_++;
            }

            // 按指定频率休眠
            std::this_thread::sleep_for(std::chrono::milliseconds(frequency_ms_));
        }
    }

    std::string server_address_;
    std::atomic<bool> running_;
    std::vector<std::thread> client_threads_;
    int client_count_ = 0;
    int frequency_ms_ = 10;

    // 统计数据
    std::atomic<size_t> total_requests_{0};
    std::atomic<size_t> error_count_{0};
    std::vector<double> latencies_;
    std::mutex stats_mutex_;
};

/**
 * @brief GRPC通信性能测试类
 *
 * 基于现有GrpcServerTest扩展，专注于通信层面的性能测试
 * 包含原有测试和新增的文档场景测试
 */
class GrpcCommunicationPerformanceTest : public ::testing::Test {
protected:

    void SetUp() override {
        // 设置日志
//        setLogger("grpc_communication_performance_test");

        // 创建模拟的CANopenMaster和ServoControlManager
        mock_canopen_master_ = std::make_unique<MockCANopenMaster>("vcan0");
        auto nice_mock = std::make_unique<::testing::NiceMock<MockServoControlManager>>(mock_canopen_master_.get());
        mock_servo_manager_ = std::move(nice_mock);

        // 配置Mock行为以支持性能测试
        configureMockForPerformanceTest();

        // 创建GRPC服务器
        server_address_ = "localhost:50051";
        grpc_server_ = std::make_unique<GrpcServer>(server_address_, mock_servo_manager_.get());

        // 启动服务器
        ASSERT_TRUE(grpc_server_->start());

        // 等待服务器启动
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        // 创建客户端
        auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
        stub_ = motor_control::BedMasterAppService::NewStub(channel);

        // 创建背景负载模拟器
        background_load_simulator_ = std::make_unique<BackgroundLoadSimulator>(server_address_);

        std::cout << "[INFO] GRPC通信性能测试环境初始化完成" << std::endl;
    }

    void TearDown() override {
        // 确保背景负载被停止
        if (background_load_simulator_) {
            background_load_simulator_->stopBackgroundLoad();
        }

        grpc_server_->stop();

        // 保存所有测试结果
        saveAllResultsToCsv();
        printFinalSummaryReport();

        std::cout << "[INFO] 性能测试结果已保存:" << std::endl;
        std::cout << "  - performance_results.csv (易读格式，数据项与值对应)" << std::endl;
        std::cout << "  - performance_results_table.csv (表格格式，便于数据分析)" << std::endl;
    }

    // 配置Mock行为以支持性能测试
    void configureMockForPerformanceTest() {
        using ::testing::_;
        using ::testing::Return;
        using ::testing::Invoke;

        // 配置增强的Mock行为，支持详细时间测量
        ON_CALL(*mock_servo_manager_, moveToPosition(_, _, _, _, _))
                .WillByDefault(Invoke([this](uint8_t nodeId, int32_t position, int32_t velocity, bool absolute,
                                             bool immediate) -> bool {
                    auto start_time = std::chrono::high_resolution_clock::now();

                    // 模拟位置模式的处理延迟
                    std::this_thread::sleep_for(std::chrono::microseconds(100));

                    auto end_time = std::chrono::high_resolution_clock::now();
                    recordDetailedTiming("PositionMode", start_time, end_time);

                    return true;
                }));

        ON_CALL(*mock_servo_manager_, moveWithVelocity(_, _, _))
                .WillByDefault(Invoke([this](uint8_t nodeId, int32_t velocity, int32_t position) -> bool {
                    auto start_time = std::chrono::high_resolution_clock::now();

                    // 模拟速度模式的处理延迟
                    std::this_thread::sleep_for(std::chrono::microseconds(100));

                    auto end_time = std::chrono::high_resolution_clock::now();
                    recordDetailedTiming("VelocityMode", start_time, end_time);

                    return true;
                }));

        ON_CALL(*mock_servo_manager_, moveWithHoming(_))
                .WillByDefault(Invoke([this](uint8_t nodeId) -> bool {
                    auto start_time = std::chrono::high_resolution_clock::now();

                    // 模拟回零模式的处理延迟
                    std::this_thread::sleep_for(std::chrono::microseconds(100));

                    auto end_time = std::chrono::high_resolution_clock::now();
                    recordDetailedTiming("HomingMode", start_time, end_time);

                    return true;
                }));

        // ON_CALL(*mock_servo_manager_, stopMoveBed(_))
        //     .WillByDefault(Invoke([](BedType bedType) -> bool {
        //         std::this_thread::sleep_for(std::chrono::microseconds(30));
        //         return true;
        //     }));

        ON_CALL(*mock_servo_manager_, getBedStatus(_))
                .WillByDefault(Invoke([](BedType bedType) -> BedStatus {
                    std::this_thread::sleep_for(std::chrono::microseconds(80));
                    BedStatus status{};
                    // status.isHomed = true;
                    // status.isMoving = false;
                    // status.currentPosition = 100.0f;
                    // status.targetPosition = 100.0f;
                    // status.currentVelocity = 0.0f;
                    // status.hasFault = false;
                    return status;
                }));

        ON_CALL(*mock_servo_manager_, getPostId(_))
                .WillByDefault(Invoke([](BedType bedType) -> PostIdInfo {
                    std::this_thread::sleep_for(std::chrono::microseconds(60));
                    PostIdInfo info;
                    info.vid = 0x12345678;
                    info.did = 0x87654321;
                    info.hwid = 0x11223344;
                    info.rid = 0x44332211;
                    return info;
                }));

        // 配置心跳检测为快速响应
        // ON_CALL(*mock_servo_manager_, updateHeartbeat(_))
        //     .WillByDefault(Invoke([](BedOwnerType owner) -> bool {
        //         std::this_thread::sleep_for(std::chrono::microseconds(20));
        //         return true;
        //     }));
        //
        // 配置触发器信息变化 - 支持服务端推送流模式
        ON_CALL(*mock_servo_manager_, waitForTriggerInfoChange(_, _))
                .WillByDefault(Invoke([](TriggerInfo &lastKnownInfo, int timeout_ms) -> bool {
                    static std::atomic<uint64_t> counter{0};
                    counter++;

                    // 模拟触发器信息更新
                    lastKnownInfo.timestamp = counter;
                    lastKnownInfo.triggerTimestamp = lastKnownInfo.timestamp;
                    lastKnownInfo.position = counter;  // 位置递增
                    lastKnownInfo.interval = 50;  // 50ms间隔
                    lastKnownInfo.isValid = true;

                    // 模拟快速的服务端推送，减少延迟以提高性能测试效果
                    std::this_thread::sleep_for(std::chrono::microseconds(1000));
                    return true;
                }));
    }

    // 计算性能统计
    PerformanceStats calculateStats(const std::vector<double> &latencies) {
        PerformanceStats stats;
        stats.latencies_ms = latencies;
        stats.total_requests = latencies.size();

        if (latencies.empty()) {
            return stats;
        }

        // 排序以计算百分位数
        std::vector<double> sorted_latencies = latencies;
        std::sort(sorted_latencies.begin(), sorted_latencies.end());

        // 计算百分位数
        stats.p50 = calculatePercentile(sorted_latencies, 0.50);
        stats.p95 = calculatePercentile(sorted_latencies, 0.95);
        stats.p99 = calculatePercentile(sorted_latencies, 0.99);

        // 计算基本统计
        stats.min = sorted_latencies.front();
        stats.max = sorted_latencies.back();
        stats.avg = std::accumulate(latencies.begin(), latencies.end(), 0.0) / latencies.size();

        // 计算标准差
        double variance = 0.0;
        for (double latency: latencies) {
            variance += std::pow(latency - stats.avg, 2);
        }
        stats.std_deviation = std::sqrt(variance / latencies.size());

        return stats;
    }

    // 计算百分位数
    double calculatePercentile(const std::vector<double> &sorted_values, double percentile) {
        if (sorted_values.empty()) return 0.0;

        double index = percentile * (sorted_values.size() - 1);
        size_t lower_index = static_cast<size_t>(std::floor(index));
        size_t upper_index = static_cast<size_t>(std::ceil(index));

        if (lower_index == upper_index) {
            return sorted_values[lower_index];
        }

        double weight = index - lower_index;
        return sorted_values[lower_index] * (1.0 - weight) + sorted_values[upper_index] * weight;
    }

    // 保存结果到CSV文件 - 改进的易读格式
    void saveResultsToCsv(const std::string &test_name, const PerformanceStats &stats,
                          int concurrent_clients = 1) {
        // 生成易读的CSV格式：每个测试用例一个独立的数据块
        std::ofstream file("performance_results.csv", std::ios::app);

        // 写入测试用例分隔符和标题
        file << "\n=== " << test_name << " ===\n";
        file << "指标项,数值,单位\n";

        // 基本测试信息
        file << "测试名称," << test_name << ",\n";
        file << "并发客户端数," << concurrent_clients << ",个\n";
        file << "总请求数," << stats.total_requests << ",个\n";
        file << "测试持续时间," << std::fixed << std::setprecision(2) << stats.getDurationSeconds() << ",秒\n";

        // 延迟性能指标
        file << "\n--- 延迟性能指标 ---\n";
        file << "P50延迟," << std::fixed << std::setprecision(2) << stats.p50 << ",ms\n";
        file << "P95延迟," << stats.p95 << ",ms\n";
        file << "P99延迟," << stats.p99 << ",ms\n";
        file << "平均延迟," << stats.avg << ",ms\n";
        file << "最大延迟," << stats.max << ",ms\n";
        file << "最小延迟," << stats.min << ",ms\n";
        file << "延迟标准差," << stats.std_deviation << ",ms\n";

        // 错误率指标
        file << "\n--- 错误率指标 ---\n";
        file << "错误数量," << stats.error_count << ",个\n";
        file << "错误率," << std::fixed << std::setprecision(4) << (stats.error_rate * 100) << ",%\n";
        file << "成功率," << std::fixed << std::setprecision(4) << ((1.0 - stats.error_rate) * 100) << ",%\n";

        // 吞吐量指标
        if (stats.getDurationSeconds() > 0) {
            double qps = static_cast<double>(stats.total_requests) / stats.getDurationSeconds();
            file << "\n--- 吞吐量指标 ---\n";
            file << "QPS(每秒请求数)," << std::fixed << std::setprecision(1) << qps << ",req/s\n";
            file << "平均请求间隔," << std::fixed << std::setprecision(2) << (1000.0 / qps) << ",ms\n";
        }

        // 文档场景特定指标
        if (stats.background_load_clients > 0 || !stats.motion_mode.empty()) {
            file << "\n--- 文档场景指标 ---\n";
            if (stats.background_load_clients > 0) {
                file << "背景负载客户端数," << stats.background_load_clients << ",个\n";
            }
            if (!stats.motion_mode.empty()) {
                file << "运动模式," << stats.motion_mode << ",\n";
            }
        }

        // 详细时间分解
        if (stats.detailed_timing.total_end_to_end_time_ms > 0) {
            file << "\n--- 详细时间分解 ---\n";
            file << "伺服处理时间," << std::fixed << std::setprecision(3)
                 << stats.detailed_timing.servo_processing_time_ms << ",ms\n";
            file << "运动完成时间," << stats.detailed_timing.motion_completion_time_ms << ",ms\n";
            file << "端到端总时间," << stats.detailed_timing.total_end_to_end_time_ms << ",ms\n";
        }

        file << "\n";  // 添加空行分隔不同测试用例
        file.close();
    }

    // 打印测试进度
    void printProgress(const std::string &test_name, int current, int total) {
        int progress = (current * 100) / total;
        int bar_width = 40;
        int filled = (progress * bar_width) / 100;

        std::cout << "\r[PROGRESS] " << test_name << " ";
        std::cout << "[";
        for (int i = 0; i < bar_width; ++i) {
            if (i < filled) std::cout << "█";
            else std::cout << " ";
        }
        std::cout << "] " << current << "/" << total << " (" << progress << "%)";
        std::cout.flush();

        if (current == total) {
            std::cout << std::endl;
        }
    }

    // 测试流式接口性能
    PerformanceStats testStreamPerformance(int concurrent_streams, int messages_per_stream, int duration_seconds);

    // 测试单个接口的延迟性能
    PerformanceStats
    measureInterfaceConcurrentLatency(const std::string &interface_name, int request_count, int concurrent_clients = 1);

    // 测试StartMove接口的延迟性能（支持不同运动模式）
    PerformanceStats measureInterfaceLatency(const std::string &interface_name, int request_count);

    // 测试StartMove接口的并发延迟性能（单客户端异步并发）
    PerformanceStats measureStartMoveConcurrentLatency(const std::string &motion_mode, int concurrent_requests, int total_requests);

    // 测试GetTriggerInfo接口的延迟性能
    PerformanceStats measureGetTriggerInfoLatency(int messages_per_stream, int duration_seconds);

    // 测试GetSystemStatusInfo接口的并发性能
    PerformanceStats measureGetSystemStatusInfoConcurrency(int concurrent_clients, int requests_per_client);
    
    // 调用指定的接口
    bool callInterface(const std::string &interface_name, motor_control::BedMasterAppService::Stub *stub);

    // 记录详细时间测量
    void recordDetailedTiming(const std::string &motion_mode,
                              std::chrono::high_resolution_clock::time_point start_time,
                              std::chrono::high_resolution_clock::time_point end_time);

    // 获取当前详细时间统计
    DetailedTimingStats getCurrentDetailedTiming();

    // 成员变量
    std::unique_ptr<MockCANopenMaster> mock_canopen_master_;
    std::unique_ptr<MockServoControlManager> mock_servo_manager_;
    std::unique_ptr<GrpcServer> grpc_server_;
    std::unique_ptr<motor_control::BedMasterAppService::Stub> stub_;
    std::string server_address_;

    // 背景负载模拟器
    std::unique_ptr<BackgroundLoadSimulator> background_load_simulator_;

    // 详细时间测量相关
    std::mutex timing_mutex_;
    DetailedTimingStats current_detailed_timing_;

    // 存储所有测试结果
    std::map<std::string, PerformanceStats> all_test_results_;

private:
    void saveAllResultsToCsv();

    void printFinalSummaryReport();
};

PerformanceStats
GrpcCommunicationPerformanceTest::testStreamPerformance(int concurrent_streams, int messages_per_stream,
                                                        int duration_seconds) {

    std::mutex metrics_mutex;
    std::vector<double> stream_setup_times;
    std::vector<double> message_latencies;
    std::atomic<int> total_messages{0};
    std::atomic<int> error_count{0};
    std::atomic<bool> test_running{true};

    auto test_start = std::chrono::steady_clock::now();

    // 创建并发流任务
    std::vector<std::future<void>> stream_futures;

    for (int stream_id = 0; stream_id < concurrent_streams; ++stream_id) {
        stream_futures.push_back(std::async(std::launch::async,
                                            [=, &metrics_mutex, &stream_setup_times, &message_latencies, &total_messages, &error_count, &test_running]() {
                                                try {
                                                    // 创建独立的客户端连接
                                                    auto channel = grpc::CreateChannel(server_address_,
                                                                                       grpc::InsecureChannelCredentials());
                                                    auto client_stub = motor_control::BedMasterAppService::NewStub(
                                                            channel);

                                                    grpc::ClientContext context;
                                                    context.AddMetadata("client-type", "NONE");

                                                    // 建立流连接
                                                    auto stream_start = std::chrono::high_resolution_clock::now();
                                                    auto stream = client_stub->GetTriggerInfo(&context);

                                                    auto establish_end = std::chrono::high_resolution_clock::now();
                                                    auto establishment_time_ms = std::chrono::duration<double, std::milli>(
                                                            establish_end - stream_start).count();
                                                    {
                                                        std::lock_guard<std::mutex> lock(metrics_mutex);
                                                        stream_setup_times.push_back(establishment_time_ms);
                                                    }

                                                    // 进入消息接收循环（被动接收服务端推送）
                                                    motor_control::TriggerInfoStatus response;
                                                    int messages_received = 0;

                                                    while (test_running.load() &&
                                                           (messages_received < messages_per_stream)) {
                                                        auto read_start = std::chrono::high_resolution_clock::now();

                                                        if (stream->Read(&response)) {

                                                            auto read_end = std::chrono::high_resolution_clock::now();

                                                            // 记录消息接收延迟（从开始读取到接收完成的时间）
                                                            auto latency_ms = std::chrono::duration<double, std::milli>(
                                                                    read_end - read_start).count();

                                                            {
                                                                std::lock_guard<std::mutex> lock(metrics_mutex);
                                                                message_latencies.push_back(latency_ms);
                                                            }

                                                            messages_received++;
                                                            total_messages++;


                                                        } else {
                                                            // 读取失败，可能是流结束或错误
                                                            if (test_running.load()) {
                                                                error_count++;
                                                                std::cerr << "[ERROR] 流 " << stream_id << " 读取消息失败"
                                                                          << std::endl;
                                                            }
                                                            break;
                                                        }
                                                    }

                                                    // 发送结束请求
                                                    stream->WritesDone();

                                                    // 关键：继续读取服务端可能还在发送的数据
                                                    while (stream->Read(&response)) {
                                                        total_messages++;
                                                        // 读空所有剩余数据，但不处理
                                                        // 这样可以让服务端的Write()不再阻塞
                                                    }

                                                    auto finish_status = stream->Finish();

                                                    if (!finish_status.ok()) {
                                                        error_count++;
                                                        std::cerr << "[ERROR] 流 " << stream_id << " 结束时出错: "
                                                                  << finish_status.error_message() << std::endl;
                                                    }

                                                    std::cout << "[INFO] 流 " << stream_id << " 完成，接收消息数: "
                                                              << messages_received << std::endl;

                                                } catch (const std::exception &e) {
                                                    error_count++;
                                                    std::cerr << "[ERROR] 流 " << stream_id << " 异常: " << e.what()
                                                              << std::endl;
                                                }
                                            }));
    }

    // 运行指定时间后停止测试
    std::this_thread::sleep_for(std::chrono::seconds(duration_seconds));
    test_running.store(false);

    // 等待所有流完成
    for (auto &future: stream_futures) {
        future.wait();
    }

    auto test_end = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats;
    {
        std::lock_guard<std::mutex> lock(metrics_mutex);
        stats = calculateStats(message_latencies);
    }

    stats.start_time = test_start;
    stats.end_time = test_end;
    stats.error_count = error_count.load();
    stats.total_requests = total_messages.load();

    // 计算错误率
    int expected_total = concurrent_streams * messages_per_stream;
    stats.error_rate = static_cast<double>(stats.error_count) /
                       static_cast<double>(stats.total_requests + stats.error_count);

    // 将流建立时间存储在min字段中（平均流建立时间）
    {
        std::lock_guard<std::mutex> lock(metrics_mutex);
        if (!stream_setup_times.empty()) {
            stats.min = std::accumulate(stream_setup_times.begin(), stream_setup_times.end(), 0.0) /
                        stream_setup_times.size();
        }
    }

    // 输出性能统计信息
    std::cout << "\n[STREAM PERFORMANCE STATS]" << std::endl;
    std::cout << "并发流数量: " << concurrent_streams << std::endl;
    std::cout << "每流期望消息数: " << messages_per_stream << std::endl;
    std::cout << "实际接收消息总数: " << total_messages.load() << std::endl;
    std::cout << "平均流建立时间: " << std::fixed << std::setprecision(2) << stats.min << " ms" << std::endl;
    std::cout << "消息接收延迟 P50: " << stats.p50 << " ms" << std::endl;
    std::cout << "消息接收延迟 P95: " << stats.p95 << " ms" << std::endl;
    std::cout << "消息接收平均延迟: " << stats.avg << " ms" << std::endl;
    std::cout << "错误数量: " << error_count.load() << std::endl;
    std::cout << "错误率: " << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

    // 计算QPS
    double duration_sec = stats.getDurationSeconds();
    if (duration_sec > 0) {
        double qps = static_cast<double>(total_messages.load()) / duration_sec;
        std::cout << "消息接收QPS: " << std::setprecision(1) << qps << std::endl;
    }

    return stats;
}

// 记录详细时间测量
void GrpcCommunicationPerformanceTest::recordDetailedTiming(const std::string &motion_mode,
                                                            std::chrono::high_resolution_clock::time_point start_time,
                                                            std::chrono::high_resolution_clock::time_point end_time) {
    std::lock_guard<std::mutex> lock(timing_mutex_);

    double processing_time_ms = std::chrono::duration<double, std::milli>(end_time - start_time).count();

    current_detailed_timing_.servo_processing_time_ms = processing_time_ms;
    current_detailed_timing_.motion_completion_time_ms = processing_time_ms; // 简化处理，实际中可能需要更复杂的逻辑
    current_detailed_timing_.total_end_to_end_time_ms = processing_time_ms;
}

// 获取当前详细时间统计
DetailedTimingStats GrpcCommunicationPerformanceTest::getCurrentDetailedTiming() {
    std::lock_guard<std::mutex> lock(timing_mutex_);
    return current_detailed_timing_;
}

// 测试接口的并发延迟性能
PerformanceStats
GrpcCommunicationPerformanceTest::measureInterfaceConcurrentLatency(const std::string &interface_name, int request_count,
                                                          int concurrent_clients) {
    std::vector<double> all_latencies;
    std::atomic<int> completed_requests{0};
    std::atomic<int> error_count{0};

    auto start_time = std::chrono::steady_clock::now();

    // 创建并发任务
    std::vector<std::future<std::vector<double>>> futures;
    int requests_per_client = request_count / concurrent_clients;

    for (int client = 0; client < concurrent_clients; ++client) {
        futures.push_back(std::async(std::launch::async, [=, &completed_requests, &error_count]() {
            std::vector<double> client_latencies;

            // 为每个客户端创建独立的连接
            auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
            auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

            for (int i = 0; i < requests_per_client; ++i) {
                auto request_start = std::chrono::high_resolution_clock::now();
                bool success = false;

                try {
                    success = callInterface(interface_name, client_stub.get());
                } catch (const std::exception &e) {
                    error_count++;
                }

                auto request_end = std::chrono::high_resolution_clock::now();

                if (success) {
                    double latency_ms = std::chrono::duration<double, std::milli>(request_end - request_start).count();
                    client_latencies.push_back(latency_ms);
                } else {
                    error_count++;
                }

                completed_requests++;

                // 更新进度（每10个请求更新一次以减少开销）
                if (completed_requests % 10 == 0 || completed_requests == request_count) {
                    printProgress(interface_name, completed_requests, request_count);
                }
            }

            return client_latencies;
        }));
    }

    // 收集所有客户端的结果
    for (auto &future: futures) {
        auto client_latencies = future.get();
        all_latencies.insert(all_latencies.end(), client_latencies.begin(), client_latencies.end());
    }

    auto end_time = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats = calculateStats(all_latencies);
    stats.start_time = start_time;
    stats.end_time = end_time;
    stats.error_count = error_count.load();
    stats.error_rate = static_cast<double>(stats.error_count) / request_count;

    return stats;
}

// 测试接口的串行延迟
PerformanceStats
GrpcCommunicationPerformanceTest::measureInterfaceLatency(const std::string &interface_name, int request_count) {
    std::vector<double> latencies;
    std::atomic<int> completed_requests{0};
    std::atomic<int> error_count{0};

    auto start_time = std::chrono::steady_clock::now();

    for (int i = 0; i < request_count; ++i) {
        auto request_start = std::chrono::high_resolution_clock::now();

        bool success = false;
        try {
            success = callInterface(interface_name, stub_.get());
        } catch (const std::exception &e) {
            error_count++;
        }

        auto request_end = std::chrono::high_resolution_clock::now();

        if (success) {
            double latency_ms = std::chrono::duration<double, std::milli>(request_end - request_start).count();
            latencies.push_back(latency_ms);
        } else {
            error_count++;
        }

        completed_requests++;
    }

    auto end_time = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats = calculateStats(latencies);
    stats.start_time = start_time;
    stats.end_time = end_time;
    stats.error_count = error_count.load();
    stats.error_rate = static_cast<double>(stats.error_count) / request_count;

    return stats;
}

bool GrpcCommunicationPerformanceTest::callInterface(const std::string &interface_name,
                                                     motor_control::BedMasterAppService::Stub *stub) {
    grpc::ClientContext context;
    context.AddMetadata("client-type", "NONE");
    context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(5));

    if (interface_name == "StartMove") {

        motor_control::StartMoveDescription request;
        motor_control::StartMoveStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
        request.set_mode(motor_control::MotionMode::PositionMode);
        request.set_bedtype(motor_control::BedType::Primary);
        auto *motion_info = request.mutable_targetmotioninfo();
        motion_info->set_postion(100.0);
        motion_info->set_velocity(50.0);

        grpc::Status status = stub->StartMove(&context, request, &response);
        return status.ok() && response.errorcode() == 0;

    } else if (interface_name == "StopMove") {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

        grpc::Status status = stub->StopMove(&context, request, &response);
        return status.ok() && response.errorcode() == 0;

    } else if (interface_name == "HeartBeatCheck") {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

        grpc::Status status = stub->HeartBeatCheck(&context, request, &response);
        return status.ok() && response.errorcode() == 0;

    } else if (interface_name == "GetSystemStatusInfo") {
        motor_control::CommonDescription request;
        motor_control::SystemStatusInfoStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

        grpc::Status status = stub->GetSystemStatusInfo(&context, request, &response);
        return status.ok();

    } else if (interface_name == "GainControl") {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

        grpc::Status status = stub->GainControl(&context, request, &response);
        return status.ok() && response.errorcode() == 0;

    } else if (interface_name == "ReleaseControl") {
        motor_control::CommonDescription request;
        motor_control::CommonStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

        grpc::Status status = stub->ReleaseControl(&context, request, &response);
        return status.ok() && response.errorcode() == 0;

    } else if (interface_name == "GetPostId") {
        motor_control::GetPostIdDescription request;
        motor_control::GetPostIdStatus response;
        request.set_contextuid(
                "perf-test-" + std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));
        request.set_bedtype(motor_control::BedType::Primary);

        grpc::Status status = stub->GetPostId(&context, request, &response);
        return status.ok() && response.errorcode() == 0;
    }

    return false;
}

// 测试StartMove接口的并发延迟性能（单客户端异步并发）
PerformanceStats
GrpcCommunicationPerformanceTest::measureStartMoveConcurrentLatency(const std::string &motion_mode, int concurrent_requests, int total_requests) {
    std::vector<double> all_latencies;
    std::atomic<int> completed_requests{0};
    std::atomic<int> error_count{0};
    std::mutex latencies_mutex;

    auto start_time = std::chrono::steady_clock::now();

    // 创建单个客户端连接
    auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
    auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

    // 计算需要执行的批次数
    int batches = total_requests / concurrent_requests;

    std::cout << "[INFO] StartMove单客户端并发测试: " << motion_mode
              << ", 并发数=" << concurrent_requests
              << ", 总请求数=" << total_requests
              << ", 批次数=" << batches << std::endl;

    for (int batch = 0; batch < batches; ++batch) {

        // 创建并发任务
        std::vector<std::future<std::pair<double, bool>>> futures;

        auto batch_start = std::chrono::high_resolution_clock::now();

        for (int i = 0; i < concurrent_requests; ++i) {
            int request_id = batch * concurrent_requests + i;
            futures.push_back(std::async(std::launch::async, [=, &client_stub, &motion_mode]() -> std::pair<double, bool> {
                auto request_start = std::chrono::high_resolution_clock::now();

                bool success = false;
                try {
                    // 构造StartMove请求
                    grpc::ClientContext context;
                    context.AddMetadata("client-type", "NONE");
                    context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(5));

                    motor_control::StartMoveDescription request;
                    motor_control::StartMoveStatus response;

                    request.set_contextuid("startmove-concurrent-" + std::to_string(request_id));

                    // 根据运动模式设置请求参数
                    if (motion_mode == "PositionMode") {
                        request.set_mode(motor_control::MotionMode::PositionMode);
                    } else if (motion_mode == "VelocityMode") {
                        request.set_mode(motor_control::MotionMode::VelocityMode);
                    } else if (motion_mode == "HomingMode") {
                        request.set_mode(motor_control::MotionMode::HomingMode);
                    } else {
                        request.set_mode(motor_control::MotionMode::VelocityMode); // 默认速度模式
                    }

                    request.set_bedtype(motor_control::BedType::Primary);

                    auto *motion_info = request.mutable_targetmotioninfo();
                    motion_info->set_postion(100.0f + request_id); // 变化的位置
                    motion_info->set_velocity(50.0);

                    grpc::Status status = client_stub->StartMove(&context, request, &response);
                    success = status.ok() && response.errorcode() == 0;
                } catch (const std::exception& e) {
                    // 异常处理
                }

                auto request_end = std::chrono::high_resolution_clock::now();
                double latency_ms = std::chrono::duration<double, std::milli>(request_end - request_start).count();

                return std::make_pair(latency_ms, success);
            }));
        }

        // 收集当前批次的结果
        for (auto& future : futures) {
            auto result = future.get();
            double latency_ms = result.first;
            bool success = result.second;

            if (success) {
                std::lock_guard<std::mutex> lock(latencies_mutex);
                all_latencies.push_back(latency_ms);
            } else {
                error_count++;
            }

            completed_requests++;
        }

        // 更新进度
        if (batch % 5 == 0 || batch == batches - 1) {
            printProgress("StartMove_" + motion_mode + "_Concurrent", completed_requests, total_requests);
        }

        // 批次间稍微休息，避免过度压力
        if (batch < batches - 1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    auto end_time = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats = calculateStats(all_latencies);
    stats.start_time = start_time;
    stats.end_time = end_time;
    stats.error_count = error_count.load();
    stats.error_rate = static_cast<double>(stats.error_count) / total_requests;

    std::cout << "[INFO] StartMove单客户端并发测试完成: 成功请求数=" << all_latencies.size()
              << ", 错误数=" << error_count.load() << std::endl;

    return stats;
}

// 测试GetTriggerInfo接口的延迟性能
PerformanceStats
GrpcCommunicationPerformanceTest::measureGetTriggerInfoLatency(int messages_per_stream, int duration_seconds) {
    std::vector<double> message_latencies;
    std::atomic<int> total_messages{0};
    std::atomic<int> error_count{0};
    std::mutex latencies_mutex;

    auto test_start = std::chrono::steady_clock::now();
    auto test_end_time = test_start + std::chrono::seconds(duration_seconds);

    // 创建独立的客户端连接
    auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
    auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

    grpc::ClientContext context;
    context.AddMetadata("client-type", "TRIGGER_PERFORMANCE_TEST");

    // 建立流连接
    auto stream_start = std::chrono::high_resolution_clock::now();
    auto stream = client_stub->GetTriggerInfo(&context);
    auto stream_establish_time = std::chrono::high_resolution_clock::now();

    double stream_setup_time_ms = std::chrono::duration<double, std::milli>(
            stream_establish_time - stream_start).count();

    std::cout << "[INFO] 流建立时间: " << std::fixed << std::setprecision(1)
              << stream_setup_time_ms << "ms" << std::endl;

    // 进入消息接收循环
    motor_control::TriggerInfoStatus response;
    int messages_received = 0;

    while (std::chrono::steady_clock::now() < test_end_time &&
           messages_received < messages_per_stream) {

        auto read_start = std::chrono::high_resolution_clock::now();

        if (stream->Read(&response)) {
            auto read_end = std::chrono::high_resolution_clock::now();

            // 计算消息接收延迟
            double latency_ms = std::chrono::duration<double, std::milli>(
                    read_end - read_start).count();

            {
                std::lock_guard<std::mutex> lock(latencies_mutex);
                message_latencies.push_back(latency_ms);
            }

            messages_received++;
            total_messages++;

            // 定期更新进度
            if (messages_received % 50 == 0) {
                std::cout << "[PROGRESS] 已接收消息: " << messages_received << std::endl;
            }

        } else {
            // 读取失败
            error_count++;
            std::cerr << "[ERROR] GetTriggerInfo 读取消息失败" << std::endl;
            break;
        }
    }

    // 关闭流
    stream->WritesDone();

    // 继续读取剩余数据
    while (stream->Read(&response)) {
        total_messages++;
    }

    auto finish_status = stream->Finish();
    if (!finish_status.ok()) {
        error_count++;
        std::cerr << "[ERROR] GetTriggerInfo 流结束时出错: "
                  << finish_status.error_message() << std::endl;
    }

    auto test_end = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats;
    {
        std::lock_guard<std::mutex> lock(latencies_mutex);
        stats = calculateStats(message_latencies);
    }

    stats.start_time = test_start;
    stats.end_time = test_end;
    stats.error_count = error_count.load();
    stats.total_requests = total_messages.load();
    stats.error_rate = stats.total_requests > 0 ?
                       static_cast<double>(stats.error_count) / stats.total_requests : 0.0;

    // 将流建立时间存储在min字段中
    stats.min = stream_setup_time_ms;

    std::cout << "[INFO] GetTriggerInfo测试完成: 接收消息数=" << total_messages.load()
              << ", 错误数=" << error_count.load() << std::endl;

    return stats;
}

// 测试GetSystemStatusInfo接口的并发性能
PerformanceStats GrpcCommunicationPerformanceTest::measureGetSystemStatusInfoConcurrency(int concurrent_clients,
                                                                                         int requests_per_client) {
    std::vector<double> all_latencies;
    std::atomic<int> completed_requests{0};
    std::atomic<int> error_count{0};
    std::mutex latencies_mutex;

    auto start_time = std::chrono::steady_clock::now();

    // 创建并发任务
    std::vector<std::future<std::vector<double>>> futures;

    for (int client = 0; client < concurrent_clients; ++client) {
        futures.push_back(std::async(std::launch::async, [=, &completed_requests, &error_count]() {
            std::vector<double> client_latencies;

            // 为每个客户端创建独立的连接
            auto channel = grpc::CreateChannel(server_address_, grpc::InsecureChannelCredentials());
            auto client_stub = motor_control::BedMasterAppService::NewStub(channel);

            for (int i = 0; i < requests_per_client; ++i) {
                auto request_start = std::chrono::high_resolution_clock::now();

                // 构造GetSystemStatusInfo请求
                grpc::ClientContext context;
                context.AddMetadata("client-type", "CONCURRENCY_TEST");
                context.set_deadline(std::chrono::system_clock::now() + std::chrono::seconds(2));

                motor_control::CommonDescription request;
                motor_control::SystemStatusInfoStatus response;

                request.set_contextuid("concurrent-" + std::to_string(client) + "-" + std::to_string(i) + "-" +
                                       std::to_string(std::chrono::steady_clock::now().time_since_epoch().count()));

                bool success = false;
                try {
                    grpc::Status status = client_stub->GetSystemStatusInfo(&context, request, &response);
                    success = status.ok();
                } catch (const std::exception &e) {
                    error_count++;
                }

                auto request_end = std::chrono::high_resolution_clock::now();

                if (success) {
                    double latency_ms = std::chrono::duration<double, std::milli>(
                            request_end - request_start).count();
                    client_latencies.push_back(latency_ms);
                } else {
                    error_count++;
                }

                completed_requests++;

                // 更新进度（减少频率以避免过多输出）
                if (completed_requests % 20 == 0) {
                    int total_requests = concurrent_clients * requests_per_client;
                    printProgress("GetSystemStatusInfo_Concurrent", completed_requests, total_requests);
                }
            }

            return client_latencies;
        }));
    }

    // 收集所有客户端的结果
    for (auto &future: futures) {
        auto client_latencies = future.get();
        std::lock_guard<std::mutex> lock(latencies_mutex);
        all_latencies.insert(all_latencies.end(), client_latencies.begin(), client_latencies.end());
    }

    auto end_time = std::chrono::steady_clock::now();

    // 计算统计结果
    PerformanceStats stats = calculateStats(all_latencies);
    stats.start_time = start_time;
    stats.end_time = end_time;
    stats.error_count = error_count.load();
    stats.error_rate = static_cast<double>(stats.error_count) / (concurrent_clients * requests_per_client);

    std::cout << "[INFO] GetSystemStatusInfo并发测试完成: 客户端数=" << concurrent_clients
              << ", 总请求数=" << stats.total_requests
              << ", 错误数=" << error_count.load() << std::endl;

    return stats;
}
// 保存所有测试结果到CSV
void GrpcCommunicationPerformanceTest::saveAllResultsToCsv() {
    // CSV文件已在各个测试中逐步写入，这里不需要额外操作
}

// 打印最终摘要报告
void GrpcCommunicationPerformanceTest::printFinalSummaryReport() {
    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "BedMaster GRPC通信性能测试摘要报告" << std::endl;
    std::cout << std::string(60, '=') << std::endl;
    std::time_t localTime{};
    std::cout << "测试时间: " << std::put_time(std::localtime(&localTime), "%Y-%m-%d %H:%M:%S") << std::endl;
    std::cout << "总测试项目: " << all_test_results_.size() << std::endl;

    // 生成文本摘要报告
    std::ofstream report("performance_summary.txt");
    report << "=== BedMaster GRPC通信性能测试报告 ===" << std::endl;
    report << "测试时间: " << std::put_time(std::localtime(&localTime), "%Y-%m-%d %H:%M:%S") << std::endl;
    report << std::endl;

    // 文档场景测试结果
    std::cout << "\n== 文档场景测试结果 ==" << std::endl;
    report << "== 文档场景测试结果 ==" << std::endl;

    // StartMove专项测试结果
    std::cout << "\n--- StartMove专项测试 ---" << std::endl;
    report << "\n--- StartMove专项测试 ---" << std::endl;

    for (const auto &result: all_test_results_) {
        if (result.first.find("StartMove_") != std::string::npos) {
            const auto &stats = result.second;
            bool passed = stats.p95 <= 50.0 && stats.error_rate <= 0.001;
            std::string status = passed ? "✓" : "⚠";

            std::cout << status << " " << result.first << ": P95="
                      << std::fixed << std::setprecision(1) << stats.p95
                      << "ms, 背景负载=" << stats.background_load_clients << "个客户端" << std::endl;

            report << status << " " << result.first << ": P95="
                   << std::fixed << std::setprecision(1) << stats.p95
                   << "ms, 背景负载=" << stats.background_load_clients << "个客户端" << std::endl;
        }
    }

    // GetTriggerInfo专项测试结果
    std::cout << "\n--- GetTriggerInfo专项测试 ---" << std::endl;
    report << "\n--- GetTriggerInfo专项测试 ---" << std::endl;

    for (const auto &result: all_test_results_) {
        if (result.first.find("GetTriggerInfo_") != std::string::npos) {
            const auto &stats = result.second;
            bool passed = stats.p95 <= 30.0 && stats.error_rate <= 0.001;
            std::string status = passed ? "✓" : "⚠";

            std::cout << status << " " << result.first << ": P95="
                      << std::fixed << std::setprecision(1) << stats.p95
                      << "ms, 消息数=" << stats.total_requests << std::endl;

            report << status << " " << result.first << ": P95="
                   << std::fixed << std::setprecision(1) << stats.p95
                   << "ms, 消息数=" << stats.total_requests << std::endl;
        }
    }

    // GetSystemStatusInfo并发测试结果
    std::cout << "\n--- GetSystemStatusInfo并发测试 ---" << std::endl;
    report << "\n--- GetSystemStatusInfo并发测试 ---" << std::endl;

    for (const auto &result: all_test_results_) {
        if (result.first.find("GetSystemStatusInfo_Concurrent") != std::string::npos) {
            const auto &stats = result.second;
            bool passed = stats.p95 <= 20.0 && stats.error_rate <= 0.001;
            std::string status = passed ? "✓" : "⚠";

            // 从测试名称中提取并发客户端数
            size_t pos = result.first.find("Concurrent");
            std::string client_info = pos != std::string::npos ? result.first.substr(pos) : "unknown";

            std::cout << status << " " << client_info << ": P95="
                      << std::fixed << std::setprecision(1) << stats.p95
                      << "ms, 总请求数=" << stats.total_requests << std::endl;

            report << status << " " << client_info << ": P95="
                   << std::fixed << std::setprecision(1) << stats.p95
                   << "ms, 总请求数=" << stats.total_requests << std::endl;
        }
    }

    // 单次接口延迟测试结果
    std::cout << "\n== 单次接口延迟测试结果 ==" << std::endl;
    report << "== 单次接口延迟测试结果 ==" << std::endl;

    std::map<std::string, double> interface_targets = {
            {"StartMove",           50.0},
            {"StopMove",            50.0},
            {"HeartBeatCheck",      20.0},
            {"GetSystemStatusInfo", 100.0},
            {"GainControl",         75.0},
            {"ReleaseControl",      75.0},
            {"GetPostId",           50.0}
    };

    for (const auto &target: interface_targets) {
        const std::string &interface = target.first;
        double p95_target = target.second;

        // 查找单客户端测试结果
        std::string key = interface + "_Latency_1";
        if (all_test_results_.find(key) != all_test_results_.end()) {
            const auto &stats = all_test_results_[key];
            bool passed = stats.p95 <= p95_target && stats.error_rate <= 0.001;

            std::string status = passed ? "✓" : "⚠";
            std::cout << status << " " << interface << ": P95="
                      << std::fixed << std::setprecision(1) << stats.p95
                      << "ms (目标<" << p95_target << "ms)" << std::endl;

            report << status << " " << interface << ": P95="
                   << std::fixed << std::setprecision(1) << stats.p95
                   << "ms (目标<" << p95_target << "ms)" << std::endl;
        }
    }

    // 流式接口测试结果
    std::cout << "\n== 流式接口测试结果 ==" << std::endl;
    report << std::endl << "== 流式接口测试结果 ==" << std::endl;

    for (const auto &result: all_test_results_) {
        if (result.first.find("StreamingLatency") != std::string::npos) {
            const auto &stats = result.second;
            bool passed = stats.min < 100.0 && stats.p95 < 50.0 && stats.error_rate <= 0.001;
            std::string status = passed ? "✓" : "⚠";

            // 从测试名称中提取流数量
            size_t pos = result.first.find("_");
            std::string stream_info = pos != std::string::npos ? result.first.substr(pos + 1) : "unknown";

            std::cout << status << " " << stream_info << ": 流建立="
                      << std::fixed << std::setprecision(1) << stats.min
                      << "ms, 消息延迟P95=" << stats.p95 << "ms" << std::endl;

            report << status << " " << stream_info << ": 流建立="
                   << std::fixed << std::setprecision(1) << stats.min
                   << "ms, 消息延迟P95=" << stats.p95 << "ms" << std::endl;
        }
    }

    // 总体评估
    int total_tests = 0;
    int passed_tests = 0;

    for (const auto &result: all_test_results_) {
        total_tests++;
        // 简单的通过标准：错误率低且延迟合理
        if (result.second.error_rate <= 0.001 && result.second.p95 < 200.0) {
            passed_tests++;
        }
    }

    double pass_rate = static_cast<double>(passed_tests) / total_tests * 100.0;

    std::cout << "\n== 总体评估 ==" << std::endl;
    report << std::endl << "== 总体评估 ==" << std::endl;

    std::cout << "测试通过率: " << std::fixed << std::setprecision(1) << pass_rate << "% ("
              << passed_tests << "/" << total_tests << ")" << std::endl;
    report << "测试通过率: " << std::fixed << std::setprecision(1) << pass_rate << "% ("
           << passed_tests << "/" << total_tests << ")" << std::endl;

    if (pass_rate >= 90.0) {
        std::cout << "✓ 通信性能优秀，满足设计要求" << std::endl;
        report << "✓ 通信性能优秀，满足设计要求" << std::endl;
    } else if (pass_rate >= 80.0) {
        std::cout << "✓ 通信性能良好，基本满足要求" << std::endl;
        report << "✓ 通信性能良好，基本满足要求" << std::endl;
    } else {
        std::cout << "⚠ 通信性能需要优化" << std::endl;
        report << "⚠ 通信性能需要优化" << std::endl;
    }

    report.close();
}

// ==================== 测试用例实现 ====================

// 1. 一元grpc接口串行性能测试
TEST_F(GrpcCommunicationPerformanceTest, SingleInterfaceLatencyTest) {
    std::cout << "\n[INFO] 开始单次接口交互耗时测试..." << std::endl;

    // 测试参数
    const int base_request_count = 50;
    const std::vector<int> concurrent_clients = {1};
     const std::vector<std::string> interfaces = {
         "StartMove", "StopMove", "GetSystemStatusInfo",
         "GainControl", "ReleaseControl", "GetPostId"
     };

    // 性能目标定义
    std::map<std::string, double> p95_targets = {
            {"StartMove",           50.0},
            {"StopMove",            50.0},
            {"HeartBeatCheck",      20.0},
            {"GetSystemStatusInfo", 100.0},
            {"GainControl",         75.0},
            {"ReleaseControl",      75.0},
            {"GetPostId",           50.0}
    };

    for (const auto &interface: interfaces) {
        std::cout << "\n[INFO] 测试接口: " << interface << std::endl;

        for (int clients: concurrent_clients) {
            std::cout << "[INFO] 并发客户端数: " << clients << ", 请求数: " << base_request_count << std::endl;

            auto stats = measureInterfaceLatency(interface, base_request_count * clients);

            // 保存结果
            std::string test_name = interface + "_Latency";
            saveResultsToCsv(test_name, stats, clients);
            all_test_results_[test_name + "_" + std::to_string(clients)] = stats;

            // 打印结果
            std::cout << "[RESULT] " << interface << " (客户端:" << clients << "): "
                      << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "ms, "
                      << "P95=" << stats.p95 << "ms, "
                      << "P99=" << stats.p99 << "ms, "
                      << "min=" << stats.min << "ms, "
                      << "max=" << stats.max << "ms, "
                      << "average=" << stats.avg << "ms, "
                      << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

            // 检查是否满足性能目标
            if (stats.p95 <= p95_targets[interface]) {
                std::cout << "[✓] 性能目标达成 (P95 <= " << p95_targets[interface] << "ms)" << std::endl;
            } else {
                std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_targets[interface] << "ms)" << std::endl;
            }

            // 验证基本性能要求
            EXPECT_LE(stats.error_rate, 0.001) << interface << " 错误率过高";
            EXPECT_GT(stats.p50, 0.0) << interface << " P50延迟异常";
            EXPECT_LT(stats.p99, 1000.0) << interface << " P99延迟过高";
            EXPECT_LE(stats.avg, 5.0) << interface << "平均延迟过高";
        }
    }

    std::cout << "\n[INFO] 单次接口交互耗时测试完成" << std::endl;
}

// 2. 流式接口性能测试（无背景负载）
TEST_F(GrpcCommunicationPerformanceTest, StreamingLatencyTest) {
    std::cout << "\n[INFO] 开始流式接口延时测试..." << std::endl;

    const std::vector<int> concurrent_streams = {1};
    const int messages_per_stream = 5000;
    const int test_duration_seconds = 5;

    for (int stream_count: concurrent_streams) {
        std::cout << "\n[INFO] 测试并发流数量: " << stream_count << std::endl;

        auto stream_stats = testStreamPerformance(stream_count, messages_per_stream, test_duration_seconds);

        // 保存结果
        std::string test_name = "StreamingLatency_" + std::to_string(stream_count) + "streams";
        saveResultsToCsv(test_name, stream_stats, stream_count);
        all_test_results_[test_name] = stream_stats;

        std::cout << "[RESULT] 并发流(" << stream_count << "): "
                  << "流建立=" << std::fixed << std::setprecision(1) << stream_stats.min << "ms, "
                  << "消息延迟P50=" << stream_stats.p50 << "ms, "
                  << "消息延迟P95=" << stream_stats.p95 << "ms, "
                  << "错误率=" << std::setprecision(2) << (stream_stats.error_rate * 100) << "%" << std::endl;

        // 验证流式接口性能要求
        EXPECT_LT(stream_stats.min, 100.0) << "流建立时间过长";
        EXPECT_LT(stream_stats.p95, 50.0) << "消息传输延迟过高";
        EXPECT_LE(stream_stats.error_rate, 0.001) << "流式接口错误率过高";

        // 检查性能目标
        if (stream_stats.min < 100.0 && stream_stats.p95 < 10.0) {
            std::cout << "[✓] 流式接口性能优秀" << std::endl;
        } else if (stream_stats.min < 100.0 && stream_stats.p95 < 50.0) {
            std::cout << "[✓] 流式接口性能良好" << std::endl;
        } else {
            std::cout << "[⚠] 流式接口性能需要优化" << std::endl;
        }
    }

    std::cout << "\n[INFO] 流式接口延时测试完成" << std::endl;
}

// 3. StartMove接口专项性能测试（单客户端异步并发，有背景负载）
TEST_F(GrpcCommunicationPerformanceTest, StartMovePerformanceTest) {
    std::cout << "\n[INFO] 开始StartMove接口专项性能测试（单客户端异步并发）..." << std::endl;

    // 测试参数
    const int concurrent_requests = 3;  // 并发请求数
    const int total_requests = 3;      // 总请求数
    const std::vector<int> background_client_counts = {0, 1, 3, 5}; // 包括无背景负载的基准测试
//    const std::vector<std::string> motion_modes = {"PositionMode", "VelocityMode", "HomingMode"};
    const std::vector<std::string> motion_modes = {"VelocityMode"};

    // 性能目标：在背景负载下P95 < 50ms
    const double p95_target_ms = 50.0;
    const double p99_target_ms = 100.0;

    for (int bg_clients: background_client_counts) {
        for (const auto &motion_mode: motion_modes) {
            std::cout << "\n[INFO] 测试配置: 背景负载客户端=" << bg_clients
                      << ", 运动模式=" << motion_mode
                      << ", 并发请求数=" << concurrent_requests
                      << ", 总请求数=" << total_requests << std::endl;

            // 启动背景负载（如果需要）
            if (bg_clients > 0) {
                background_load_simulator_->startBackgroundLoad(bg_clients, 10); // 10ms频率
                std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待背景负载稳定
            }

            // 执行StartMove并发性能测试
            auto stats = measureStartMoveConcurrentLatency(motion_mode, concurrent_requests, total_requests);

            // 停止背景负载
            if (bg_clients > 0) {
                background_load_simulator_->stopBackgroundLoad();
            }

            // 设置测试结果的额外信息
            stats.background_load_clients = bg_clients;
            stats.motion_mode = motion_mode;
            stats.detailed_timing = getCurrentDetailedTiming();

            // 保存结果
            std::string test_name = "StartMove_" + motion_mode + "_Concurrent_BG" + std::to_string(bg_clients);
            saveResultsToCsv(test_name, stats, 1);
            all_test_results_[test_name] = stats;

            // 打印结果
            std::cout << "[RESULT] " << test_name << ": "
                      << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "ms, "
                      << "P95=" << stats.p95 << "ms, "
                      << "P99=" << stats.p99 << "ms, "
                      << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%, "
                      << "QPS=" << std::setprecision(1) << (stats.getDurationSeconds() > 0 ? stats.total_requests / stats.getDurationSeconds() : 0) << std::endl;

            // 检查性能目标
            if (stats.p95 <= p95_target_ms) {
                std::cout << "[✓] 性能目标达成 (P95 <= " << p95_target_ms << "ms)" << std::endl;
            } else {
                std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_target_ms << "ms)" << std::endl;
            }

            // 验证基本性能要求
            EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
            EXPECT_GT(stats.p50, 0.0) << test_name << " P50延迟异常";
            EXPECT_LT(stats.p95, p95_target_ms) << test_name << " P50延迟异常";
            EXPECT_LT(stats.p99, p99_target_ms) << test_name << " P99延迟过高";
        }
    }

    std::cout << "\n[INFO] StartMove接口专项性能测试（单客户端异步并发）完成" << std::endl;
}

// 4. GetTriggerInfo接口专项性能测试（有背景负载）
TEST_F(GrpcCommunicationPerformanceTest, GetTriggerInfoPerformanceTest) {
    std::cout << "\n[INFO] 开始GetTriggerInfo接口专项性能测试..." << std::endl;

    // 测试参数
    const int messages_per_stream = 200;
    const int test_duration_seconds = 10;
    const std::vector<int> background_client_counts = {1, 3, 5};

    // 性能目标：P95 < 30ms
    const double p95_target_ms = 30.0;

    for (int bg_clients: background_client_counts) {
        std::cout << "\n[INFO] 测试配置: 背景负载客户端=" << bg_clients << std::endl;

        // 启动背景负载（如果需要）
        if (bg_clients > 0) {
            background_load_simulator_->startBackgroundLoad(bg_clients, 10); // 10ms频率
            std::this_thread::sleep_for(std::chrono::milliseconds(500)); // 等待背景负载稳定
        }

        // 执行GetTriggerInfo性能测试
        auto stats = measureGetTriggerInfoLatency(messages_per_stream, test_duration_seconds);

        // 停止背景负载
        if (bg_clients > 0) {
            background_load_simulator_->stopBackgroundLoad();
        }

        // 设置测试结果的额外信息
        stats.background_load_clients = bg_clients;
        stats.motion_mode = "TriggerInfo";

        // 保存结果
        std::string test_name = "GetTriggerInfo_BG" + std::to_string(bg_clients);
        saveResultsToCsv(test_name, stats, 1);
        all_test_results_[test_name] = stats;

        // 打印结果
        std::cout << "[RESULT] " << test_name << ": "
                  << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "ms, "
                  << "P95=" << stats.p95 << "ms, "
                  << "P99=" << stats.p99 << "ms, "
                  << "消息数=" << stats.total_requests << ", "
                  << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

        // 检查性能目标
        if (stats.p95 <= p95_target_ms) {
            std::cout << "[✓] 性能目标达成 (P95 <= " << p95_target_ms << "ms)" << std::endl;
        } else {
            std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_target_ms << "ms)" << std::endl;
        }

        // 验证基本性能要求
        EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
        EXPECT_GT(stats.total_requests, 0) << test_name << " 未接收到消息";
        EXPECT_LT(stats.p99, 100.0) << test_name << " P99延迟过高";
    }

    std::cout << "\n[INFO] GetTriggerInfo接口专项性能测试完成" << std::endl;
}

// 5. GetSystemStatusInfo接口并发性能测试
TEST_F(GrpcCommunicationPerformanceTest, GetSystemStatusInfoConcurrencyTest) {
    std::cout << "\n[INFO] 开始GetSystemStatusInfo接口并发性能测试..." << std::endl;

    // 测试参数
    const int requests_per_client = 1;
    const std::vector<int> concurrent_client_counts = {1, 2, 3, 5}; // 多客户端多请求

    // 性能目标：P95 < 20ms
    const double p95_target_ms = 20.0;

    for (int client_count: concurrent_client_counts) {
        std::cout << "\n[INFO] 测试配置: 并发客户端数=" << client_count << std::endl;

        // 执行并发性能测试
        auto stats = measureGetSystemStatusInfoConcurrency(client_count, requests_per_client);

        // 设置测试结果的额外信息
        stats.background_load_clients = 0; // 这个测试本身就是并发测试
        stats.motion_mode = "SystemStatusInfo";

        // 保存结果
        std::string test_name = "GetSystemStatusInfo_Concurrent" + std::to_string(client_count);
        saveResultsToCsv(test_name, stats, client_count);
        all_test_results_[test_name] = stats;

        // 打印结果
        std::cout << "[RESULT] " << test_name << ": "
                  << "P50=" << std::fixed << std::setprecision(1) << stats.p50 << "ms, "
                  << "P95=" << stats.p95 << "ms, "
                  << "P99=" << stats.p99 << "ms, "
                  << "总请求数=" << stats.total_requests << ", "
                  << "错误率=" << std::setprecision(2) << (stats.error_rate * 100) << "%" << std::endl;

        // 计算QPS
        double duration_sec = stats.getDurationSeconds();
        if (duration_sec > 0) {
            double qps = static_cast<double>(stats.total_requests) / duration_sec;
            std::cout << "[INFO] QPS: " << std::setprecision(1) << qps << std::endl;
        }

        // 检查性能目标
        if (stats.p95 <= p95_target_ms) {
            std::cout << "[✓] 性能目标达成 (P95 <= " << p95_target_ms << "ms)" << std::endl;
        } else {
            std::cout << "[⚠] 性能目标未达成 (P95 > " << p95_target_ms << "ms)" << std::endl;
        }

        // 验证基本性能要求
        EXPECT_LE(stats.error_rate, 0.001) << test_name << " 错误率过高";
        EXPECT_GT(stats.p50, 0.0) << test_name << " P50延迟异常";
        EXPECT_LT(stats.p99, 100.0) << test_name << " P99延迟过高";

        // 验证并发扩展性（简化处理）
        if (client_count > 1) {
            EXPECT_LT(stats.p95, p95_target_ms * 3.0) << test_name << " 并发扩展性较差";
        }
    }

    std::cout << "\n[INFO] GetSystemStatusInfo接口并发性能测试完成" << std::endl;
}


// 主函数
int main(int argc, char **argv) {
    ::testing::InitGoogleTest(&argc, argv);

    std::cout << "BedMaster GRPC通信性能测试" << std::endl;
    std::cout << "=========================" << std::endl;

    // 清理之前的结果文件
    std::remove("performance_results.csv");
    std::remove("performance_results_table.csv");
    std::remove("performance_summary.txt");

    return RUN_ALL_TESTS();
}
