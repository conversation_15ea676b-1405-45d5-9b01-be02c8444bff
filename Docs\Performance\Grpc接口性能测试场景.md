# **Grpc接口性能测试场景**

## StartMove

- **基本场景**
  - 多客户端（最多5个）以10ms频率持续获取系统状态过程中，测试该接口延迟


- **重点测试项**
    - 三种模式下ServoControlManger检测到运动完成至客户端接收到返回信息这段时间的耗时


- **并发场景**：
  - 单客户端多个**并发请求**，并非多客户端多个请求


## GetTriggerInfo

- **基本场景**
  - 多客户端（最多5个）以10ms频率持续获取系统状态过程中，测试该接口延迟


- **重点测试项**
    - ServoControlManger检测到运动完成至客户端接收到返回信息这段时间的耗时


- **并发场景**：
    - 无

## GetSystemStatusInfo

- **基本场景**
    - 测试该接口的并发延迟


- **重点测试项**
    - 基于StartMove、GetSystemStatusInfo触发的场景下测试该接口延迟


- **并发场景**：
    - 多客户端多请求

## 其他接口

- **基本场景**
    - 测试接口延迟


- **重点测试项**
    - 无


- **并发场景**：
    - 无
