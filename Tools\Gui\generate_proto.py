#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import subprocess
import sys
import shutil

def generate_proto_files():
    """
    生成proto文件对应的Python文件
    """
    print("开始生成proto文件...")
    
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查proto目录是否存在
    proto_dir = os.path.join(current_dir, "proto")
    if not os.path.exists(proto_dir):
        os.makedirs(proto_dir)
    
    # proto文件路径
    proto_file_path = os.path.join(proto_dir, "motor_control.proto")
    
    # 源proto文件路径
    source_proto_path = os.path.join(current_dir, "..", "..", "Common", "Protobuf", "ProtobufSource", "motor_control.proto")
    
    # 复制最新的proto文件
    if os.path.exists(source_proto_path):
        print(f"正在复制最新的proto文件...")
        shutil.copy2(source_proto_path, proto_file_path)
        print(f"proto文件已复制到 {proto_file_path}")
    else:
        # 检查proto文件是否存在
        if not os.path.exists(proto_file_path):
            print(f"错误: proto文件不存在: {proto_file_path}")
            print(f"源文件 {source_proto_path} 也不存在")
            return False
    
    try:
        # 执行protoc命令生成Python文件
        cmd = [
            "python", "-m", "grpc_tools.protoc",
            f"--proto_path={proto_dir}",
            f"--python_out={proto_dir}",
            f"--grpc_python_out={proto_dir}",
            proto_file_path
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"生成proto文件失败: {result.stderr}")
            return False
        
        print("Proto文件生成成功!")
        print(f"生成的文件: motor_control_pb2.py 和 motor_control_pb2_grpc.py")
        return True
    
    except Exception as e:
        print(f"生成proto文件时发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 检查是否安装了必要的库
    try:
        import grpc
        from grpc_tools import protoc
    except ImportError:
        print("错误: 缺少必要的库。请先安装以下Python包:")
        print("  pip install grpcio grpcio-tools protobuf")
        sys.exit(1)
    
    # 生成proto文件
    if generate_proto_files():
        print("准备工作完成，现在可以运行 motor_control_gui.py 了")
    else:
        print("准备工作失败，请检查错误并重试") 